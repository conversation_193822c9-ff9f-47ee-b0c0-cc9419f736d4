/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding all the various device types.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum DeviceType implements ValueEnum {

    UNKNOWN(0),
    DESKTOP(1),
    FEATURE_PHONE(2),
    ANDROID(3),
    IPHONE(4),
    ANDROID_TAB(5),
    IPAD(6);

    private final int value;

    /**
     * Returns the {@link DeviceType} item for the given {@code value}, or {@code null},
     * if no {@link DeviceType} is associated with {@code value}.
     *
     * @param value
     *            the value whose associated {@link DeviceType} is to be returned
     * @return the {@link DeviceType} item for the given {@code value}, or {@code null},
     *         if no {@link DeviceType} is associated with {@code value}
     */
    public static DeviceType findBy(int value) {
        for (DeviceType deviceType : values()) {
            if (value == deviceType.value) {
                return deviceType;
            }
        }
        return DeviceType.UNKNOWN;
    }
}
