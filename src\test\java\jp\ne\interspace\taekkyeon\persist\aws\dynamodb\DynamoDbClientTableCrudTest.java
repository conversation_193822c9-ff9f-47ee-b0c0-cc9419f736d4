/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.dynamodb;

import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.document.DynamoDB;
import com.amazonaws.services.dynamodbv2.document.Table;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.CreateTableResult;
import com.amazonaws.services.dynamodbv2.model.DeleteTableRequest;
import com.amazonaws.services.dynamodbv2.model.DeleteTableResult;
import com.amazonaws.services.dynamodbv2.model.DescribeTableRequest;
import com.amazonaws.services.dynamodbv2.model.DescribeTableResult;
import com.amazonaws.services.dynamodbv2.model.ListTablesRequest;
import com.amazonaws.services.dynamodbv2.model.ListTablesResult;
import com.amazonaws.services.dynamodbv2.model.UpdateTableRequest;
import com.amazonaws.services.dynamodbv2.model.UpdateTableResult;
import com.google.common.util.concurrent.RateLimiter;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.DynamoDbClient.MAX_RECOVERY_ATTEMPTS;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link DynamoDbClient} methods that execute CRUD operations on DynamoDB
 * tables.
 *
 * <AUTHOR> OBS DEV Team
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class DynamoDbClientTableCrudTest {

    private static final String DEFAULT_TEST_TABLE_NAME = "don't just blindly mimick; start thinking";

    @InjectMocks
    private DynamoDbClient underTest;

    @Mock
    private AmazonDynamoDB dynamoDb;

    @Mock
    private DynamoDB dynamoDbDocument;

    @Test(expected = IllegalArgumentException.class)
    public void testCreateTableShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        CreateTableRequest request = null;

        // when
        underTest.createTable(request);
    }

    @Test
    public void testCreateTableShouldReturnCreateTableResultWhenGivenCreateTableRequest() {
        // given
        CreateTableRequest request = new CreateTableRequest();
        CreateTableResult expected = new CreateTableResult();
        when(dynamoDb.createTable(request)).thenReturn(expected);

        // when
        CreateTableResult actual = underTest.createTable(request);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testListTablesShouldReturnListTablesResultWhenCalled() {
        // given
        ListTablesResult expected = new ListTablesResult();
        when(dynamoDb.listTables()).thenReturn(expected);

        // when
        ListTablesResult actual = underTest.listTables();

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testListTableShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        ListTablesRequest request = null;

        // when
        underTest.listTables(request);
    }

    @Test
    public void testListTablesShouldReturnListTablesResultWhenGivenListTablesRequest() {
        // given
        ListTablesRequest request = new ListTablesRequest();
        ListTablesResult expected = new ListTablesResult();
        when(dynamoDb.listTables(request)).thenReturn(expected);

        // when
        ListTablesResult actual = underTest.listTables(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDescribeTableShouldThrowIllegalArgumentExceptionWhenGivenTableNameIsNull() {
        // given
        String tableName = null;

        // when
        underTest.describeTable(tableName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDescribeTableShouldThrowIllegalArgumentExceptionWhenGivenTableNameIsEmpty() {
        // given
        String tableName = " ";

        // when
        underTest.describeTable(tableName);
    }

    @Test
    public void testDescribeTableShouldReturnDescribeTableResultWhenGivenTableName() {
        // given
        DescribeTableResult expected = new DescribeTableResult();
        when(dynamoDb.describeTable(DEFAULT_TEST_TABLE_NAME)).thenReturn(expected);

        // when
        DescribeTableResult actual = underTest.describeTable(DEFAULT_TEST_TABLE_NAME);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDescribeTableShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        DescribeTableRequest request = null;

        // when
        underTest.describeTable(request);
    }

    @Test
    public void testDescribeTableShouldReturnDescribeTableResultWhenGivenDescribeTableRequest() {
        // given
        DescribeTableRequest request = new DescribeTableRequest();
        DescribeTableResult expected = new DescribeTableResult();
        when(dynamoDb.describeTable(request)).thenReturn(expected);

        // when
        DescribeTableResult actual = underTest.describeTable(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUpdateTableShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        UpdateTableRequest request = null;

        // when
        underTest.updateTable(request);
    }

    @Test
    public void testUpdateTableShouldReturnUpdateTableResultWhenGivenUpdateTableRequest() {
        // given
        UpdateTableRequest request = new UpdateTableRequest();
        UpdateTableResult expected = new UpdateTableResult();
        when(dynamoDb.updateTable(request)).thenReturn(expected);

        // when
        UpdateTableResult actual = underTest.updateTable(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteTableShouldThrowIllegalArgumentExceptionWhenGivenTableNameIsNull() {
        // given
        String tableName = null;

        // when
        underTest.deleteTable(tableName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteTableShouldThrowIllegalArgumentExceptionWhenGivenTableNameIsEmpty() {
        // given
        String tableName = " ";

        // when
        underTest.deleteTable(tableName);
    }

    @Test
    public void testDeleteTableShouldReturnDeleteTableResultWhenGivenTableName() {
        // given
        DeleteTableResult expected = new DeleteTableResult();
        when(dynamoDb.deleteTable(DEFAULT_TEST_TABLE_NAME)).thenReturn(expected);

        // when
        DeleteTableResult actual = underTest.deleteTable(DEFAULT_TEST_TABLE_NAME);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteTableShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        DeleteTableRequest request = null;

        // when
        underTest.deleteTable(request);
    }

    @Test
    public void testDeleteTableShouldReturnDeleteTableResultWhenGivenDeleteTableRequest() {
        // given
        DeleteTableRequest request = new DeleteTableRequest();
        DeleteTableResult expected = new DeleteTableResult();
        when(dynamoDb.deleteTable(request)).thenReturn(expected);

        // when
        DeleteTableResult actual = underTest.deleteTable(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testWaitUntilAvailableForShouldThrowWhenGivenNullTableName() {
        // given
        RateLimiter interval = mock(RateLimiter.class);

        // when
        underTest.waitUntilAvailableFor(null, interval);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testWaitUntilAvailableForShouldThrowWhenGivenNullRateLimiter() {
        // given
        String tableName = "someTable";

        // when
        underTest.waitUntilAvailableFor(tableName, null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testWaitUntilAvailableForShouldThrowWhenGivenNullParameters() {
        // when
        underTest.waitUntilAvailableFor(null, null);
    }

    @Test
    public void testWaitUntilAvailableForShouldRetryToCallWaitForDeleteWhenItKeepsCrashing()
            throws InterruptedException {
        // given
        String tableName = "someTable";
        Table table = mock(Table.class);
        RateLimiter interval = mock(RateLimiter.class);

        when(dynamoDbDocument.getTable(tableName)).thenReturn(table);
        doThrow(Exception.class).when(table).waitForDelete();

        // when
        underTest.waitUntilAvailableFor(tableName, interval);

        // then
        verify(interval, times(MAX_RECOVERY_ATTEMPTS)).acquire();
        verify(table, times(MAX_RECOVERY_ATTEMPTS)).waitForDelete();
        verify(table, never()).waitForActive();
    }

    @Test
    public void testWaitUntilAvailableForShouldCallWaitForDeleteOnceWhenGivenTableIsDeletedOrBeingDeleted()
            throws InterruptedException {
        // given
        String tableName = "someTable";
        Table table = mock(Table.class);
        RateLimiter interval = mock(RateLimiter.class);

        when(dynamoDbDocument.getTable(tableName)).thenReturn(table);
        doNothing().when(table).waitForDelete();

        // when
        underTest.waitUntilAvailableFor(tableName, interval);

        // then
        verify(interval).acquire();
        verify(table).waitForDelete();
        verify(table, never()).waitForActive();
    }

    @Test
    public void testWaitUntilAvailableForShouldRetryToCallWaitForActiveWhenItKeepsCrashing()
            throws InterruptedException {
        // given
        String tableName = "someTable";
        Table table = mock(Table.class);
        RateLimiter interval = mock(RateLimiter.class);

        when(dynamoDbDocument.getTable(tableName)).thenReturn(table);
        doThrow(IllegalArgumentException.class).when(table).waitForDelete();
        when(table.waitForActive()).thenThrow(Exception.class);

        // when
        underTest.waitUntilAvailableFor(tableName, interval);

        // then
        verify(interval, times(MAX_RECOVERY_ATTEMPTS)).acquire();
        verify(table, times(MAX_RECOVERY_ATTEMPTS)).waitForDelete();
        verify(table, times(MAX_RECOVERY_ATTEMPTS)).waitForActive();
    }

    @Test
    public void testWaitUntilAvailableForShouldCallWaitForActiveOnceWhenTableIsActive()
            throws InterruptedException {
        // given
        String tableName = "someTable";
        Table table = mock(Table.class);
        RateLimiter interval = mock(RateLimiter.class);

        when(dynamoDbDocument.getTable(tableName)).thenReturn(table);
        doThrow(IllegalArgumentException.class).when(table).waitForDelete();

        // when
        underTest.waitUntilAvailableFor(tableName, interval);

        // then
        verify(interval).acquire();
        verify(table).waitForDelete();
        verify(table).waitForActive();
    }

}
