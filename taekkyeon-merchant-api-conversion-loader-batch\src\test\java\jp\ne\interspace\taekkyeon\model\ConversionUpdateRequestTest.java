/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding conversion update request test for test.
 *
 * <AUTHOR> <PERSON>
 */
@Getter @AllArgsConstructor
public class ConversionUpdateRequestTest {
    private final String fileName;
    private final int errorCount;
    private final long campaignId;
    private final String emailStaff;
    private final int dataCount;
}
