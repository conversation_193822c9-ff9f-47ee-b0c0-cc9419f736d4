/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.s3;

import java.util.Arrays;
import java.util.List;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AccessControlList;
import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.CreateBucketRequest;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link SimpleStorageServiceClient}'s bucket-related functionalities.
 *
 * <AUTHOR> OBS DEV Team
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SimpleStorageServiceClientBucketOperationTest {

    private static final String DEFAULT_TEST_BUCKET = "smile now cry later";

    private static final AccessControlList DEFAULT_TEST_ACCESS_CONTROL_LIST = new AccessControlList();

    private static final CannedAccessControlList DEFAULT_TEST_CANNED_ACCESS_CONTROL_LIST = CannedAccessControlList.PublicRead;

    @InjectMocks
    private SimpleStorageServiceClient underTest;

    @Mock
    private AmazonS3 s3;

    @Test(expected = IllegalArgumentException.class)
    public void testDoesBucketExistShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        String bucketName = null;

        // when
        underTest.doesBucketExist(bucketName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDoesBucketExistShouldThrowIllegalArgumentExceptionWhenGivenEmptyParameter() {
        // given
        String bucketName = "  ";

        // when
        underTest.doesBucketExist(bucketName);
    }

    @Test
    public void testDoesBucketExistShouldReturnBooleanWhenGivenValidParameter() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        boolean expected = true;
        when(s3.doesBucketExistV2(bucketName)).thenReturn(expected);

        // when
        boolean actual = underTest.doesBucketExist(bucketName);

        // then
        assertEquals(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCreateBucketShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        String bucketName = null;

        // when
        underTest.createBucket(bucketName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCreateBucketShouldThrowIllegalArgumentExceptionWhenGivenEmptyParameter() {
        // given
        String bucketName = "  ";

        // when
        underTest.createBucket(bucketName);
    }

    @Test
    public void testCreateBucketShouldReturnBucketWhenGivenVaildParameter() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        Bucket expected = new Bucket(DEFAULT_TEST_BUCKET);
        when(s3.createBucket(bucketName)).thenReturn(expected);

        // when
        Bucket actual = underTest.createBucket(bucketName);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCreateBucketShouldThrowIllegalArgumentExceptionWhenGivenCreateBucketRequestIsNull() {
        // given
        CreateBucketRequest request = null;

        // when
        underTest.createBucket(request);
    }

    @Test
    public void testCreateBucketShouldReturnBucketWhenGivenVaildCreateBucketRequest() {
        // given
        CreateBucketRequest request = new CreateBucketRequest(DEFAULT_TEST_BUCKET);
        Bucket expected = new Bucket(DEFAULT_TEST_BUCKET);
        when(s3.createBucket(request)).thenReturn(expected);

        // when
        Bucket actual = underTest.createBucket(request);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testListBucketsShouldReturnBucketListWhenCalled() {
        // given
        List<Bucket> expected = Arrays.asList(new Bucket("uno"), new Bucket("dos"),
                new Bucket("tres"));
        when(s3.listBuckets()).thenReturn(expected);

        // when
        List<Bucket> actual = underTest.listBuckets();

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetBucketAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        String bucketName = null;

        // when
        underTest.getBucketAccessControlList(bucketName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetBucketAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenEmptyParameter() {
        // given
        String bucketName = "   ";

        // when
        underTest.getBucketAccessControlList(bucketName);
    }

    @Test
    public void testGetBucketAccessControlListShouldReturnAccessControlListWhenGivenValidParameter() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        AccessControlList expected = DEFAULT_TEST_ACCESS_CONTROL_LIST;
        when(s3.getBucketAcl(bucketName)).thenReturn(expected);

        // when
        AccessControlList actual = underTest.getBucketAccessControlList(bucketName);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetBucketAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        String bucketName = null;

        // when
        underTest.setBucketAccessControlList(bucketName,
                DEFAULT_TEST_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetBucketAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenEmptyParameter() {
        // given
        String bucketName = " ";

        // when
        underTest.setBucketAccessControlList(bucketName,
                DEFAULT_TEST_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetBucketAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenAccessControlListIsNull() {
        // given
        AccessControlList accessControlList = null;

        // when
        underTest.setBucketAccessControlList(DEFAULT_TEST_BUCKET, accessControlList);
    }

    @Test
    public void testSetBucketAccessControlListShouldCallSetBucketAclOnceWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        AccessControlList accessControlList = DEFAULT_TEST_ACCESS_CONTROL_LIST;

        // when
        underTest.setBucketAccessControlList(bucketName, accessControlList);

        // then
        verify(s3).setBucketAcl(bucketName, accessControlList);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetBucketAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.setBucketAccessControlList(bucketName,
                DEFAULT_TEST_CANNED_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetBucketAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsEmpty() {
        // given
        String bucketName = " ";

        // when
        underTest.setBucketAccessControlList(bucketName,
                DEFAULT_TEST_CANNED_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetBucketAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenCannedAccessControlListIsNull() {
        // given
        CannedAccessControlList cannedAccessControlList = null;

        // when
        underTest.setBucketAccessControlList(DEFAULT_TEST_BUCKET,
                cannedAccessControlList);
    }

    @Test
    public void testSetBucketAccessControlListShouldCallSetBucketAclOnceWhenGivenBucketNameAndCannedAccessControlList() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        CannedAccessControlList cannedAccessControlList = CannedAccessControlList.Private;

        // when
        underTest.setBucketAccessControlList(bucketName, cannedAccessControlList);

        // then
        verify(s3).setBucketAcl(bucketName, cannedAccessControlList);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteBucketShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        String bucketName = null;

        // when
        underTest.deleteBucket(bucketName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteBucketShouldThrowIllegalArgumentExceptionWhenGivenEmptyParameter() {
        // given
        String bucketName = " ";

        // when
        underTest.deleteBucket(bucketName);
    }

    @Test
    public void testDeleteBucketShouldCallDeleteBucketOnceWhenGivenValidBucketName() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;

        // when
        underTest.deleteBucket(bucketName);

        // then
        verify(s3).deleteBucket(bucketName);
    }

}
