/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.junit;

import com.google.inject.Exposed;
import com.google.inject.Provides;

import jp.ne.interspace.taekkyeon.module.MariaDbResolver;

import static jp.ne.interspace.taekkyeon.module.DatabaseType.MARIADB;

/**
 * Taekkyeon module for JUnit HSQLDB data source.
 *
 * <AUTHOR>
 */
@MariaDbResolver
public class TaekkyeonHsqldbMariaDbJunitModule extends TaekkyeonHsqldbJunitModule {

    private static final String INTEGRATION_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH =
            "database/hsqldb/mariadb-integration-test-schema.sql";
    private static final String INTEGRATION_TEST_HSQLDB_DATA_SQL_FILE_PATH =
            "database/hsqldb/mariadb-integration-test-data.sql";
    private static final String UNIT_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH =
            "database/hsqldb/mariadb-schema.sql";
    private static final String UNIT_TEST_HSQLDB_DATA_SQL_FILE_PATH =
            "database/hsqldb/mariadb-test-data.sql";

    public TaekkyeonHsqldbMariaDbJunitModule() {
        super(MARIADB);
    }

    @Provides @Exposed @MariaDbResolver
    private HsqldbSqlFilePath provideHsqldbSqlFilePath() {
        return new HsqldbSqlFilePath(UNIT_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH,
                UNIT_TEST_HSQLDB_DATA_SQL_FILE_PATH,
                INTEGRATION_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH,
                INTEGRATION_TEST_HSQLDB_DATA_SQL_FILE_PATH);
    }
}
