/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.GlobalConversionStatusSynchronizationData;
import jp.ne.interspace.taekkyeon.model.SynchronizationData;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.SynchronizationDataMapper;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.ORACLE;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.REDSHIFT;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link SynchronizationDataService}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SynchronizationDataServiceTest {

    private static final String TABLE_NAME = "creative_access_log_summaries";
    private static final String TABLE_NAME_CONVERSION = "conversions";
    private static final String TABLE_NAME_CONVERSION_STATUS = "global_conversion_status";
    private static final String COLLECTION_NAME_CLICK_ANOMALY_DETECTION = "click_anomaly_detection";
    private static final String COUNTRY_CODE = "SG";
    private static final long CONVERSION_ID = 1;
    private static final String ZONED_ID = "Asia/Ho_Chi_Minh";

    private static final LocalDateTime LOCAL_DATE_TIME = LocalDateTime.of(2018, 10, 5, 10,
            23, 44, 309);
    private static final LocalDateTime INVALID_DATE_TIME = LocalDateTime.MIN;
    private static final LocalDateTime SYNC_START_TIME = LocalDateTime.of(2016, 8, 15, 20,
            43, 14, 109);
    private static final LocalDateTime SYNC_END_TIME = LocalDateTime.of(2017, 8, 15, 20,
            43);

    @InjectMocks @Spy
    private SynchronizationDataService underTest;

    @Mock
    private SynchronizationDataMapper syncMapper;

    @Mock
    private CountryService countryService;

    @Test
    public void testFindSyncStartTimeShouldReturnCorrectLocalDateTimeWhenGivenTableNameAndDatabaseName() {
        // given
        doReturn(LOCAL_DATE_TIME).when(underTest).findSyncStartTime(TABLE_NAME,
                ORACLE.toString(), null);

        // when
        LocalDateTime actual = underTest.findSyncStartTime(TABLE_NAME, ORACLE.toString());

        // then
        assertSame(LOCAL_DATE_TIME, actual);
    }

    @Test
    public void testFindSyncStartTimeShouldReturnCorrectLocalDateTimeWhenSyncStartTimeIsInvalidAndSynchronizationDataIsFound() {
        // given
        doReturn(INVALID_DATE_TIME).when(underTest).getSyncStartTime();
        SynchronizationData syncData = new SynchronizationData(LOCAL_DATE_TIME, 1L, 0L);
        when(syncMapper.findSynchronizationData(TABLE_NAME, ORACLE.toString(), null))
                .thenReturn(syncData);

        // when
        LocalDateTime actual = underTest.findSyncStartTime(TABLE_NAME, ORACLE.toString(),
                null);

        // then
        assertSame(LOCAL_DATE_TIME, actual);
    }

    @Test
    public void testFindSyncStartTimeShouldThrowTaekkyeonExceptionWhenSyncStartTimeIsInvalidAndSynchronizationDataIsNotFound() {
        // given
        doReturn(INVALID_DATE_TIME).when(underTest).getSyncStartTime();
        when(syncMapper.findSynchronizationData(TABLE_NAME, ORACLE.toString(), null))
                .thenReturn(null);

        // when
        try {
            underTest.findSyncStartTime(TABLE_NAME, ORACLE.toString(), null);
            fail();
        } catch (TaekkyeonException exeption) {
            // then
            assertEquals("Could not find sync start time.", exeption.getMessage());
        }
    }

    @Test
    public void testFindSyncStartTimeShouldReturnCorrectLocalDateTimeWhenSyncStartTimeIsNotInvalid() {
        // given
        doReturn(SYNC_START_TIME).when(underTest).getSyncStartTime();
        doReturn(LOCAL_DATE_TIME).when(underTest).convertLocalDateTime(SYNC_START_TIME,
                COUNTRY_CODE);

        // when
        LocalDateTime actual = underTest.findSyncStartTime(TABLE_NAME, ORACLE.toString(),
                COUNTRY_CODE);

        // then
        assertSame(LOCAL_DATE_TIME, actual);
    }

    @Test
    public void testFindSyncStartTimeShouldReturnCorrectLocalDateTimeWhenGivenTableName() {
        // given
        doReturn(LOCAL_DATE_TIME).when(underTest).findSyncStartTime(TABLE_NAME,
                ORACLE.toString());

        // when
        LocalDateTime actual = underTest.findSyncStartTime(TABLE_NAME);

        // then
        assertSame(LOCAL_DATE_TIME, actual);
    }

    @Test
    public void testFindSyncedConversionShouldReturnCorrectSynchronizationDataWhenSyncStartTimeIsInvalid() {
        // given
        doReturn(INVALID_DATE_TIME).when(underTest).getSyncStartTime();
        SynchronizationData synchronizationData = mock(SynchronizationData.class);
        when(syncMapper.findSynchronizationData(TABLE_NAME_CONVERSION,
                ORACLE.toString(), null)).thenReturn(synchronizationData);

        // when
        SynchronizationData actual = underTest.findSyncedConversion();

        // then
        assertSame(synchronizationData, actual);
    }

    @Test
    public void testFindSyncedConversionShouldReturnCorrectSynchronizationDataWhenSyncStartTimeIsNotInvalid() {
        // given
        doReturn(SYNC_START_TIME).when(underTest).getSyncStartTime();

        // when
        SynchronizationData actual = underTest.findSyncedConversion();

        // then
        assertNotNull(actual);
        assertSame(SYNC_START_TIME, actual.getSyncStartTime());
        assertEquals(0L, actual.getConversionId().longValue());
        assertEquals(0L, actual.getSiteId().longValue());
        verify(syncMapper, never()).findSynchronizationData(anyString(), anyString(),
                anyString());
    }

    @Test
    public void testFindDataShouldReturnCorrectSynchronizationDataWhenSyncStartTimeIsInvalid() {
        // given
        doReturn(INVALID_DATE_TIME).when(underTest).getSyncStartTime();
        SynchronizationData synchronizationData = mock(SynchronizationData.class);
        when(syncMapper.findSynchronizationData(TABLE_NAME, ORACLE.toString(),
                COUNTRY_CODE)).thenReturn(synchronizationData);

        // when
        SynchronizationData actual = underTest.findData(TABLE_NAME, ORACLE.toString(),
                COUNTRY_CODE);

        // then
        assertSame(synchronizationData, actual);
    }

    @Test
    public void testFindDataShouldReturnCorrectSynchronizationDataWhenSyncStartTimeIsNotInvalid() {
        // given
        doReturn(SYNC_START_TIME).when(underTest).getSyncStartTime();

        // when
        SynchronizationData actual = underTest.findData(TABLE_NAME, ORACLE.toString(),
                COUNTRY_CODE);

        // then
        assertNotNull(actual);
        assertSame(SYNC_START_TIME, actual.getSyncStartTime());
        assertEquals(0L, actual.getConversionId().longValue());
        assertEquals(0L, actual.getSiteId().longValue());
        verify(syncMapper, never()).findSynchronizationData(anyString(), anyString(),
                anyString());
    }

    @Test
    public void testUpdateSyncStartTimeShouldCallUpdateMethodWhenGivenTableName()
            throws Exception {
        // given
        doNothing().when(underTest).updateSyncStartTime(TABLE_NAME, ORACLE.toString());

        // when
        underTest.updateSyncStartTime(TABLE_NAME);

        // then
        verify(underTest).updateSyncStartTime(TABLE_NAME, ORACLE.toString());
    }

    @Test
    public void testUpdateSyncStartTimeShouldNotCallUpdateMethodWhenGivenTableNameAndDatabaseName()
            throws Exception {
        // given
        doNothing().when(underTest).updateSyncStartTime(TABLE_NAME, ORACLE.toString(),
                null);

        // when
        underTest.updateSyncStartTime(TABLE_NAME, ORACLE.toString());

        // then
        verify(underTest).updateSyncStartTime(TABLE_NAME, ORACLE.toString(), null);
    }

    @Test
    public void testUpdateSyncStartTimeShouldCallUpdateMethodWhenGivenTableNameAndDatabaseNameAndCountryCodeAndSyncStartTimeIsInvalid()
            throws Exception {
        // given
        doReturn(INVALID_DATE_TIME).when(underTest).getSyncStartTime();
        doReturn(SYNC_END_TIME).when(underTest).getSyncEndTime();

        // when
        underTest.updateSyncStartTime(TABLE_NAME, REDSHIFT.toString(), COUNTRY_CODE);

        // then
        verify(syncMapper).updateSyncStartTime(SYNC_END_TIME, TABLE_NAME,
                REDSHIFT.toString(), COUNTRY_CODE);
    }

    @Test
    public void testUpdateSyncStartTimeShouldNotCallUpdateMethodWhenGivenTableNameAndDatabaseNameAndCountryCodeAndSyncStartTimeIsNotInvalid()
            throws Exception {
        // given
        doReturn(SYNC_START_TIME).when(underTest).getSyncStartTime();

        // when
        underTest.updateSyncStartTime(TABLE_NAME, REDSHIFT.toString(), COUNTRY_CODE);

        // then
        verify(syncMapper, never()).updateSyncStartTime(any(LocalDateTime.class),
                anyString(), anyString(), anyString());
    }

    @Test
    public void testUpdateSyncStartTimeBySyncEndTimeShouldCallUpdateMethodWhenCalled()
            throws Exception {
        // when
        underTest.updateSyncStartTimeBySyncEndTime(
                COLLECTION_NAME_CLICK_ANOMALY_DETECTION, ORACLE.toString(), SYNC_END_TIME,
                COUNTRY_CODE);

        // then
        verify(syncMapper).updateSyncStartTime(SYNC_END_TIME,
                COLLECTION_NAME_CLICK_ANOMALY_DETECTION, ORACLE.toString(), COUNTRY_CODE);
    }

    @Test
    public void testUpdateSyncConversionShouldCallUpdateMethodWithCorrectArgumentsWhenSyncStartTimeIsInvalid() {
        // given
        doReturn(INVALID_DATE_TIME).when(underTest).getSyncStartTime();

        // when
        underTest.updateSyncConversion(CONVERSION_ID, SYNC_END_TIME);

        // then
        verify(syncMapper).updateSynchronizationData(CONVERSION_ID, SYNC_END_TIME,
                TABLE_NAME_CONVERSION, ORACLE.toString(), null);
    }

    @Test
    public void testUpdateSyncConversionShouldNotCallUpdateMethodWhenSyncStartTimeIsNotInvalid() {
        // given
        doReturn(SYNC_START_TIME).when(underTest).getSyncStartTime();

        // when
        underTest.updateSyncConversion(CONVERSION_ID, SYNC_END_TIME);

        // the
        verify(syncMapper, never()).updateSynchronizationData(anyLong(),
                any(LocalDateTime.class), anyString(), anyString(), anyString());
    }

    @Test
    public void testFindGlobalConversionStatusSynchronizationDataShouldReturnSynchronizationDataWhenCalled() {
        // given
        GlobalConversionStatusSynchronizationData synchronizationData = mock(
                GlobalConversionStatusSynchronizationData.class);
        when(syncMapper.findGlobalConversionStatusSynchronizationData(
                TABLE_NAME_CONVERSION_STATUS, COUNTRY_CODE))
                        .thenReturn(synchronizationData);

        // when
        GlobalConversionStatusSynchronizationData actual =
                underTest.findGlobalConversionStatusSynchronizationData(
                        TABLE_NAME_CONVERSION_STATUS, COUNTRY_CODE);

        // then
        assertSame(synchronizationData, actual);
    }

    @Test
    public void testUpsertGlobalConversionStatusShouldReturnOneWhenCalled() {
        // given
        GlobalConversionStatusSynchronizationData synchronizationData = mock(
                GlobalConversionStatusSynchronizationData.class);
        int expected = 1;
        when(syncMapper.upsertGlobalConversionStatus(synchronizationData, COUNTRY_CODE))
                .thenReturn(expected);

        // when
        int actual = underTest.upsertGlobalConversionStatus(synchronizationData,
                COUNTRY_CODE);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testDeleteSynchronizationDataByShouldReturnOneWhenCalled() {
        // given
        int expected = 1;
        when(syncMapper.delete(TABLE_NAME_CONVERSION_STATUS, ORACLE.toString(),
                COUNTRY_CODE)).thenReturn(expected);

        // when
        int actual = underTest.deleteSynchronizationData(TABLE_NAME_CONVERSION_STATUS,
                ORACLE.toString(), COUNTRY_CODE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testConvertLocalDateTimeByShouldReturnCorrectLocalDateTimeWhenCalled() {
        // given
        doReturn(LOCAL_DATE_TIME).when(underTest).convertLocalDateTime(SYNC_START_TIME,
                EMPTY);

        // when
        LocalDateTime actual = underTest.convertLocalDateTimeBy(SYNC_START_TIME);

        // then
        assertSame(LOCAL_DATE_TIME, actual);
    }

    @Test
    public void testConvertLocalDateTimeShouldReturnCorrectLocalDateTimeWhenCalled() {
        // given
        LocalDateTime localDateTime = LocalDateTime.of(2024, 8, 8, 11, 22, 33);
        LocalDateTime expected = LocalDateTime.of(2024, 8, 8, 18, 22, 33);
        doReturn(COUNTRY_CODE).when(underTest).getCountryCodeFrom(COUNTRY_CODE);
        when(countryService.findZoneIdBy(COUNTRY_CODE)).thenReturn(ZONED_ID);

        // when
        LocalDateTime actual = underTest.convertLocalDateTime(localDateTime,
                COUNTRY_CODE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testConvertUtcShouldReturnCorrectLocalDateTimeWhenCalled() {
        // given
        LocalDateTime localDateTime = LocalDateTime.of(2024, 8, 8, 11, 22, 33);
        LocalDateTime expected = LocalDateTime.of(2024, 8, 8, 4, 22, 33);
        doReturn(COUNTRY_CODE).when(underTest).getCountryCodeFrom(COUNTRY_CODE);
        when(countryService.findZoneIdBy(COUNTRY_CODE)).thenReturn(ZONED_ID);

        // when
        LocalDateTime actual = underTest.convertUtc(localDateTime, COUNTRY_CODE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetCountryCodeFromReturnCorrectCountryCodeWhenCountryCodeIsNull() {
        // given
        doReturn(COUNTRY_CODE).when(underTest).getCurrentCountryCode();

        // when
        String actual = underTest.getCountryCodeFrom(null);

        // then
        assertSame(COUNTRY_CODE, actual);
    }

    @Test
    public void testGetCountryCodeFromReturnCorrectCountryCodeWhenCountryCodeIsNotNullAndEmpty() {
        // given
        doReturn(COUNTRY_CODE).when(underTest).getCurrentCountryCode();

        // when
        String actual = underTest.getCountryCodeFrom(EMPTY);

        // then
        assertSame(COUNTRY_CODE, actual);
    }

    @Test
    public void testGetCountryCodeFromReturnCorrectCountryCodeWhenCountryCodeIsNotNullAndOneCharacter() {
        // given
        doReturn(COUNTRY_CODE).when(underTest).getCurrentCountryCode();

        // when
        String actual = underTest.getCountryCodeFrom("I");

        // then
        assertSame(COUNTRY_CODE, actual);
    }

    @Test
    public void testGetCountryCodeFromReturnCorrectCountryCodeWhenCountryCodeIsNotNullAndTwoCharacter() {
        // given
        String countryCode = "ID";

        // when
        String actual = underTest.getCountryCodeFrom(countryCode);

        // then
        assertSame(countryCode, actual);
        verify(underTest, never()).getCurrentCountryCode();
    }
}
