/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.YearMonth;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding closed month data.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public class ClosedMonth {

    private final YearMonth closedMonth;
}
