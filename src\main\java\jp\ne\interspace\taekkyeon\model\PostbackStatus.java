/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding postback statuses.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum PostbackStatus implements ValueEnum {

    NEEDED(0),
    NOT_NEEDED(1),
    ERROR(2),
    PROCESSING(3);

    private final int value;
}
