SET DATABASE SQL SYNTAX ORA TRUE;

DROP TABLE IF EXISTS MERCHANT_CAMPAIGN;
CREATE TABLE MERCHANT_CAMPAIGN (
	"CAMPAIGN_NO" NUMBER(10,0) NOT NULL,
	"ACCOUNT_NO" NUMBER(10,0) NOT NULL,
	"CA<PERSON><PERSON>IGN_STATE_ID" NUMBER(2,0) NOT NULL,
	"CREATIVE_SYNC_STATUS" NUMBER(1,0),
	"CAMPAIGN_NAME" VARCHAR2(512) NOT NULL,
	"IMAGE_URL" VARCHAR2(512),
	"URL" VARCHAR2(512) NOT NULL,
	"DESCRIPTION" VARCHAR2(4000),
	"CATEGORY1" NUMBER(10,0) NOT NULL,
	"CATEGORY2" NUMBER(10,0) NOT NULL,
	"CATEGORY3" NUMBER(10,0) NOT NULL,
	"AUTO_AFF_LIMITATION_OPTION" NUMBER(1,0) NOT NULL,
	"AUTO_AFF_LIMITATION_DIVISION" NUMBER(1,0) NOT NULL,
	"AFF_CONDITION_SPECIAL" VARCHAR2(2048),
	"RESULT_APPROVAL_SPECIAL" VARCHAR2(2000),
	"PR_FOR_PARTNER" VARCHAR2(4000),
	"DEVICE_TYPES" VARCHAR2(128),
	"GET_PARAMETER_FLAG" NUMBER(1,0) NOT NULL,
	"POINTBACK_PERMISSION" NUMBER(1,0) NOT NULL,
	"SELF_CONVERSION_FLAG" NUMBER(1,0),
	"CAMPAIGN_START_DATE" DATE,
	"CAMPAIGN_END_DATE" DATE,
	"AUTO_AFF_APPR_DURATION" NUMBER(2,0) DEFAULT 3,
	"OEM_FLAG" NUMBER(1,0),
	"AUTO_ACTION_APPR_DURATION" NUMBER(2,0),
	"HIDDEN_FLAG" NUMBER(1,0),
	"START_DATE" DATE,
	"END_DATE" DATE,
	"OVERLAP_FLG" NUMBER(1,0) DEFAULT 0,
	"OFFER_CODE" VARCHAR2(32),
	"DESCRIPTION_EN" VARCHAR2(4000),
	"CAMPAIGN_TYPE" NUMBER(2,0) DEFAULT 0,
	"CURRENCY" VARCHAR2(3) DEFAULT 'USD',
	"AD_PLATFORM_ID" NUMBER(1,0) DEFAULT 0,
	"CREATED_BY" VARCHAR2(256),
	"CREATED_ON" DATE,
	"UPDATED_BY" VARCHAR2(256),
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS CAMPAIGN_INTEGRATION_CONDITION;
CREATE TABLE CAMPAIGN_INTEGRATION_CONDITION (
   "ID" NUMBER(12,0) NOT NULL,
   "CAMPAIGN_BRAND" VARCHAR(100) NOT NULL,
   "CAMPAIGN_ID" NUMBER(12,0) NOT NULL,
   "CONDITION_NAME" VARCHAR2(100) NOT NULL,
   "CONDITION_VALUES" VARCHAR2(100) NOT NULL,
   "GROUP_SHOP_ID_VALUES" VARCHAR2(100),
   "RELATIVE_CLAUSE_OPERATOR" VARCHAR2(100)
);

DROP TABLE IF EXISTS MERCHANT_INTEGRATION_HISTORY;
CREATE TABLE MERCHANT_INTEGRATION_HISTORY (
      "KEY" VARCHAR2(100) PRIMARY KEY NOT NULL,
      "CAMPAIGN_ID" NUMBER(10,0) NOT NULL,
      "CONVERSION_OCCURS_DATE" DATE NOT NULL,
      "DATA" VARCHAR2(4000) NOT NULL,
      "MERCHANT" VARCHAR(100) DEFAULT NULL
);

DROP TABLE IF EXISTS CURRENCY_EXCHANGE_RATE_HISTORY;
CREATE TABLE CURRENCY_EXCHANGE_RATE_HISTORY (
    "CURRENCY" VARCHAR2(3) NOT NULL,
    "QUOTE_CURRENCY" VARCHAR2(3) NOT NULL,
    "TARGET_MONTH" DATE NOT NULL,
    "RATE" NUMBER(20,10) NOT NULL,
    "CREATED_BY" VARCHAR2(128),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(128),
    "UPDATED_ON" DATE
);

DROP TABLE IF EXISTS MERCHANT_ACCOUNT;
CREATE TABLE MERCHANT_ACCOUNT (
    "ACCOUNT_NO" NUMBER(10,0) NOT NULL,
    "MERCHANT_TYPE_ID" NUMBER(1,0) NOT NULL,
    "CORPORATE_NAME" VARCHAR2(128),
    "CORPORATE_ZIP_CODE" VARCHAR2(10),
    "CORPORATE_PREFECTURE" VARCHAR2(128),
    "CORPORATE_CITY" VARCHAR2(128),
    "CORPORATE_ADDRESS" VARCHAR2(128),
    "CORPORATE_ADDRESS2" VARCHAR2(128),
    "CORPORATE_PHONE" VARCHAR2(32),
    "CORPORATE_FAX" VARCHAR2(32),
    "CORPORATE_DIRECTOR_NAME" VARCHAR2(128),
    "CORPORATE_REMARK" VARCHAR2(2000),
    "FOSTER_LASTNAME" VARCHAR2(64),
    "FOSTER_FIRSTNAME" VARCHAR2(64),
    "FOSTER_MIDDLENAME" VARCHAR2(64),
    "FOSTER_ZIP_CODE" VARCHAR2(10),
    "FOSTER_PREFECTURE" VARCHAR2(128),
    "FOSTER_CITY" VARCHAR2(128),
    "FOSTER_ADDRESS" VARCHAR2(128),
    "FOSTER_ADDRESS2" VARCHAR2(128),
    "FOSTER_SECTION_NAME" VARCHAR2(128),
    "FOSTER_POST_NAME" VARCHAR2(128),
    "FOSTER_EMAIL" VARCHAR2(64),
    "FOSTER_PHONE" VARCHAR2(32),
    "FOSTER_FAX" VARCHAR2(32),
    "FOSTER_REMARK" VARCHAR2(2000),
    "LOGIN_NAME" VARCHAR2(64),
    "LOGIN_PASSWORD" VARCHAR2(32),
    "ACCOUNT_STATE" NUMBER(1,0) NOT NULL,
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "ACCOUNTANT_LASTNAME" VARCHAR2(64),
    "ACCOUNTANT_FIRSTNAME" VARCHAR2(64),
    "ACCOUNTANT_MIDDLENAME" VARCHAR2(64),
    "ACCOUNTANT_EMAIL" VARCHAR2(64),
    "ACCOUNTANT_PHONE" VARCHAR2(32),
    "U_ID" VARCHAR2(32) DEFAULT 'v5542527tvx7w4ts6suvswssss2137xx',
    "COUNTRY_CODE" VARCHAR2(2) DEFAULT 'ID' NOT NULL
);

DROP TABLE IF EXISTS COUNTRY;
CREATE TABLE COUNTRY (
    "CODE" CHAR(2) NOT NULL,
    "NAME" VARCHAR2(256),
    "CURRENCY" VARCHAR2(3) NOT NULL,
    "SUPPORT_EMAIL" VARCHAR2(64) NOT NULL,
    "ACCESSTRADE_URL" VARCHAR2(64) NOT NULL,
    "PHONE" VARCHAR2(32) NOT NULL,
    "MINIMUM_AMOUNT" NUMBER(10,0) NOT NULL,
    "CJ_API_KEY" VARCHAR2(256),
    "CJ_PUBLISHER_ID" VARCHAR2(10),
    "ZONE_ID" VARCHAR2(40),
    "GLOBAL_PUBLISHERS_ENABLED" NUMBER(1,0)
);

DROP TABLE IF EXISTS STAFF_ACCOUNT;
CREATE TABLE STAFF_ACCOUNT (
    "STAFF_NO" NUMBER(10,0),
    "LASTNAME" VARCHAR2(64),
    "FIRSTNAME" VARCHAR2(64),
    "EMAIL" VARCHAR2(64),
    "PHONE" VARCHAR2(32),
    "LOGIN_NAME" VARCHAR2(64),
    "LOGIN_PASSWORD" VARCHAR2(32),
    "ACCOUNT_STATE" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "U_ID" CHAR(32),
    "COUNTRY_CODE" VARCHAR2(2)
);

DROP TABLE IF EXISTS CONVERSION_UPDATE_REQUEST;
CREATE TABLE CONVERSION_UPDATE_REQUEST (
    "FILE_NAME" VARCHAR2(512),
    "ERROR_COUNT" NUMBER(2, 0),
    "CAMPAIGN_ID" NUMBER(10, 0),
    "STAFF_EMAIL" VARCHAR2(64),
    "DATA_COUNT" NUMBER(10, 0)
);

DROP TABLE IF EXISTS API_KEY_NAME;
CREATE TABLE API_KEY_NAME (
    "CAMPAIGN_ID" NUMBER(10, 0) NOT NULL,
    "CAMPAIGN_ADV_TYPE" VARCHAR2(20),
    "ADV_PARENT_CAMPAIGN_ID" NUMBER(10, 0),
    "SHOP_ID_MAPPING" VARCHAR2(100),
    "ADV_PLATFORM" VARCHAR2(20),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE
);