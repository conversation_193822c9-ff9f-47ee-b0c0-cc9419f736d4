/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core;

import java.math.BigDecimal;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CustomerSupport;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

/**
 * Integration test for {@link CountryMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class CountryMapperTest {

    private static final String INDONESIA_COUNTRY_CODE = "ID";
    private static final String MALAYSIA_COUNTRY_CODE = "MY";
    private static final String SINGAPORE_COUNTRY_CODE = "SG";
    private static final String THAILAND_COUNTRY_CODE = "TH";
    private static final String VIETNAM_COUNTRY_CODE = "VN";

    @Inject
    private CountryMapper underTest;

    @Test
    public void testIsConversionProcessingShouldReturnTrueWhenProcessFlagIsOne() {
        // when
        boolean actual = underTest.isConversionProcessing(THAILAND_COUNTRY_CODE);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsConversionProcessingShouldReturnFalseWhenProcessFlagIsZero() {
        // when
        boolean actual = underTest.isConversionProcessing(MALAYSIA_COUNTRY_CODE);

        // then
        assertFalse(actual);
    }

    @Test
    public void testUpdateConversionProcessingFlagToProcessingShouldReturnOneWhenGivenCountryCodeProcessFlagIsZero() {
        // when
        int actual = underTest.updateConversionProcessingFlagToProcessing(
                SINGAPORE_COUNTRY_CODE);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testUpdateConversionProcessingFlagToProcessingShouldReturnZeroWhenGivenCountryCodeProcessFlagIsOne() {
        // when
        int actual = underTest.updateConversionProcessingFlagToProcessing(
                THAILAND_COUNTRY_CODE);

        // then
        assertEquals(0, actual);
    }

    @Test
    public void testUpdateConversionProcessingFlagToNotProcessingShouldReturnOneWhenGivenCountryCodeProcessFlagIsOne() {
        // when
        int actual = underTest.updateConversionProcessingFlagToNotProcessing(
                VIETNAM_COUNTRY_CODE);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testUpdateConversionProcessingFlagToNotProcessingShouldReturnZeroWhenGivenCountryCodeProcessFlagIsZero() {
        // given
        String countryCode = "JP";

        // when
        int actual = underTest.updateConversionProcessingFlagToNotProcessing(countryCode);

        // then
        assertEquals(0, actual);
    }

    @Test
    public void testFindCustomerSupportByReturnNullWhenCountryIsNotFound() {
        // given
        String countryCode = "IDD";

        // when
        CustomerSupport actual = underTest.findCustomerSupportBy(countryCode);

        // then
        assertNull(actual);
    }

    @Test
    public void testFindCustomerSupportByReturnCorrectDataWhenCountryIsFound() {
        // given
        String countryCode = "ID";

        // when
        CustomerSupport actual = underTest.findCustomerSupportBy(countryCode);

        // then
        assertNotNull(actual);
        assertEquals("ACCESSTRADE Indonesia <<EMAIL>>",
                actual.getEmailSender());
        assertEquals("https://www.accesstrade.co.id", actual.getAccessTradeUrl());
    }

    @Test
    public void testFindCustomerSupportByShouldReturnCorrectCustomerSupportWhenCalled() {
        // when
        CustomerSupport actual = underTest.findCustomerSupportBy(MALAYSIA_COUNTRY_CODE);

        // then
        assertNotNull(actual);
        assertEquals("https://www.accesstrade.global/my", actual.getAccessTradeUrl());
        assertEquals("ACCESSTRADE Malaysia <<EMAIL>>",
                actual.getEmailSender());
    }

    @Test
    public void testFindCurrencyByShouldReturnCorrectCurrencyWhenCalled() {
        // when
        String actual = underTest.findCurrencyBy(SINGAPORE_COUNTRY_CODE);

        // then
        assertEquals("SGD", actual);
    }

    @Test
    public void testFindZoneIdByShouldReturnCorrectZoneIdWhenCalled() {
        // when
        String actual = underTest.findZoneIdBy(THAILAND_COUNTRY_CODE);

        // then
        assertEquals("Asia/Bangkok", actual);
    }

    @Test
    public void testFindLocaleByShouldReturnCorrectLocaleWhenCalled() {
        // when
        String actual = underTest.findLocaleBy(THAILAND_COUNTRY_CODE);

        // then
        assertEquals("th-TH", actual);
    }

    @Test
    public void testFindCountryNameByShouldReturnCorrectCountryNameWhenCalled() {
        // when
        String actual = underTest.findCountryNameBy(VIETNAM_COUNTRY_CODE);

        // then
        assertEquals("Vietnam", actual);
    }

    @Test
    public void testIsPendingForMerchantPaymentShouldReturnFalseWhenGivenCountryIsIndonesia() {
        // when
        boolean actual = underTest.isPendingForMerchantPayment(INDONESIA_COUNTRY_CODE);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsPendingForMerchantPaymentShouldReturnTrueWhenGivenCountryIsSingapore() {
        // when
        boolean actual = underTest.isPendingForMerchantPayment(SINGAPORE_COUNTRY_CODE);

        // then
        assertTrue(actual);
    }

    @Test
    public void testFindTaxMinCalculationByShouldReturnCorrectCountryNameWhenCalled() {
        // when
        BigDecimal actual = underTest.findTaxMinCalculationBy(SINGAPORE_COUNTRY_CODE);

        // then
        assertEquals(new BigDecimal("333.33"), actual);
    }
}
