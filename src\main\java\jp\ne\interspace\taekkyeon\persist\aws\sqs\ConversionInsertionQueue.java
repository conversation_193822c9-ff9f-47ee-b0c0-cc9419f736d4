/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.sqs;

import javax.inject.Singleton;

import com.google.inject.Inject;

import lombok.Getter;

import jp.ne.interspace.taekkyeon.module.ConversionInsertionQueueNameResolver;

import static lombok.AccessLevel.PROTECTED;

/**
 * SQS queue for inserting conversions.
 *
 * <AUTHOR>
 */
@Singleton
public class ConversionInsertionQueue extends SimpleQueueServiceQueue {

    @Inject @ConversionInsertionQueueNameResolver @Getter(PROTECTED)
    private String partialQueueName;
}
