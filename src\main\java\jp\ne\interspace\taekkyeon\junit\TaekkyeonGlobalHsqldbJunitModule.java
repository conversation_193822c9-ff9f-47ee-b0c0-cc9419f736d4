/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.junit;

import java.lang.annotation.Annotation;
import java.util.Collection;
import java.util.Properties;
import java.util.Set;

import javax.sql.DataSource;

import com.google.inject.Key;
import com.google.inject.PrivateModule;
import com.google.inject.name.Names;

import org.apache.ibatis.io.ResolverUtil;
import org.apache.ibatis.io.ResolverUtil.Test;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionManager;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.mybatis.guice.MyBatisModule;
import org.mybatis.guice.datasource.builtin.PooledDataSourceProvider;
import org.mybatis.guice.datasource.helper.JdbcHelper;
import org.mybatis.guice.session.SqlSessionFactoryProvider;
import org.mybatis.guice.session.SqlSessionManagerProvider;

import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.module.DatabaseType;

import static com.google.inject.Key.get;
import static com.google.inject.Scopes.SINGLETON;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonMyBatisModule.BIND_KEY_MYBATIS_ENVIRONMENT_ID;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonMyBatisModule.DEFAULT_MYBATIS_ENVIRONMENT_NAME;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonMyBatisModule.MYBATIS_SQL_MAPPER_PACKAGES;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonMyBatisModule.MYBATIS_TYPE_HANDLER_PACKAGE;

/**
 * Taekkyeon module for global JUnit HSQLDB data source.
 *
 * <AUTHOR> Mayur
 */
public class TaekkyeonGlobalHsqldbJunitModule extends PrivateModule {

    private static final String BIND_KEY_HSQLDB_USERNAME = "hsqldb.username";
    private static final String BIND_KEY_HSQLDB_PASSWORD = "hsqldb.password";
    private static final String BIND_KEY_HSQLDB_AUTOCOMMIT = "hsqldb.autoCommit";

    private static final String HSQLDB_USERNAME = "sa";
    private static final String HSQLDB_PASSWORD = "";
    private static final String HSQLDB_AUTOCOMMIT = "false";

    private static final String VM_ARG_NAME_FOR_TEST_BATCH_NAME = "batchName";
    private static final String TEST_BATCH_NAME = "testBatch";

    private static final String MYBATIS_SQL_CORE_MAPPER_PACKAGES = "jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core";

    private final DatabaseType databaseType;

    private final Class<? extends Annotation> country;

    /**
     * Constructor of {@link TaekkyeonGlobalHsqldbJunitModule}.
     *
     * @param country
     *            {@link Country} which country the datasource resides
     * @param databaseType
     *            {@link databaseType} which databaseType the data resides
     */
    public TaekkyeonGlobalHsqldbJunitModule(Country country, DatabaseType databaseType) {
        this.country = country.getAnnotationClass();
        this.databaseType = databaseType;
    }

    @Override
    protected void configure() {
        install(new MyBatisModule() {

            @Override
            protected void initialize() {
                setTestBatchNameSystemProperty();

                addMapperClassesIn(MYBATIS_SQL_MAPPER_PACKAGES.get(databaseType));
                addTypeHandlerClasses(MYBATIS_TYPE_HANDLER_PACKAGE);

                Names.bindProperties(binder(), getHsqldbProperties());
                install(JdbcHelper.HSQLDB_IN_MEMORY_NAMED);

                bindDataSourceProviderType(PooledDataSourceProvider.class);
                bindTransactionFactoryType(JdbcTransactionFactory.class);

                bindSessionManager();
                bindSessionFactory();
                bindDataSourceProvider();
            }

            private void bindSessionManager() {
                bind(SqlSessionManager.class).annotatedWith(country)
                        .toProvider(SqlSessionManagerProvider.class).in(SINGLETON);
                expose(SqlSessionManager.class)
                        .annotatedWith(country);
            }

            private void bindSessionFactory() {
                bind(SqlSessionFactory.class)
                        .annotatedWith(databaseType.getAnnotationType())
                        .toProvider(SqlSessionFactoryProvider.class);
                expose(SqlSessionFactory.class)
                        .annotatedWith(databaseType.getAnnotationType());
            }

            private void bindDataSourceProvider() {
                bind(DataSource.class).annotatedWith(databaseType.getAnnotationType())
                        .toProvider(PooledDataSourceProvider.class);
                expose(DataSource.class).annotatedWith(databaseType.getAnnotationType());
            }

            private void setTestBatchNameSystemProperty() {
                if (System.getProperty(VM_ARG_NAME_FOR_TEST_BATCH_NAME) == null) {
                    System.setProperty(VM_ARG_NAME_FOR_TEST_BATCH_NAME, TEST_BATCH_NAME);
                }
            }

            private Properties getHsqldbProperties() {
                Properties properties = new Properties();
                properties.setProperty(BIND_KEY_MYBATIS_ENVIRONMENT_ID,
                        DEFAULT_MYBATIS_ENVIRONMENT_NAME);
                properties.setProperty(BIND_KEY_HSQLDB_USERNAME, HSQLDB_USERNAME);
                properties.setProperty(BIND_KEY_HSQLDB_PASSWORD, HSQLDB_PASSWORD);
                properties.setProperty(BIND_KEY_HSQLDB_AUTOCOMMIT, HSQLDB_AUTOCOMMIT);
                return properties;
            }

            private void addMapperClassesIn(String packageName) {
                bind(getClassesIn(packageName));
                bind(getClassesIn(MYBATIS_SQL_CORE_MAPPER_PACKAGES));
            }

            private Set<Class<?>> getClassesIn(String packageName) {
                Test isObject = new ResolverUtil.IsA(Object.class);
                return new ResolverUtil<Object>().find(isObject, packageName)
                        .getClasses();
            }

            @SuppressWarnings({ "unchecked", "rawtypes" })
            private void bind(Collection<Class<?>> mappers) {
                for (Class<?> mapper : mappers) {
                    Key key = get(mapper, country);
                    bind(key).to(mapper);
                    addMapperClass(mapper);
                    expose(key);
                }
            }

        });

    }
}
