/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core;

import java.time.YearMonth;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.ClosedMonth;
import jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.StringYearMonthTypeHandler;

/**
 * Mybatis mapper for monthly closing.
 *
 * <AUTHOR> Huynh
 */
public interface MonthlyClosingMapper {

    String SELECT_CLOSED_MONTH =
            "SELECT"
            + "    closed_month "
            + "FROM"
            + "    monthly_closing "
            + "WHERE"
            + "    country_code = #{countryCode}";

    /**
     * Returns the closed month by the given country code.
     *
     * @param countryCode
     *              target country code
     * @return the closed month by the given country code
     * @see #SELECT_CLOSED_MONTH
     */
    @Select(SELECT_CLOSED_MONTH)
    @ConstructorArgs({@Arg(column = "closed_month", javaType = YearMonth.class,
                 typeHandler = StringYearMonthTypeHandler.class) })
    ClosedMonth findClosedMonth(@Param("countryCode") String countryCode);
}
