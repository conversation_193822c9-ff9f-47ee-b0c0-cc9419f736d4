/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding recoommended campaign type.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum RecommendedCampaignType implements ValueEnum {

    FEATURED(1),
    FAST_GROWING(2),
    TOP(3);

    private final int value;
}
