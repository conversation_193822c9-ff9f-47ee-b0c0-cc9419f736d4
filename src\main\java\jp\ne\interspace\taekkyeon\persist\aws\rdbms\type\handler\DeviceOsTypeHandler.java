/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler.ValueEnumTypeHandler;

/**
 * MyBatis {@link TypeHandler} for {@link DeviceOs}.
 *
 * <AUTHOR>
 */
@MappedTypes(DeviceOs.class)
public class DeviceOsTypeHandler extends ValueEnumTypeHandler<DeviceOs> {

    private static final DeviceOs DEFAULT_DEVICE_OS = DeviceOs.UNKNOWN;

    public DeviceOsTypeHandler() {
        super(DeviceOs.class, DEFAULT_DEVICE_OS);
    }

    /**
     * Returns the system default {@link DeviceOs}.
     *
     * @return system default {@link DeviceOs}
     */
    public static DeviceOs getDefaultDeviceOs() {
        return DEFAULT_DEVICE_OS;
    }
}
