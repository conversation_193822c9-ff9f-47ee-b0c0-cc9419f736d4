/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.io.File;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO the data of google ads.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class GoogleAd {

    private final String adHeading;
    private final String adDescription;
    private final String finalUrl;
    private final String domain;
    private final String country;
    private final String city;
    private final String googleTrackingUrl;
    private final String screenshotImageUrl;
    private final File screenshotFile;
    private final int pageNumber;
}
