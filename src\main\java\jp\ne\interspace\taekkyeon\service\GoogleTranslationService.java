/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import javax.inject.Singleton;

import com.google.cloud.translate.Detection;
import com.google.cloud.translate.Translate;
import com.google.inject.Inject;

/**
 * Service layer for translating using Google translation.
 *
 * <AUTHOR>
 */
@Singleton
public class GoogleTranslationService {

    @Inject
    private Translate translate;

    /**
     * Detect the language of {@code text}.
     *
     * @param text
     *            given the word to detect language
     * @return detected language of {@code text}
     */
    public String detectLauguage(String text) {
        Detection detection = translate.detect(text);
        return detection.getLanguage();
    }
}
