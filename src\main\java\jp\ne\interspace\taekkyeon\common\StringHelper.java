/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import java.util.Arrays;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;

import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.stream.Collectors.joining;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SPACE;

/**
 * Convenience class for string operations.
 *
 * <AUTHOR> OBS DEV Team
 */
@Singleton
public class StringHelper {

    private static final String ALPHANUMERIC_CHARACTERS = "**********"
            + "ABCDEFGHIJKLMNOPQRSTUVWXYZ" + "abcdefghijklmnopqrstuvwxyz";

    @VisibleForTesting
    Random random = new Random();

    /**
     * Returns a random alphnumberic string of the given length.
     *
     * @param length
     *            length of the random string to be generated
     * @return random alphnumberic string of the given length
     */
    public String generateRandomAlphanumericString(final int length) {
        return IntStream.range(0, length)
                .mapToObj(__ -> String.valueOf(ALPHANUMERIC_CHARACTERS.charAt(
                        random.nextInt(ALPHANUMERIC_CHARACTERS.length()))))
                .collect(joining());
    }

    /**
     * Returns the longest prefix of the string {@code toTruncate}, whose {@code UTF-8}
     * byte array representation is not longer than {@code byteCount}.
     *
     * @param toTruncate
     *            the string to truncate
     * @param byteCount
     *            the desired length of the truncated string in bytes
     * @return the truncated string
     */
    public String truncateToBytes(String toTruncate, int byteCount) {
        String result = toTruncate;
        while (!Strings.isNullOrEmpty(result) && result.getBytes().length > byteCount) {
            result = result.substring(0, result.length() - 1);
        }
        return result;
    }

    /**
     * Returns the longest suffix of the string {@code toTruncate}, whose {@code UTF-8}
     * byte array representation is not longer {@code byteCount}.
     *
     * @param toTruncate
     *            the string to truncate
     * @param byteCount
     *            the desired length of the truncated string in bytes
     * @return the truncated string
     */
    public String truncateToBytesFromFront(String toTruncate, int byteCount) {
        String result = toTruncate;
        while (!Strings.isNullOrEmpty(result)
                && result.getBytes().length - byteCount > 0) {
            result = result.substring(1, result.length());
        }
        return result;
    }

    /**
     * Encodes the given {@link String} into a new byte array using {@code UTF-8}.
     *
     * @param string
     *          the string to encode into a byte array
     * @return a new byte array encoded with {@code UTF-8}
     * @throws Exception when a problem occurs
     */
    public byte[] getBytesFrom(String string) throws Exception {
        if (!Strings.isNullOrEmpty(string)) {
            return string.getBytes(UTF_8.name());
        }
        return null;
    }

    /**
     * Converts the given {@link String} to camel case.
     *
     * @param str
     *          the string to be converted to camel case. Words in the input string
     *          are assumed to be separated by spaces.
     * @return the camel case version of the input string, where the first word is in lowercase
     *          and subsequent words are capitalized.
     */
    public String toCamelCaseFrom(String str) {
        String[] words = str.trim().toLowerCase().split(SPACE);
        return words[0]
                + Arrays.stream(words, 1, words.length)
                .map(word -> Character.toUpperCase(word.charAt(0)) + word.substring(1))
                .collect(Collectors.joining());
    }
}
