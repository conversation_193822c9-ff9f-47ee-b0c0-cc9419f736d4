/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import java.util.Random;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link StringHelper}.
 *
 * <AUTHOR> OBS DEV Team
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class StringHelperTest {

    @InjectMocks
    private StringHelper underTest;

    @Mock
    private Random random;

    @Test
    public void testGenerateRandomStringShouldReturnCorrectGeneratedStringWhenTheGivenIsGreaterThanZero() {
        // given
        int length = 6;

        when(random.nextInt(62)).thenReturn(10).thenReturn(61).thenReturn(13)
                .thenReturn(5).thenReturn(35).thenReturn(0);

        // when
        String actual = underTest.generateRandomAlphanumericString(length);

        // then
        assertEquals("AzD5Z0", actual);
    }

    @Test
    public void testGenerateRandomAlphanumericStringShouldReturnNonNullEmptyStringEvenWhenTheGivenLengthIsZero() {
        // given
        int zeroLength = 0;

        // when
        String actual = underTest.generateRandomAlphanumericString(zeroLength);

        // then
        assertNotNull(actual);
        assertThat(actual.length(), is(zeroLength));
    }

    @Test
    public void testGenerateRandomAlphanumericStringShouldReturnNonNullEmptyStringEvenWhenTheGivenLengthIsBelowZero() {
        // given
        int belowZeroLength = -213;
        int expected = 0;

        // when
        String actual = underTest.generateRandomAlphanumericString(belowZeroLength);

        // then
        assertNotNull(actual);
        assertThat(actual.length(), is(expected));
    }

    @Test
    public void testTruncateToBytesShouldReturnOriginalStringWhenTheLengthOfOriginalStringEqualsTheDesiredByteCount() {
        // given
        String toTruncate = "abc";
        int byteCount = 3;

        // when
        String actual = underTest.truncateToBytes(toTruncate, byteCount);

        // then
        assertEquals(toTruncate, actual);
    }

    @Test
    public void testTruncateToBytesShouldReturnTruncatedStringWhenOriginalStringIsLongerTheDesiredByteCount() {
        // given
        String toTruncate = "abc";
        int byteCount = 2;
        String expected = "ab";

        // when
        String actual = underTest.truncateToBytes(toTruncate, byteCount);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testTruncateToBytesShouldReturnEmptyStringWhenTheDesiredByteCountIsZero() {
        // given
        String toTruncate = "abc";
        int byteCount = 0;
        String expected = "";

        // when
        String actual = underTest.truncateToBytes(toTruncate, byteCount);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testTruncateToBytesShouldReturnEmptyStringWhenTheDesiredByteCountIsNegative() {
        // given
        String toTruncate = "abc";
        int byteCount = -1;
        String expected = "";

        // when
        String actual = underTest.truncateToBytes(toTruncate, byteCount);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetBytesFromShouldReturnCorrectDataWhenGivenCorrectData()
            throws Exception {
        // given
        byte[] expected = { 116, 101, 115, 116 };

        // when
        byte[] actual = underTest.getBytesFrom("test");

        // then
        assertNotNull(actual);
        assertEquals(4, actual.length);
        assertEquals(expected[0], actual[0]);
        assertEquals(expected[1], actual[1]);
        assertEquals(expected[2], actual[2]);
        assertEquals(expected[3], actual[3]);
    }

    @Test
    public void testGetBytesFromShouldReturnNullWhenGivenNull() throws Exception {
        // when
        byte[] actual = underTest.getBytesFrom(null);

        // then
        assertNull(actual);
    }

    @Test
    public void testTruncateToBytesFromFrontShouldReturnOriginalStringWhenTheLengthOfOriginalStringLessThanTheDesiredByteCount() {
        // given
        String toTruncate = "123456789";
        int byteCount = 10;

        // when
        String actual = underTest.truncateToBytesFromFront(toTruncate, byteCount);

        // then
        assertEquals(toTruncate, actual);
    }

    @Test
    public void testTruncateToBytesFromFrontShouldReturnOriginalStringWhenTheLengthOfOriginalStringEqualsTheDesiredByteCount() {
        // given
        String toTruncate = "1234567890";
        int byteCount = 10;

        // when
        String actual = underTest.truncateToBytesFromFront(toTruncate, byteCount);

        // then
        assertEquals(toTruncate, actual);
    }

    @Test
    public void testTruncateToBytesFromFrontShouldReturnTruncatedStringWhenOriginalStringIsLongerTheDesiredByteCount() {
        // given
        String toTruncate = "a1234567890";
        int byteCount = 10;
        String expected = "1234567890";

        // when
        String actual = underTest.truncateToBytesFromFront(toTruncate, byteCount);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testTruncateToBytesFromFrontShouldReturnEmptyStringWhenTheDesiredByteCountIsZero() {
        // given
        String toTruncate = "1234567890";
        int byteCount = 0;
        String expected = "";

        // when
        String actual = underTest.truncateToBytesFromFront(toTruncate, byteCount);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testTruncateToBytesFromFrontShouldReturnEmptyStringWhenTheDesiredByteCountIsNegative() {
        // given
        String toTruncate = "1234567890";
        int byteCount = -1;
        String expected = "";

        // when
        String actual = underTest.truncateToBytesFromFront(toTruncate, byteCount);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testToCamelCaseFromShouldReturnCorrectValueWhenCalled() throws Exception {
        // given
        String input = "camel case";
        String expected = "camelCase";

        // when
        String actual = underTest.toCamelCaseFrom(input);

        // then
        assertNotNull(actual);
        assertEquals(actual, expected);
    }
}
