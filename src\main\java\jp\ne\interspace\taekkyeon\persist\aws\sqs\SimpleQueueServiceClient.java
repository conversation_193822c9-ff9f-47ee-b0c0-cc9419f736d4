/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.sqs;

import java.util.Arrays;
import java.util.List;

import javax.inject.Singleton;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClient;
import com.amazonaws.services.sqs.model.CreateQueueResult;
import com.amazonaws.services.sqs.model.DeleteMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.DeleteQueueResult;
import com.amazonaws.services.sqs.model.GetQueueAttributesResult;
import com.amazonaws.services.sqs.model.GetQueueUrlResult;
import com.amazonaws.services.sqs.model.PurgeQueueRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import com.amazonaws.services.sqs.model.SendMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.SendMessageBatchResult;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;

import static com.google.common.base.Preconditions.checkArgument;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.AWS_SQS_CREDENTIALS_CHAIN;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.DEFAULT_AWS_REGION;

/**
 * Low-level client for Amazon SQS Service.
 *
 * <AUTHOR> Shin
 * @see {@link AmazonSQS}
 */
@Singleton
public class SimpleQueueServiceClient {

    private AmazonSQS sqs;

    /**
     * Constructs a new Amazon SQS client using the specified AWS credentials provider to
     * access Amazon SQS.
     */
    public SimpleQueueServiceClient() {
        sqs = createAmazonSqs();
    }

    private AmazonSQS createAmazonSqs() {
        return AmazonSQSClient.builder().withCredentials(AWS_SQS_CREDENTIALS_CHAIN)
                .withRegion(DEFAULT_AWS_REGION).build();
    }

    private void checkParameters(String... parameters) {
        String message = "Invalid String parameter(s) for SQS API";
        checkArgument(parameters != null, message);
        for (String parameter : parameters) {
            checkArgument(Strings.nullToEmpty(parameter).trim().length() > 0, message);
        }
    }

    private void checkParameters(List<?>... parameters) {
        String message = "Invalid List parameter(s) for SQS API";
        checkArgument(parameters != null, message);
        for (List<?> parameter : parameters) {
            checkArgument(parameter != null && !parameter.isEmpty(), message);
        }
    }

    /**
     * Receive message based on the given {@link ReceiveMessageRequest}.
     *
     * @param request
     *            {@link ReceiveMessageRequest} specifying the request attributes
     * @return {@link ReceiveMessageResult} containing the received messages
     */
    public ReceiveMessageResult receiveMessage(ReceiveMessageRequest request) {
        checkArgument(request != null, "Invalid ReceiveMessageRequest");
        return sqs.receiveMessage(request);
    }

    /**
     * Put messages to the specified queue.
     *
     * @param queueUrl
     *          the url of the Amazon SQS queue
     * @param entries
     *          the messages
     * @return the result of send message batch
     */
    public SendMessageBatchResult sendMessageBatch(
            String queueUrl, List<SendMessageBatchRequestEntry> entries) {
        checkArgument(entries != null && !entries.isEmpty(),
                "Invalid List Parameter for SQS API");
        checkParameters(queueUrl);

        return sqs.sendMessageBatch(queueUrl, entries);
    }

    /**
     * Put message to the specified queue.
     *
     * @param queueUrl
     *          the url of the Amazon SQS queue
     * @param message
     *          the message
     * @return the result of send message
     */
    public SendMessageResult sendMessage(String queueUrl, String message) {
        checkArgument(message != null, "Invalid message for SQS API");
        checkParameters(queueUrl);
        return sqs.sendMessage(queueUrl, message);
    }

    /**
     * Delete the given messages from the given queue.
     *
     * @param queueUrl
     *            URL of the given queue
     * @param messagesToDelete
     *            {@link DeleteMessageBatchRequestEntry} items containing the unique IDs
     *            of the receipt of the messages to delete
     */
    public void deleteMessageBatch(String queueUrl,
            List<DeleteMessageBatchRequestEntry> messagesToDelete) {
        checkParameters(queueUrl);
        checkParameters(messagesToDelete);
        sqs.deleteMessageBatch(queueUrl, messagesToDelete);
    }

    /**
     * Gets the queue URL for the specified Amazon SQS queue name.
     *
     * @param queueName
     *            the name of the queue
     * @return the queue URL for the specified Amazon SQS queue name
     */
    public String getQueueUrl(String queueName) {
        checkParameters(queueName);
        GetQueueUrlResult urlResult = sqs.getQueueUrl(queueName);
        if (urlResult == null) {
            throw new TaekkyeonException("Not found queue url.");
        }
        return urlResult.getQueueUrl();
    }

    /**
     * Adds a new queue to your account.
     *
     * @param queueName
     *          the name of queue
     * @return {@link CreateQueueResult} as a result of the CreateQueue
     * @see AmazonSQS#createQueue(String)
     */
    public CreateQueueResult createQueue(String queueName) {
        checkParameters(queueName);
        return sqs.createQueue(queueName);
    }

    /**
     * Deletes a queue and all of its items.
     *
     * @param queueUrl
     *          the url of the queue to delete
     * @return {@link DeleteQueueResult} as a result of the DeleteQueue
     * @see AmazonSQS#deleteQueue(String)
     */
    public DeleteQueueResult deleteQueue(String queueUrl) {
        checkParameters(queueUrl);
        return sqs.deleteQueue(queueUrl);
    }

    @VisibleForTesting
    public int getApproximateNumberOfMessages(String queueUrl) {
        String visibleMessages = "ApproximateNumberOfMessages";
        String invisibleMessages = "ApproximateNumberOfMessagesNotVisible";
        GetQueueAttributesResult attributes = sqs.getQueueAttributes(queueUrl,
                Arrays.asList(visibleMessages, invisibleMessages));

        int numberOfVisibleMessages = Integer
                .parseInt(attributes.getAttributes().get(visibleMessages));
        int numberOfInvisibleMessages = Integer
                .parseInt(attributes.getAttributes().get(invisibleMessages));
        return numberOfVisibleMessages + numberOfInvisibleMessages;
    }

    @VisibleForTesting
    public void purge(String queueUrl) {
        sqs.purgeQueue(new PurgeQueueRequest(queueUrl));
    }
}
