/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.validator;

import javax.inject.Singleton;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;

/**
 * Class for validating the various database operations.
 *
 * <AUTHOR>
 */
@Singleton
public class DatabaseOperationValidator {

    private static final String ROW_COUNT_MISMATCH_ERROR_MESSAGE_FORMAT =
            "Update failed because updated row count: %d "
                    + "and expected row count: %d are different.";

    /**
     * Validates the ID that is auto-generated during {@code insert} operations.
     *
     * @param id
     *            the auto-generated ID
     */
    public void validateAutoGeneratedId(long id) {
        if (id == 0) {
            throw new TaekkyeonException("Insertion failed due to a database error.");
        }
    }

    /**
     * Validates the number of rows that were inserted by an {@code insert} operation.
     *
     * @param insertedRowCount
     *            number of inserted rows
     */
    public void validateInsertedRowCount(int insertedRowCount) {
        if (insertedRowCount != 1) {
            throw new TaekkyeonException("Insertion failed due to a database error.");
        }
    }

    /**
     * Validates the number of rows that were modified by an {@code update} operation.
     * Expects {@code modifiedRowCount} to be {@code 1}.
     *
     * @param modifiedRowCount
     *            number of modified rows
     * @param noRowUpdatedErrorMessage
     *            error message to be sent to the client side when no database row was
     *            updated
     */
    public void validateModifiedRowCount(int modifiedRowCount,
            String noRowUpdatedErrorMessage) {
        if (modifiedRowCount == 0) {
            throw new TaekkyeonException(noRowUpdatedErrorMessage);
        } else if (modifiedRowCount > 1) {
            throw new TaekkyeonException("Update failed due to a database error.");
        }
    }

    /**
     * Validates the number of rows that were modified by an {@code update} operation.
     * {@code expectedModifiedRowCount} can be freely set to any integer value.
     *
     * @param modifiedRowCount
     *            number of modified rows
     * @param expectedModifiedRowCount
     *            number of rows that are expected to be modified
     */
    public void validateModifiedRowCount(int modifiedRowCount,
            int expectedModifiedRowCount) {
        if (modifiedRowCount != expectedModifiedRowCount) {
            throw new TaekkyeonException(String.format(
                    ROW_COUNT_MISMATCH_ERROR_MESSAGE_FORMAT,
                    modifiedRowCount, expectedModifiedRowCount));
        }
    }

    /**
     * Validates the number of rows that were modified by an {@code update} operation.
     *
     * @param modifiedRowCount
     *            number of modified rows
     */
    public void validateModifiedSingleOrZeroRow(int modifiedRowCount) {
        if (modifiedRowCount > 1) {
            throw new TaekkyeonException("Update failed due to a database error.");
        }
    }
}
