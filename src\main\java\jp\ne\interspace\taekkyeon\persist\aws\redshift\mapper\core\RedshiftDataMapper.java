/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.redshift.mapper.core;

import java.util.List;

import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.module.Environment;

/**
 * <PERSON><PERSON><PERSON> mapper for handling data for redshift table.
 *
 * <AUTHOR> NT
 */
public interface RedshiftDataMapper {

    /**
     * Executes copy command for inserting redshift data from s3 file.
     *
     * @param country
     *          country for specified table name
     * @param environment
     *          environment for specified table name
     * @param s3FilePath
     *          s3 file path for inserting redshift data
     * @param key
     *          unique key for specifying table
     * @param redshiftCredentials
     *          IAM credentials for redshift
     */
    void copy(Country country, Environment environment, String s3FilePath,
            String redshiftCredentials, String key);

    /**
     * Creates temporary sync table for upserting redshift data.
     *
     * @param country
     *          country for specified table name
     * @param environment
     *          environment for specified table name
     * @param key
     *          unique key for specifying table
     */
    void createTemporarySyncTable(Country country, Environment environment, String key);

    /**
     * Drops temporary table.
     *
     * @param country
     *          country for specified table name
     * @param environment
     *          environment for specified table name
     * @param key
     *          unique key for specifying table
     */
    void dropTemporaryTable(Country country, Environment environment, String key);

    /**
     * Updates redshift data by temporary table.
     *
     * @param country
     *          country for specified table name
     * @param environment
     *          environment for specified table name
     * @param key
     *          unique key for specifying table
     */
    void updateDataByTemporaryTable(Country country, Environment environment, String key);

    /**
     * Inserts redshift data by temporary table.
     *
     * @param country
     *          country for specified table name
     * @param environment
     *          environment for specified table name
     * @param key
     *          unique key for specifying table
     * @return the number of inserted rows
     */
    int insertDataByTemporaryTable(Country country, Environment environment, String key);

    /**
     * Select distinct redshift data sync table names.
     *
     * @param country
     *          country for specified table name
     * @param environment
     *          environment for specified table name
     * @return distinct redshift data sync table names
     */
    List<String> findDistinctTemporaryTables(Country country, Environment environment);

    /**
     * Drops temporary table.
     * @param tableName
     *          tableName for specifying table
     */
    void dropTemporaryTableBy(String tableName);
}
