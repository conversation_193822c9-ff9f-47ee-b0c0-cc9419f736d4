/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import javax.inject.Singleton;

import org.easybatch.core.record.Batch;
import org.easybatch.core.writer.RecordWriter;

/**
 * No-operation {@link RecordWriter} implementation that can be injected when a record
 * writer is not necessary.
 *
 * <AUTHOR>
 */
@Singleton
public class DummyRecordWriter implements RecordWriter {

    @Override
    public void open() throws Exception {
        // do nothing
    }

    @Override
    public void writeRecords(Batch batch) throws Exception {
        // do nothing
    }

    @Override
    public void close() throws Exception {
        // do nothing
    }
}
