/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CampaignAdvType;

import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.BRAND;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

/**
 * Integration test for {@link ApiKeyNameMapper}.
 *
 * <AUTHOR> Van
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class ApiKeyNameMapperTest {

    @Inject
    private ApiKeyNameMapper underTest;

    @Test
    public void testFindParentCampaignIdByShouldReturnCorrectDataIdWhenParentCampaignIdIsFound() {
        // given
        long brandCampaignId = 2000;
        Long expected = 1000L;

        // when
        Long actual = underTest.findParentCampaignIdBy(BRAND.name(), brandCampaignId);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testFindParentCampaignIdByShouldReturnNullWhenParentCampaignIdIsNotFound() {
        // given
        String campaignAdvType = "INVALID_TYPE";
        long brandCampaignId = 9999;

        // when
        Long actual = underTest.findParentCampaignIdBy(campaignAdvType, brandCampaignId);

        // then
        assertNull(actual);
    }

    @Test
    public void testFindBrandCampaignIdShouldReturnCorrectDataWhenBrandCampaignIdIsFound() {
        // given
        String shopId = "123456";
        Long parentCampaignId = 1000L;
        Long expected = 2000L;

        // when
        Long actual = underTest.findBrandCampaignId(shopId, parentCampaignId);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testFindBrandCampaignIdShouldReturnNullWhenBrandCampaignIdIsNotFound() {
        // given
        String shopId = "999999";
        Long parentCampaignId = 9999L;

        // when
        Long actual = underTest.findBrandCampaignId(shopId, parentCampaignId);

        // then
        assertNull(actual);
    }

    @Test
    public void testFindAdvCampaignByShouldReturnCorrectDataWhenCampaignAdvDetailsIsFound() {
        // given
        long campaignId = 2000;

        // when
        CampaignAdvType actual = underTest.findAdvCampaignBy(campaignId);

        // then
        assertNotNull(actual);
        assertEquals(BRAND, actual);
    }

    @Test
    public void testFindAdvCampaignByShouldReturnNullWhenCampaignAdvDetailsIsNotFound() {
        // given
        long campaignId = 9999;

        // when
        CampaignAdvType actual = underTest.findAdvCampaignBy(campaignId);

        // then
        assertNull(actual);
    }

    @Test
    public void testIsBrandItemShouldReturnTrueWhenShopIdIsFoundInApiKeyName() {
        // given
        String shopId = "123456";

        // when
        boolean actual = underTest.isBrandItem(shopId);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsBrandItemShouldReturnFalseWhenShopIdIsNotFoundInApiKeyName() {
        // given
        String shopId = "999999";

        // when
        boolean actual = underTest.isBrandItem(shopId);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsBrandItemShouldReturnFalseWhenShopIdIsFoundAndAdvPlatformIsNotShopee() {
        // given
        String shopId = "345678";

        // when
        boolean actual = underTest.isBrandItem(shopId);

        // then
        assertFalse(actual);
    }

    @Test
    public void testFindCampaignByShouldReturnCorrectCampaignIdWhenShopIdIsFoundAndAdvPlatformIsShopee() {
        // given
        String shopId = "123456";
        Long expected = 2000L;

        // when
        Long actual = underTest.findCampaignBy(shopId);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testFindCampaignByShouldReturnNullWhenShopIdIsNotFound() {
        // given
        String shopId = "999999";

        // when
        Long actual = underTest.findCampaignBy(shopId);

        // then
        assertNull(actual);
    }

    @Test
    public void testFindCampaignByShouldReturnNullWhenShopIdIsNull() {
        // given
        String shopId = null;

        // when
        Long actual = underTest.findCampaignBy(shopId);

        // then
        assertNull(actual);
    }
}
