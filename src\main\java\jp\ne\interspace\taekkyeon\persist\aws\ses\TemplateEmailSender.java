/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.ses;

import java.io.IOException;
import java.io.StringWriter;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import javax.inject.Singleton;
import javax.mail.MessagingException;

import com.amazonaws.services.simpleemail.model.Body;
import com.amazonaws.services.simpleemail.model.Content;
import com.amazonaws.services.simpleemail.model.Destination;
import com.amazonaws.services.simpleemail.model.Message;
import com.amazonaws.services.simpleemail.model.MessageTag;
import com.amazonaws.services.simpleemail.model.SendEmailRequest;
import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import jp.ne.interspace.taekkyeon.module.EmailEventConfigurationSetNameResolver;

import static java.nio.charset.StandardCharsets.UTF_8;
import static lombok.AccessLevel.PACKAGE;

/**
 * Class for sending emails based on template files.
 *
 * <AUTHOR> Varga
 */
@Slf4j
@Singleton
class TemplateEmailSender {

    @Inject
    private Configuration templateConfiguration;

    @Inject
    private SimpleEmailServiceClient sesClient;

    @Inject @EmailEventConfigurationSetNameResolver @VisibleForTesting @Getter(PACKAGE)
    private String configurationSetName;

    /**
     * Sends an HTML email from the given sender to the given recipient with the given
     * subject and body.
     *
     * @param sender
     *            the email address of the sender
     * @param recipient
     *            the email address of the recipient
     * @param subject
     *            the subject of the email
     * @param bodyTemplateFileName
     *            the file name of the template to be used for the email body
     * @param templateParameters
     *            the parameters to be used in the email body template
     * @param locale
     *            the {@link Locale} of the email template
     * @throws IOException
     *             when the template processing fails
     * @throws TemplateException
     *             when the template processing fails
     */
    void send(String sender, String recipient, String subject,
            String bodyTemplateFileName, Object templateParameters, Locale locale)
            throws IOException, TemplateException {
        Template bodyTemplate = templateConfiguration
                .getTemplate(bodyTemplateFileName, locale);
        StringWriter bodyWriter = createBodyWriter();
        bodyTemplate.process(templateParameters, bodyWriter);
        String body = bodyWriter.toString();

        Destination destination = new Destination().withToAddresses(recipient);
        Message message = new Message()
                .withSubject(new Content(subject).withCharset(UTF_8.name()))
                .withBody(new Body().withHtml(new Content(body)
                .withCharset(UTF_8.name())));

        log.debug("Sending {} email", subject);
        sesClient.sendEmail(sender, destination, message);
        log.debug("{} email has been sent", subject);
    }

    /**
     * Sends an HTML email from the given sender to the given recipient with the given
     * subject and body and message tags.
     *
     * @param sender
     *            the email address of the sender
     * @param recipient
     *            the email address of the recipient
     * @param subject
     *            the subject of the email
     * @param body
     *            the body of the email
     * @param messageTags
     *            the parameters to be used in the email message tag
     */
    void sendWithCustomTags(String sender, String recipient, String subject, String body,
            Map<String, String> messageTags) {
        SendEmailRequest request = new SendEmailRequest()
                .withSource(sender)
                .withDestination(new Destination().withToAddresses(recipient))
                .withMessage(createMessage(subject, body))
                .withConfigurationSetName(getConfigurationSetName())
                .withTags(createMessageTags(messageTags));
        sesClient.sendEmail(request);
    }

    /**
     * Sends an HTML mail with attachment from given criteria.
     *
     * @param sender
     *            the email address of the sender
     * @param recipient
     *            the email address of the recipient
     * @param subject
     *            the subject of the email
     * @param bodyTemplateFileName
     *            the file name of the template to be used for the email body
     * @param templateParameters
     *            the parameters to be used in the email body template
     * @param attachmentPath
     *            the file path of attachment
     * @param locale
     *            the {@link Locale} of the email template
     * @throws IOException
     *            when the template processing fails
     * @throws TemplateException
     *            when the template processing fails
     * @throws MessagingException
     *            when the email body message creating fails
     */
    public void sendWithAttachmentFile(String sender, String recipient, String subject,
            String bodyTemplateFileName, Object templateParameters,
            String attachmentPath, Locale locale)
            throws IOException, TemplateException, MessagingException {
        Template bodyTemplate = templateConfiguration
                .getTemplate(bodyTemplateFileName, locale);
        StringWriter bodyWriter = createBodyWriter();
        bodyTemplate.process(templateParameters, bodyWriter);
        String body = bodyWriter.toString();

        log.debug("Sending {} email", subject);
        sesClient.sendMailWithAttachment(sender, recipient, subject, body,
                attachmentPath);
        log.debug("{} email has been sent", subject);
    }

    @VisibleForTesting
    StringWriter createBodyWriter() {
        return new StringWriter();
    }

    private Message createMessage(String subject, String body) {
        return new Message(new Content(subject).withCharset(UTF_8.name()),
                new Body().withHtml(new Content(body).withCharset(UTF_8.name())));
    }

    private List<MessageTag> createMessageTags(
            Map<String, String> messageTags) {
        return messageTags
                .entrySet()
                .stream()
                .map(messageTag -> new MessageTag().withName(messageTag.getKey())
                        .withValue(messageTag.getValue()))
                .collect(Collectors.toList());
    }
}
