/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model.elasticsearch;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding the response of elasticsearch operation.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @EqualsAndHashCode
public class ElasticsearchBulkOperationResponse {

    private final long took;
    private final boolean errors;
    private final List<ElasticsearchBulkOperationIndex> items;
}
