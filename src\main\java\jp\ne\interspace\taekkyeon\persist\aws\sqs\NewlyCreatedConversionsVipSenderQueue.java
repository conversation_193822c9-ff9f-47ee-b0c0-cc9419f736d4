/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.sqs;

import com.google.inject.Inject;
import lombok.Getter;

import jp.ne.interspace.taekkyeon.module.NewlyCreatedConversionsVipSenderQueueNameResolver;

import static lombok.AccessLevel.PROTECTED;

/**
 * SQS queue for sending newly created conversions vip.
 *
 * <AUTHOR>
 */
public class NewlyCreatedConversionsVipSenderQueue extends SimpleQueueServiceQueue {

    @Inject @NewlyCreatedConversionsVipSenderQueueNameResolver @Getter(PROTECTED)
    private String partialQueueName;
}
