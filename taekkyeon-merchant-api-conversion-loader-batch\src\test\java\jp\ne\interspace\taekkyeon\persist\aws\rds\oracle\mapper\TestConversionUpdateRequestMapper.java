/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.ConversionUpdateRequestTest;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Integration test for {@link ConversionUpdateRequestMapper}.
 *
 * <AUTHOR> Van
 */
public interface TestConversionUpdateRequestMapper {

    /**
        SELECT
            file_name fileName,
            error_count errorCount,
            campaign_id campaignId,
            staff_email staffEmail,
            data_count dataCount
        FROM
            conversion_update_request
        WHERE
            file_name = #{fileName}
     */
    @Multiline String SELECT_CONVERSION_UPDATE_REQUEST = "";

    @Select(SELECT_CONVERSION_UPDATE_REQUEST)
    @ConstructorArgs({ @Arg(column = "fileName", javaType = String.class),
            @Arg(column = "errorCount", javaType = int.class),
            @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "staffEmail", javaType = String.class),
            @Arg(column = "dataCount", javaType = int.class)})
    ConversionUpdateRequestTest findConversionUpdateRequestBy(
            @Param("fileName") String fileName);
}
