/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.Map;

import com.google.common.collect.ImmutableMap;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.util.TaekkyeonHttpClient;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link SlackServiceTest}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SlackServiceTest {

    private static final String MESSAGE_FORMAT = "%S test %s";
    private static final String ELEMENT_1 = "element1";
    private static final String ELEMENT_2 = "element2";
    private static final String MESSAGE = "element1 test element2";

    @InjectMocks @Spy
    private SlackService underTest;

    @Mock
    private TaekkyeonHttpClient httpClient;

    @Test
    public void testSendShouldReturnCorrectValueWhenCalled()
            throws Exception {
        // given
        String slackUrl = "slackUrl";

        doReturn(slackUrl).when(underTest).getSlackUrl();
        doReturn(MESSAGE).when(underTest).createMessage(MESSAGE_FORMAT, ELEMENT_1,
                ELEMENT_2);
        Map<String, String> header = ImmutableMap.of("Content-Type", "application/text");
        Map<String, String> requestBody = ImmutableMap.of("text", MESSAGE);
        when(httpClient.post(slackUrl, header, requestBody)).thenReturn("");

        // when
        underTest.send(MESSAGE_FORMAT, ELEMENT_1, ELEMENT_2);

        // then
        verify(httpClient).post(slackUrl, header, requestBody);
    }

    @Test
    public void testSendShouldReturnCorrectValueWhenHaveOnlyMessage()
            throws Exception {
        // given
        String slackUrl = "slackUrl";

        doReturn(slackUrl).when(underTest).getSlackUrl();
        Map<String, String> header = ImmutableMap.of("Content-Type", "application/text");
        Map<String, String> requestBody = ImmutableMap.of("text", MESSAGE);
        when(httpClient.post(slackUrl, header, requestBody)).thenReturn("");

        // when
        underTest.send(MESSAGE);

        // then
        verify(httpClient).post(slackUrl, header, requestBody);
    }

    @Test
    public void testCreateMessageShouldReturnCorrectValueWhenCalled() {
        // given
        String messageFormat = "%s test %s";

        // when
        String actual = underTest.createMessage(messageFormat, ELEMENT_1, ELEMENT_2);

        // then
        assertEquals("element1 test element2", actual);
    }
}
