/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding all the affiliate marketing knowledge level.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum AffiliateMarketingKnowledgeLevel implements ValueEnum {

    BEGINNER(0),
    AMATEUR(1),
    EXPERIENCED(2);

    private final int value;
}
