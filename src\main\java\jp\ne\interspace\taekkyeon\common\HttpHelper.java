/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Map;
import java.util.Map.Entry;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;

import static com.amazonaws.HttpMethod.GET;
import static com.amazonaws.HttpMethod.POST;
import static java.nio.charset.StandardCharsets.UTF_8;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.PIPE;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.FOUND;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.MOVED_PERMANENTLY;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.TEMPORARY_REDIRECT;

/**
 * Convenience class for HTTP request.
 *
 * <AUTHOR> Shin
 */
@Singleton @Slf4j
public class HttpHelper {

    private static final int DEFAULT_TIMEOUT = 3000;

    private static final String HTTP_HEADER_CONTENT_TYPE_JSON = "application/json";

    /**
     * Returns the {@link URI} by the given URL with query parameters.
     *
     * @param baseUrl
     *          base URL
     * @param parameters
     *          additional parameters of URL
     * @return the {@link URI} by the given URL with query parameters
     * @throws URISyntaxException when a problem occurs
     */
    public URI createUri(String baseUrl, Map<String, String> parameters)
            throws URISyntaxException {
        try {
            URL url = new URL(baseUrl);
            URI uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(),
                    url.getPort(), url.getPath(), url.getQuery(), url.getRef());
            return addParameters(new URIBuilder(uri), parameters).build();
        } catch (Exception e) {
            return addParameters(new URIBuilder("").setPath(baseUrl), parameters).build();
        }
    }

    /**
     * Sends the HTTP {@code GET} request composed with the given {@link URI} and the
     * {@code userAgent} and returns the responded HTTP status code.
     *
     * @param url
     *          {@code URL} for sending HTTP request
     * @param userAgent
     *          a string value for {@code User-Agent} HTTP header
     * @param siteId
     *          the given site ID
     * @return a HTTP status code resulted from the HTTP request
     * @throws IOException when a problem occurs
     */
    public String sendGetRequest(String url, String userAgent, long siteId) throws IOException {
        URL httpUrl = new URL(url);
        HttpURLConnection connection = null;
        try {
            connection = createHttpConnectionTo(httpUrl, userAgent);
            connection.connect();
            int responseCode = connection.getResponseCode();
            InputStream errorStream = connection.getErrorStream();
            String errorDetails = errorStream != null ? IOUtils.toString(errorStream)
                    : EMPTY;
            logResponse(url, responseCode, errorDetails, siteId);
            return createCustomResponse(responseCode, errorDetails);
        } catch (Exception e) {
            getLogger().error(
                    String.format("Sending GET request to URL[%s] is failed", url), e);
            throw e;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * Sends the HTTP {@code POST} request composed with the given {@link URI}, the
     * {@code authorization} and the {@code requestBody} and returns the responded HTTP
     * status code.
     *
     * @param url
     *          {@code URL} for sending HTTP request
     * @param authorization
     *          the authorization for sending HTTP request header
     * @param requestBody
     *          the request body for sending HTTP request
     * @return a HTTP status code resulted from the HTTP request
     * @throws IOException IOException when a problem occurs
     */
    public String sendPostRequest(String url, String authorization, String requestBody)
            throws IOException {
        URL httpUrl = new URL(url);
        HttpURLConnection connection = null;
        try {
            connection = createHttpPostConnectionTo(httpUrl, authorization, requestBody);
            connection.connect();
            int responseCode = connection.getResponseCode();
            InputStream errorStream = connection.getErrorStream();
            String errorDetails = errorStream != null
                    ? IOUtils.toString(errorStream, UTF_8) : EMPTY;
            logPostResponse(url, requestBody, responseCode, errorDetails);
            return createCustomResponse(responseCode, errorDetails);
        } catch (Exception e) {
            getLogger().error(String.format(
                    "Sending POST request to URL[%s] is failed(requestBody:%s)", url,
                    requestBody), e);
            throw e;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * Sends the HTTP request composed with the given {@link URI},
     * returns the final url.
     *
     * @param url
     *          {@code URL} for sending HTTP request
     * @return the final url
     */
    public String getFinalUrlBy(String url) {
        HttpURLConnection connection = null;
        try {
            URL httpUrl = new URL(url);
            connection = createConnectionTo(httpUrl);
            connection.connect();
            if (isRedirect(connection.getResponseCode())) {
                return getFinalUrlBy(connection.getHeaderField("Location"));
            }
        } catch (Exception e) {
            getLogger().error(String.format("Failed to access the URL: URL = [%s]", url),
                    e);
            return EMPTY;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        return url;
    }

    @VisibleForTesting
    HttpURLConnection createHttpConnectionTo(URL httpUrl, String userAgent)
            throws IOException, ProtocolException {
        HttpURLConnection connection = (HttpURLConnection) httpUrl.openConnection();
        connection.setRequestMethod(GET.name());
        connection.addRequestProperty(HttpHeaders.USER_AGENT, userAgent);
        connection.setReadTimeout(DEFAULT_TIMEOUT);
        connection.setConnectTimeout(DEFAULT_TIMEOUT);
        return connection;
    }

    @VisibleForTesting
    HttpURLConnection createHttpPostConnectionTo(URL httpUrl, String authorization,
            String requestBody) throws IOException, ProtocolException {
        HttpURLConnection connection = (HttpURLConnection) httpUrl.openConnection();
        connection.setRequestMethod(POST.name());
        connection.setRequestProperty(HttpHeaders.CONTENT_TYPE,
                HTTP_HEADER_CONTENT_TYPE_JSON);
        connection.setRequestProperty("Accept", HTTP_HEADER_CONTENT_TYPE_JSON);
        connection.setRequestProperty(HttpHeaders.AUTHORIZATION, authorization);
        connection.setReadTimeout(DEFAULT_TIMEOUT);
        connection.setConnectTimeout(DEFAULT_TIMEOUT);
        connection.setDoOutput(true);
        setRequestBody(connection, requestBody);
        return connection;
    }

    @VisibleForTesting
    void logPostResponse(String url, String requestBody, int responseCode,
            String responseErrorDetails) {
        if (responseCode == HttpStatus.SC_OK) {
            getLogger().info(String.format(
                    "Successfully sent POST request to URL [%s] (requestBody:%s)",
                    url, requestBody));
        } else {
            String errorFormat =
                    "Sending POST request to URL[{}]([{}]) is failed with error code[{}]";
            if (responseErrorDetails.isEmpty()) {
                getLogger().error(errorFormat, url, requestBody, responseCode);
            } else {
                getLogger().error(errorFormat + " and error message[{}]", url,
                        requestBody, responseCode, responseErrorDetails);
            }
        }
    }

    @VisibleForTesting
    HttpURLConnection createConnectionTo(URL httpUrl) throws IOException {
        HttpURLConnection connection = (HttpURLConnection) httpUrl.openConnection();
        connection.setInstanceFollowRedirects(false);
        connection.setReadTimeout(DEFAULT_TIMEOUT);
        connection.setConnectTimeout(DEFAULT_TIMEOUT);
        return connection;
    }

    @VisibleForTesting
    boolean isRedirect(int statusCode) {
        return statusCode == MOVED_PERMANENTLY.getStatusCode()
                || statusCode == FOUND.getStatusCode()
                || statusCode == TEMPORARY_REDIRECT.getStatusCode();
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    private URIBuilder addParameters(URIBuilder uriBuilder, Map<String, String> parameters) {
        if (parameters != null) {
            for (Entry<String, String> entry : parameters.entrySet()) {
                uriBuilder.addParameter(entry.getKey(), entry.getValue());
            }
        }
        return uriBuilder;
    }

    private String createCustomResponse(Object... objects) {
        return Joiner.on(PIPE).join(objects);
    }

    private void logResponse(String url, int responseCode, String errorStream,
            long siteId) {
        if (responseCode == HttpStatus.SC_OK) {
            getLogger().info("Successfully sent GET request to siteId [{}] to URL [{}]",
                    siteId, url);
        } else {
            String errorFormat = "Sending postback request to site [{}]"
                    + " by URL[{}] is failed with error code[{}]";
            if (!errorStream.isEmpty()) {
                String errorFormatWithErrorStream = Joiner.on(EMPTY).join(errorFormat,
                        " and error message[{}]");
                getLogger().error(errorFormatWithErrorStream, siteId, url,
                        responseCode, errorStream);
            } else {
                getLogger().error(errorFormat, siteId, url, responseCode);
            }
        }
    }

    private void setRequestBody(HttpURLConnection connection, String body)
            throws IOException {
        try (OutputStream outputStream = connection.getOutputStream()) {
            byte[] input = body.getBytes(UTF_8);
            outputStream.write(input, 0, input.length);
        }
    }
}
