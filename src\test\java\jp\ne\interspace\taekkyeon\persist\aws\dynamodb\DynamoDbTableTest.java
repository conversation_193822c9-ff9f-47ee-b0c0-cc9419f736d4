/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.dynamodb;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.UnaryOperator;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.AttributeValueUpdate;
import com.amazonaws.services.dynamodbv2.model.BatchWriteItemRequest;
import com.amazonaws.services.dynamodbv2.model.BatchWriteItemResult;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.DeleteItemResult;
import com.amazonaws.services.dynamodbv2.model.PutItemResult;
import com.amazonaws.services.dynamodbv2.model.PutRequest;
import com.amazonaws.services.dynamodbv2.model.QueryRequest;
import com.amazonaws.services.dynamodbv2.model.QueryResult;
import com.amazonaws.services.dynamodbv2.model.UpdateItemRequest;
import com.amazonaws.services.dynamodbv2.model.UpdateItemResult;
import com.amazonaws.services.dynamodbv2.model.WriteRequest;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.DynamoDbTable.stringOf;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Answers.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link DynamoDbTable}.
 *
 * <AUTHOR> OBS DEV Team
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class DynamoDbTableTest {

    @Spy
    private DynamoDbTable underTest;

    @Mock(answer = RETURNS_DEEP_STUBS)
    private DynamoDbClient dynamoDbClient;

    @Mock
    private UnaryOperator<String> nameResolver;

    @Test
    public void testSetUpForTestShouldCallCreateTableWhenCalled() {
        // given
        CreateTableRequest request = mock(CreateTableRequest.class);

        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        doReturn(request).when(underTest).getTestSchema();

        // when
        underTest.setUpForTest();

        // then
        verify(dynamoDbClient).createTable(request);
    }

    @Test
    public void testTearDownForTestShouldCallDeleteTableWhenCalled() {
        // given
        String table = "someTable";

        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        doReturn(table).when(underTest).getTableName();

        // when
        underTest.tearDownForTest();

        // then
        verify(dynamoDbClient).deleteTable(table);
    }

    @Test
    public void testFetchByShouldReturnValidOptionalMapWhenSuccessfullyFetchedResultFromDynamoDbClient() {
        // given
        String table = "una_mesa";
        String key = "una_llave";
        Map<String, AttributeValue> map = emptyMap();
        Map<String, AttributeValue> result = emptyMap();
        Optional<Map<String, AttributeValue>> expected = Optional.of(result);

        doReturn(table).when(underTest).getTableName();
        doReturn(map).when(underTest).getMapOf(key);

        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        when(dynamoDbClient.getItem(table, map).getItem()).thenReturn(result);

        // when
        Optional<Map<String, AttributeValue>> actual = underTest.fetchBy(key);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testFetchByShouldReturnEmptyOptionalWhenFailedToFetchResultFromDynamoDbClient() {
        // given
        String table = "una_mesa";
        String key = "una_llave";
        Map<String, AttributeValue> map = emptyMap();
        Optional<Map<String, AttributeValue>> expected = Optional.empty();

        doReturn(table).when(underTest).getTableName();
        doReturn(map).when(underTest).getMapOf(key);

        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        when(dynamoDbClient.getItem(table, map).getItem()).thenReturn(null);

        // when
        Optional<Map<String, AttributeValue>> actual = underTest.fetchBy(key);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testUpdateShouldCallPutItemWithCorrectParametersAndReturnPutItemResultWhenCalled() {
        // given
        String table = "una_mesa";
        String key = "una_llave";
        Map<String, AttributeValue> items = emptyMap();
        PutItemResult result = mock(PutItemResult.class);
        Optional<PutItemResult> expected = Optional.of(result);

        doReturn(table).when(underTest).getTableName();
        doReturn(items).when(underTest).getMapOf(key);

        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        when(dynamoDbClient.putItem(table, items)).thenReturn(result);

        // when
        Optional<PutItemResult> actual = underTest.update(key, items);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testUpdateShouldCorrectlyMergeTheGivenItemsWithTheKeyMapAndReturnPutItemWhenGivenItemsAndKey() {
        // given
        String table = "una_mesa";
        String keyName = "una_llave";
        String keyValue = "es_mia";
        String itemName = "vatos";
        String itemValue = "locos";

        Map<String, AttributeValue> key = populateTestMapOf(keyName, keyValue);
        Map<String, AttributeValue> items = populateTestMapOf(itemName, itemValue);
        Map<String, AttributeValue> mergedItems = populateTestMapOf(keyName, keyValue,
                itemName, itemValue);

        PutItemResult result = mock(PutItemResult.class);
        Optional<PutItemResult> expected = Optional.of(result);

        doReturn(table).when(underTest).getTableName();
        doReturn(key).when(underTest).getMapOf(keyName);

        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        when(dynamoDbClient.putItem(table, mergedItems)).thenReturn(result);

        // when
        Optional<PutItemResult> actual = underTest.update(keyName, items);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test(expected = IllegalStateException.class)
    public void testUpdateShoulThrowIllegalStateExceptionWhenGivenKeyAndItemsInDuplicate() {
        // given
        String table = "una_mesa";
        String keyName = "una_llave";
        String keyValue = "es_mia";
        String itemName = "vatos";
        String itemValue = "locos";

        Map<String, AttributeValue> key = populateTestMapOf(keyName, keyValue);
        Map<String, AttributeValue> itemsWithDuplication = populateTestMapOf(keyName,
                keyValue, itemName, itemValue);

        doReturn(table).when(underTest).getTableName();
        doReturn(key).when(underTest).getMapOf(keyName);

        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);

        // when
        underTest.update(keyName, itemsWithDuplication);
    }

    @Test
    public void testUpdateItemShouldCallUpdateItemWithCorrectParametersAndReturnUpdateItemResultWhenCalled() {
        // given
        Map<String, AttributeValue> key = emptyMap();
        Map<String, AttributeValueUpdate> updates = Collections
                .<String, AttributeValueUpdate>emptyMap();
        String table = "someTable";
        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        doReturn(table).when(underTest).getTableName();
        UpdateItemResult result = mock(UpdateItemResult.class);
        ArgumentCaptor<UpdateItemRequest> updateItemRequestCaptor = ArgumentCaptor
                .forClass(UpdateItemRequest.class);
        when(dynamoDbClient.updateItem(updateItemRequestCaptor.capture()))
                .thenReturn(result);

        // when
        Optional<UpdateItemResult> actual = underTest.updateItem(key, updates);

        // then
        assertNotNull(actual);
        UpdateItemRequest actualRequest = updateItemRequestCaptor.getValue();
        assertEquals(table, actualRequest.getTableName());
        assertEquals(key, actualRequest.getKey());
        assertEquals(updates, actualRequest.getAttributeUpdates());
    }

    @Test
    public void testBatchUpdateByShouldReturnRequestResultWhenGivenKeyItemMap() {
        // given
        String table = "una_mesa";
        String keyName = "una_llave";
        String keyValue = "es_mia";
        String itemName = "vatos";
        String itemValue = "locos";

        Map<String, AttributeValue> key = populateTestMapOf(keyName, keyValue);
        Map<String, Map<String, AttributeValue>> keyItemMap = populateTestMapOf(keyName,
                itemName, itemValue);

        BatchWriteItemRequest request = new BatchWriteItemRequest(ImmutableMap.of(table,
                populateTestListOf(keyName, keyValue, itemName, itemValue)));

        Optional<BatchWriteItemResult> expected = Optional.empty();

        Function<String, ImmutableMap<String, AttributeValue>> keyResolver =
                __ -> (ImmutableMap<String, AttributeValue>) key;

        doReturn(table).when(underTest).getTableName();
        doReturn(keyResolver).when(underTest).getKeyResolver();

        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        when(dynamoDbClient.batchWriteItem(request)).thenReturn(null);

        // when
        Optional<BatchWriteItemResult> actual = underTest.batchUpdateBy(keyItemMap);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testDeleteByShouldReturnEmptyOptionalWhenFailedToFetchResultFromDynamoDbClient() {
        // given
        String table = "una_mesa";
        String keyName = "una_llave";
        String keyValue = "es_mia";

        Map<String, AttributeValue> key = populateTestMapOf(keyName, keyValue);

        Optional<DeleteItemResult> expected = Optional.empty();

        doReturn(table).when(underTest).getTableName();
        doReturn(key).when(underTest).getMapOf(keyName);

        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        when(dynamoDbClient.deleteItem(table, key)).thenReturn(null);

        // when
        Optional<DeleteItemResult> actual = underTest.deleteBy(keyName);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testDeleteByShoulCallDeleteItemWithCorrectParametersAndReturnDeleteItemResultWhenGivenKey() {
        // given
        String table = "una_mesa";
        String keyName = "una_llave";
        String keyValue = "es_mia";

        Map<String, AttributeValue> key = populateTestMapOf(keyName, keyValue);

        DeleteItemResult result = mock(DeleteItemResult.class);
        Optional<DeleteItemResult> expected = Optional.of(result);

        doReturn(table).when(underTest).getTableName();
        doReturn(key).when(underTest).getMapOf(keyName);

        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        when(dynamoDbClient.deleteItem(table, key)).thenReturn(result);

        // when
        Optional<DeleteItemResult> actual = underTest.deleteBy(keyName);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test @SuppressWarnings("static-access")
    public void testNumberOfShouldReturnAttributeValueWhenGivenNumericValue() {
        // given
        String value = "310";
        AttributeValue expected = new AttributeValue().withN(value);

        // when
        AttributeValue actual = underTest.numberOf(value);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test @SuppressWarnings("static-access")
    public void testNumberOfShouldReturnNumericStringWhenGivenNumericAttributeValueSetAsString() {
        // given
        String expected = "213";
        AttributeValue value = new AttributeValue().withS(expected);

        // when
        String actual = underTest.numberOf(value);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test @SuppressWarnings("static-access")
    public void testNumberOfShouldReturnNumericStringWhenGivenNumericAttributeValueSetAsNumber() {
        // given
        String expected = "213";
        AttributeValue value = new AttributeValue().withN(expected);

        // when
        String actual = underTest.numberOf(value);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test @SuppressWarnings("static-access")
    public void testStringOfShouldReturnAttributeValueWhenGivenStringValue() {
        // given
        String value = "la_ciudad_de_angeles";
        AttributeValue expected = new AttributeValue(value);

        // when
        AttributeValue actual = underTest.stringOf(value);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test @SuppressWarnings("static-access")
    public void testMapAttributeOfOfShouldReturnMapAttributeWhenGivenAttributeMap() {
        // given
        ImmutableMap<String, AttributeValue> attributeMap = ImmutableMap.of("foo",
                new AttributeValue("bar"));
        AttributeValue expected = new AttributeValue().withM(attributeMap);

        // when
        AttributeValue actual = underTest.mapAttributeOf(attributeMap);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    @SuppressWarnings("static-access")
    public void testMapOfShouldReturnAttributeMapWhenGivenMapAttribute() {
        // given
        Map<String, AttributeValue> map = ImmutableMap.of("foo",
                new AttributeValue("bar"));
        AttributeValue attributeMap = new AttributeValue().withM(map);
        Optional<Map<String, AttributeValue>> expected = Optional.of(map);

        // when
        Optional<Map<String, AttributeValue>> actual = underTest.mapOf(attributeMap);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateItemWithShouldReturnMergedMapWhenGivenKeyAndItems() {
        // given
        String keyName = "una_llave";
        String keyValue = "es_mia";
        String itemName = "vatos";
        String itemValue = "locos";

        Map<String, AttributeValue> key = populateTestMapOf(keyName, keyValue);
        Map<String, AttributeValue> items = populateTestMapOf(itemName, itemValue);
        Map<String, AttributeValue> expected = populateTestMapOf(keyName, keyValue,
                itemName, itemValue);

        Function<String, ImmutableMap<String, AttributeValue>> keyResolver =
                __ -> (ImmutableMap<String, AttributeValue>) key;

        doReturn(keyResolver).when(underTest).getKeyResolver();

        // when
        Map<String, AttributeValue> actual = underTest.createItemWith(keyValue, items);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateRequestsFromShouldReturnRequestsWhenGivenKeyItemMap() {
        // given
        String table = "una_mesa";
        String keyName = "una_llave";
        String keyValue = "es_mia";
        String itemName = "vatos";
        String itemValue = "locos";

        Map<String, AttributeValue> key = populateTestMapOf(keyName, keyValue);
        Map<String, Map<String, AttributeValue>> keyItemMap = populateTestMapOf(keyName,
                itemName, itemValue);

        List<WriteRequest> requestList = populateTestListOf(keyName, keyValue, itemName,
                itemValue);

        Optional<Map<String, List<WriteRequest>>> expected = Optional
                .of(ImmutableMap.of(table, requestList));

        Function<String, ImmutableMap<String, AttributeValue>> keyResolver =
                __ -> (ImmutableMap<String, AttributeValue>) key;

        doReturn(table).when(underTest).getTableName();
        doReturn(keyResolver).when(underTest).getKeyResolver();

        // when
        Optional<Map<String, List<WriteRequest>>> actual = underTest
                .createRequestsFrom(keyItemMap);

        // then
        assertEquals(expected, actual);
    }

    @Test @SuppressWarnings("unchecked")
    public void testQueryShouldReturnCorrectDataWhenCalled() {
        // given
        String partitionKeyName = "partitionKeyName";
        String partitionKeyValue = "partitionKeyValue";
        Pair<String, AttributeValue> partitionKey = ImmutablePair.of(partitionKeyName,
                new AttributeValue(partitionKeyValue));
        String indexName = "NAME-index";
        String valueExpression = ":value";
        String tableName = "TABLE_NAME";
        String keyName = "keyName";
        String keyValue = "keyValue";
        ArgumentCaptor<QueryRequest> requestCaptor = ArgumentCaptor
                .forClass(QueryRequest.class);
        doReturn(tableName).when(underTest).getTableName();
        Map<String, AttributeValue> resultAttribute = ImmutableMap
                .copyOf(populateTestMapOf(keyName, keyValue));
        QueryResult result = new QueryResult();
        result.withItems(resultAttribute);
        when(underTest.getDynamoDbClient()).thenReturn(dynamoDbClient);
        when(dynamoDbClient.queryItem(requestCaptor.capture())).thenReturn(result);

        // when
        Optional<Map<String, AttributeValue>> actual = underTest.query(
                valueExpression, partitionKey, indexName);

        // then
        assertEquals(Optional.ofNullable(resultAttribute), actual);
        QueryRequest request = requestCaptor.getValue();
        assertNotNull(request);
        assertSame(tableName, request.getTableName());
        assertSame(indexName, request.getIndexName());
        assertEquals("partitionKeyName = :value", request.getKeyConditionExpression());
        assertEquals(ImmutableMap.of(valueExpression, partitionKey.getValue()),
                request.getExpressionAttributeValues());
    }

    private Map<String, AttributeValue> emptyMap() {
        return Collections.<String, AttributeValue>emptyMap();
    }

    private Map<String, AttributeValue> populateTestMapOf(String firstKey,
            String firstValue, String secondKey, String secondValue) {
        return ImmutableMap.of(firstKey, attributeOf(firstValue),
                secondKey, attributeOf(secondValue));
    }

    private Map<String, AttributeValue> populateTestMapOf(String keyName,
            String keyValue) {
        return ImmutableMap.of(keyName, attributeOf(keyValue));
    }

    private Map<String, Map<String, AttributeValue>> populateTestMapOf(String keyName,
            String itemName, String itemValue) {
        return ImmutableMap.of(keyName, ImmutableMap.of(itemName, stringOf(itemValue)));
    }

    private List<WriteRequest> populateTestListOf(String keyName, String keyValue,
            String itemName, String itemValue) {
        return Arrays.asList(new WriteRequest(new PutRequest(
                populateTestMapOf(keyName, keyValue, itemName, itemValue))));
    }

    private AttributeValue attributeOf(String value) {
        return new AttributeValue().withS(value);
    }

}
