/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import static java.math.BigDecimal.ZERO;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.PENDING;

/**
 * DTO for holding conversion registration details.
 *
 * <AUTHOR> Shin
 */
@Getter @AllArgsConstructor @EqualsAndHashCode @ToString
public class ConversionRegistrationDetails {

    private final ZonedDateTime conversionTime;
    private final String transactionId;
    private final int resultId;
    private final String customerType;
    private final String productCategoryId;
    private final String productId;
    private final int productQuantity = 1;
    private final BigDecimal productUnitPrice;
    private final BigDecimal discount = ZERO;
    private final ConversionStatus status = PENDING;
    private final String clickId;
}
