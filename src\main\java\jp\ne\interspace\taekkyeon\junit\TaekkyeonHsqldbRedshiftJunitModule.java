/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.junit;

import com.google.inject.Exposed;
import com.google.inject.Provides;

import jp.ne.interspace.taekkyeon.module.RedshiftResolver;

import static jp.ne.interspace.taekkyeon.module.DatabaseType.REDSHIFT;

/**
 * Taekkyeon module for JUnit HSQLDB data source.
 *
 * <AUTHOR>
 */
@RedshiftResolver
public class TaekkyeonHsqldbRedshiftJunitModule extends TaekkyeonHsqldbJunitModule {

    private static final String INTEGRATION_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH =
            "database/hsqldb/redshift-integration-test-schema.sql";
    private static final String INTEGRATION_TEST_HSQLDB_DATA_SQL_FILE_PATH =
            "database/hsqldb/redshift-integration-test-data.sql";
    private static final String UNIT_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH =
            "database/hsqldb/redshift-schema.sql";
    private static final String UNIT_TEST_HSQLDB_DATA_SQL_FILE_PATH =
            "database/hsqldb/redshift-test-data.sql";

    public TaekkyeonHsqldbRedshiftJunitModule() {
        super(REDSHIFT);
    }

    @Provides @Exposed @RedshiftResolver
    private HsqldbSqlFilePath provideHsqldbSqlFilePath() {
        return new HsqldbSqlFilePath(UNIT_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH,
                UNIT_TEST_HSQLDB_DATA_SQL_FILE_PATH,
                INTEGRATION_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH,
                INTEGRATION_TEST_HSQLDB_DATA_SQL_FILE_PATH);
    }
}
