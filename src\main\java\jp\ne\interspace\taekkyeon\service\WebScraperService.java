/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.File;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import io.github.bonigarcia.wdm.WebDriverManager;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.Point;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Wait;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.common.HttpHelper;
import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.model.AdsScraper;
import jp.ne.interspace.taekkyeon.model.GoogleAd;
import jp.ne.interspace.taekkyeon.model.Location;
import jp.ne.interspace.taekkyeon.model.Pair;
import jp.ne.interspace.taekkyeon.module.ChromeDriverOptionsResolver;
import jp.ne.interspace.taekkyeon.module.ChromeDriverVersionResolver;
import jp.ne.interspace.taekkyeon.module.GoogleAdsScraperBlacklistUrlsResolver;
import jp.ne.interspace.taekkyeon.module.GoogleAdsScraperDomainNamesResolver;
import jp.ne.interspace.taekkyeon.module.GoogleAdsScraperNumberOfRedirectsResolver;
import jp.ne.interspace.taekkyeon.module.ScrapeElementTypeResolver;
import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceBucket;
import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceClient;
import jp.ne.interspace.taekkyeon.util.TaekkyeonHttpClient;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.CHROME_WEB_DRIVER_VERSION;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DEFAULT_LANGUAGE;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DEFAULT_USER_AGENT;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonPropertiesModule.BIND_KEY_CAPTURED_SCREENSHOT;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonPropertiesModule.BIND_KEY_CAPTURED_SCREENSHOT_ONLY_WHEN_SEARCHING;
import static lombok.AccessLevel.PACKAGE;

/**
 * Service layer for handling web scraper.
 *
 * <AUTHOR>
 */
@Singleton @Slf4j
public class WebScraperService {

    private static final String ATNCT3_PARAMETER = "atnct3=";
    private static final String GOOGLE_SEARCH_PATH = "/search?q=";

    private static final String WWW = "www";

    private static final String GOOGLE_TRACKING_TAG_ATTRIBUTE_NAME = "data-rw";
    private static final String GOOGLE_HREF_TAG_ATTRIBUTE_NAME = "href";

    private static final int RANDOM_SEARCH_WORD_LENGTH = 7;
    private static final int PAGE_NUMBER_1 = 1;
    private static final int PAGE_NUMBER_2 = 2;

    private static final int AD_HEADLINE_MAX_BYTE_LENGTH = 256;
    private static final int AD_DESCRIPTION_MAX_BYTE_LENGTH = 512;
    private static final int COUNTRY_MAX_BYTE_LENGTH = 64;
    private static final int CITY_MAX_BYTE_LENGTH = 128;
    private static final int TRACKING_TEMPLATE_MAX_BYTE_LENGTH = 64;

    private static final long WAIT_TIME_TO_LOAD = 3000;
    private static final long WAIT_TIME_TO_RE_TRY = 1000;

    private static final String FOOTER = "FOOTER";
    private static final String DESKTOP_LOCATION_UPDATE = "DESKTOP_LOCATION_UPDATE";
    private static final String SMARTPHONE_LOCATION_UPDATE = "SMARTPHONE_LOCATION_UPDATE";
    private static final String FOOTER_LOCATION = "FOOTER_LOCATION";
    private static final String SEARCH = "SEARCH";
    private static final String COUNTRY = "COUNTRY";
    private static final String SMARTPHONE_CITY = "SMARTPHONE_CITY";
    private static final String DESKTOP_CITY = "DESKTOP_CITY";
    private static final String ADS = "ADS";
    private static final String AD_HEADING = "AD_HEADING";
    private static final String AD_DESCRIPTION = "AD_DESCRIPTION";
    private static final String AD_TAG = "AD_TAG";
    private static final String AD_DOMAIN = "AD_DOMAIN";
    private static final String AD_DOMAIN_SPAN = "AD_DOMAIN_SPAN";
    private static final String SMARTPHONE_PAGE_2 = "SMARTPHONE_PAGE_2";
    private static final String DESKTOP_PAGE_2 = "DESKTOP_PAGE_2";

    private static final String KEY = "key";
    private static final String TYPE = "type";

    private static final String ID = "ID";
    private static final String CLASS_NAME = "CLASS_NAME";
    private static final String CSS_SELECTOR = "CSS_SELECTOR";
    private static final String NAME = "NAME";
    private static final String TAG_NAME = "TAG_NAME";
    private static final String LINK_TEXT = "LINK_TEXT";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    private static final int SCREENSHOT_WIDTH_SIZE = 860;
    private static final int SCREENSHOT_HEIGHT_SIZE = 1150;

    @Inject @ChromeDriverOptionsResolver @Getter(PACKAGE)
    private List<String> chromeDriverOptions;

    @Inject @GoogleAdsScraperBlacklistUrlsResolver @Getter(PACKAGE)
    private List<String> googleAdsScraperBlacklistUrls;

    @Inject @GoogleAdsScraperDomainNamesResolver @Getter(PACKAGE)
    private List<String> googleAdsScraperDomainNames;

    @Inject
    private TaekkyeonHttpClient taekkyeonHttpClient;

    @Inject
    private StringHelper stringHelper;

    @Inject
    private HttpHelper httpHelper;

    @Inject
    private SimpleStorageServiceBucket s3Bucket;

    @Inject
    private SimpleStorageServiceClient s3Client;

    @Inject @ChromeDriverVersionResolver @Getter(PACKAGE)
    private String chromeDriverVersion;

    @Inject @ScrapeElementTypeResolver @Getter(PACKAGE)
    private Map<String, Map<String,String>> scraperWebElementMapping;

    @Inject @GoogleAdsScraperNumberOfRedirectsResolver @Getter(PACKAGE)
    private int maximumNumberOfRedirects;

    @Inject(optional = true) @Named(BIND_KEY_CAPTURED_SCREENSHOT) @Getter(PACKAGE)
    private boolean isCapturedScreenshot;

    @Inject(optional = true) @Named(BIND_KEY_CAPTURED_SCREENSHOT_ONLY_WHEN_SEARCHING)
    @Getter(PACKAGE)
    private boolean isCapturedScreenshotOnlyWhenSearching;

    private Wait<WebDriver> wait;

    private ChromeDriver chromeDriver;

    @VisibleForTesting @Getter(PACKAGE)
    private final BiConsumer<By, Exception> logExceptionWithErrorLevel = (by, exception) -> {
        getLogger().error("Failed to find elements. By=" + by.toString(), exception);
    };

    @VisibleForTesting @Getter(PACKAGE)
    private final BiConsumer<By, Exception> logExceptionWithWarnLevel = (by, exception) -> {
        getLogger().warn("Failed to find elements. By=" + by.toString(), exception);
    };

    @VisibleForTesting @Getter(PACKAGE)
    private final BiConsumer<By, Exception> skipException = (by, exception) -> {
    };

    /**
     * Scrape the google search result page.
     *
     * @param adsScraper
     *            the given {@link AdsScraper}
     * @return the result of screped google search
     * @throws InterruptedException
     *             throw exception when an error occurs
     */
    public List<GoogleAd> scrapeData(AdsScraper adsScraper) throws InterruptedException {
        String userAgent = adsScraper.getUserAgent();
        String googleUrl = adsScraper.getGoogleUrl();

        chromeDriver = initChromeDriver();
        wait = new FluentWait<WebDriver>(chromeDriver)
                .withTimeout(Duration.ofMillis(WAIT_TIME_TO_LOAD))
                .pollingEvery(Duration.ofMillis(WAIT_TIME_TO_RE_TRY));

        clearCoockies(chromeDriver);
        grantGeolocationPermission(googleUrl);
        updateLocation(adsScraper.getLocation());
        updateUserAgentAndLanguage(userAgent, adsScraper.getLanguage());
        setSearchPath(chromeDriver, createGoogleUrlToSearchRandomKeyword(googleUrl));
        waitLoadTime();
        WebElement locationElement = findLocationElement(chromeDriver);
        if (!isNull(locationElement)) {
            boolean isMobile = adsScraper.isMobile();
            if (isKeywordSearchable(chromeDriver, adsScraper.getKeyword())) {
                By by = getLocatorBy(FOOTER_LOCATION);
                locationElement = findWebElement(chromeDriver, by,
                        getLogExceptionWithErrorLevel());
                if (!isNull(locationElement)) {
                    String country = findCountryFrom(locationElement);
                    String city = isMobile ? findCity(chromeDriver)
                            : findCityFrom(locationElement);
                    return findGoogleAds(chromeDriver, country, city, userAgent,
                            isMobile);
                }
            }
        }
        return Collections.emptyList();
    }

    /**
     * Closes the {@link ChromeDriver}.
     */
    public void closeChromeDriver() {
        if (chromeDriver != null) {
            chromeDriver.quit();
        }
    }

    @VisibleForTesting
    WebElement getFooterElement() {
        By by = getLocatorBy(FOOTER);
        return findWebElement(chromeDriver, by, getSkipException());
    }

    /**
     * Upload screenshot file to specified s3.
     *
     * @param screenshotFile
     *      captured screen
     * @return uploaded s3 url
     */
    public String uploadScreenshot(File screenshotFile) {
        if (screenshotFile != null) {
            String fileName = Joiner.on(".").join(getCurrentTime(), stringHelper
                    .generateRandomAlphanumericString(RANDOM_SEARCH_WORD_LENGTH), "png");
            s3Bucket.putPublicReadObject(fileName, screenshotFile);
            return s3Client.getUrl(s3Bucket.getBucketName(), fileName);
        }
        return EMPTY;
    }

    /**
     * Check url contain accesstrade domain.
     *
     * @param url
     *      redirect url string
     * @return boolean value of checking
     */
    public boolean isContainAccessTradeDomain(String url) {
        return Optional.ofNullable(getGoogleAdsScraperDomainNames())
                .orElseGet(Collections::emptyList).stream().anyMatch(url::contains);
    }

    @VisibleForTesting
    ChromeDriver initChromeDriver() {
        if (chromeDriver == null) {
            chromeDriver = new ChromeDriver(createChromeOptions());
            chromeDriver.manage().window().setPosition(new Point(0, 0));
            chromeDriver.manage().window().setSize(new Dimension(SCREENSHOT_WIDTH_SIZE,
                    SCREENSHOT_HEIGHT_SIZE));
        }
        return chromeDriver;
    }

    @VisibleForTesting
    void clearCoockies(WebDriver driver) {
        driver.manage().deleteAllCookies();
    }

    @VisibleForTesting
    void grantGeolocationPermission(String googleUrl) {
        Map<String, Object> permission = new HashMap<String, Object>();
        String[] permissionValues = new String[] { "geolocation" };
        permission.put("origin", googleUrl);
        permission.put("permissions", permissionValues);
        chromeDriver.executeCdpCommand("Browser.grantPermissions", permission);
    }

    @VisibleForTesting
    void updateLocation(Location location) {
        Map<String, Object> coordinates = new HashMap<String, Object>();
        coordinates.put("latitude", location.getLatitude());
        coordinates.put("longitude", location.getLongitude());
        coordinates.put("accuracy", 1);
        chromeDriver.executeCdpCommand("Emulation.setGeolocationOverride", coordinates);
    }

    @VisibleForTesting
    void updateUserAgentAndLanguage(String userAgent, String language) {
        Map<String, Object> userAgentLanguageParameters = new HashMap<String, Object>();
        userAgentLanguageParameters.put("userAgent", userAgent);
        userAgentLanguageParameters.put("acceptLanguage", language);
        chromeDriver.executeCdpCommand("Network.setUserAgentOverride",
                userAgentLanguageParameters);
    }

    @VisibleForTesting
    String createGoogleUrlToSearchRandomKeyword(String googelUrl) {
        return googelUrl + GOOGLE_SEARCH_PATH + stringHelper
                .generateRandomAlphanumericString(RANDOM_SEARCH_WORD_LENGTH);
    }

    @VisibleForTesting
    void setSearchPath(ChromeDriver chromeDriver, String googleUrl) {
        chromeDriver.navigate().to(googleUrl);
    }

    @VisibleForTesting
    WebElement findLocationElement(ChromeDriver chromeDriver)
            throws InterruptedException {
        WebElement locationLinkElement = getLocationElementBy(chromeDriver);
        if (!isNull(locationLinkElement)) {
            waitTillElementToBeClickableAndClickBy(locationLinkElement);
            return findWebElement(chromeDriver, getLocatorBy(FOOTER_LOCATION),
                    getLogExceptionWithErrorLevel());
        }
        return null;
    }

    @VisibleForTesting
    WebElement getLocationElementBy(ChromeDriver chromeDriver) {
        WebElement locationLinkElement = findWebElement(chromeDriver,
                getLocatorBy(DESKTOP_LOCATION_UPDATE), getLogExceptionWithWarnLevel());
        if (!isNull(locationLinkElement)) {
            return locationLinkElement;
        }
        return findWebElement(chromeDriver, getLocatorBy(SMARTPHONE_LOCATION_UPDATE),
                getLogExceptionWithErrorLevel());
    }

    @VisibleForTesting
    WebElement findWebElement(ChromeDriver chromeDriver, By by,
            BiConsumer<By, Exception> exceptionHandler) {
        try {
            waitTillVisibilityOfAllElementsLocated(by);
            return chromeDriver.findElement(by);
        } catch (Exception e) {
            exceptionHandler.accept(by, e);
            return null;
        }
    }

    @VisibleForTesting
    String findCountryFrom(WebElement webElement) {
        WebElement countryElement = findWebElementFrom(webElement,
                getLocatorBy(COUNTRY));
        return isNull(countryElement) ? EMPTY
                : stringHelper.truncateToBytes(countryElement.getText(),
                        COUNTRY_MAX_BYTE_LENGTH);
    }

    @VisibleForTesting
    String findCity(ChromeDriver chromeDriver) {
        WebElement cityElement = findWebElement(chromeDriver,
                getLocatorBy(SMARTPHONE_CITY), getLogExceptionWithErrorLevel());
        return isNull(cityElement) ? EMPTY : findCityFrom(cityElement);
    }

    @VisibleForTesting
    String findCityFrom(WebElement webElement) {
        WebElement cityElement = findWebElementFrom(webElement,
                getLocatorBy(DESKTOP_CITY));
        return isNull(cityElement) ? EMPTY
                : stringHelper.truncateToBytes(cityElement.getText(),
                        CITY_MAX_BYTE_LENGTH);
    }

    @VisibleForTesting
    WebElement findWebElementFrom(WebElement webElement, By by) {
        try {
            return webElement.findElement(by);
        } catch (Exception e) {
            getLogger().error("Failed to find element from element. By=" + by.toString(),
                    e);
            return null;
        }
    }

    @VisibleForTesting
    boolean isKeywordSearchable(ChromeDriver chromeDriver, String keyword)
            throws InterruptedException {
        By searchLocator = getLocatorBy(SEARCH);
        if (searchLocator == null) {
            return false;
        }
        waitTillElementToBeClickableBy(searchLocator);
        WebElement searchWebElement = findWebElement(chromeDriver, searchLocator,
                getLogExceptionWithErrorLevel());
        if (!isNull(searchWebElement)) {
            searchWebElement.clear();
            searchWebElement.sendKeys(keyword);
            searchWebElement.submit();
            waitLoadTime();
            return true;
        }
        return false;
    }

    @VisibleForTesting
    List<GoogleAd> findGoogleAds(ChromeDriver chromeDriver, String country, String city,
            String userAgent, boolean isMobile) throws InterruptedException {
        List<WebElement> firstPageAdElements = findWebElements(chromeDriver,
                getLocatorBy(ADS), getLogExceptionWithWarnLevel());
        Pair<File, String> firstPageScreenshot = takeScreenshot(chromeDriver);
        List<GoogleAd> gooogleAds = firstPageAdElements.stream()
                .map(webElement -> findGoogleAdsFrom(webElement, country, city, userAgent,
                        isMobile, PAGE_NUMBER_1, firstPageScreenshot))
                .collect(Collectors.toList());
        List<GoogleAd> secondPageGoogleAds = getSecondPageGoogleAds(chromeDriver, country,
                city, userAgent, isMobile, firstPageAdElements.size());
        gooogleAds.addAll(secondPageGoogleAds);
        return gooogleAds;
    }

    @VisibleForTesting
    List<GoogleAd> getSecondPageGoogleAds(ChromeDriver chromeDriver,
            String country, String city, String userAgent, boolean isMobile,
            int firstPageAdElementsSize) throws InterruptedException {
        By by = isMobile ? getLocatorBy(SMARTPHONE_PAGE_2) : getLocatorBy(DESKTOP_PAGE_2);
        WebElement moreResultElement = findWebElement(chromeDriver, by,
                getLogExceptionWithErrorLevel());
        if (!isNull(moreResultElement)) {
            return getSecondPageGoogleAds(moreResultElement, chromeDriver, isMobile,
                    firstPageAdElementsSize, country, city, userAgent);
        }
        return Collections.emptyList();
    }

    @VisibleForTesting
    List<GoogleAd> getSecondPageGoogleAds(WebElement moreResultElement,
            ChromeDriver chromeDriver, boolean isMobile, int firstPageAdElementsSize,
            String country, String city, String userAgent) throws InterruptedException {
        waitTillElementToBeClickableAndClickBy(moreResultElement);
        waitLoadTime();
        Pair<File, String> secondPageScreenshot = takeScreenshot(chromeDriver);
        List<WebElement> secondPageAdElements = findWebElements(chromeDriver,
                getLocatorBy(ADS), getLogExceptionWithWarnLevel());
        if (isMobile && secondPageAdElements.size() >= firstPageAdElementsSize) {
            int index = 0;
            while (index != firstPageAdElementsSize) {
                secondPageAdElements.remove(0);
                index++;
            }
        }
        return secondPageAdElements.stream()
                .map(webElement -> findGoogleAdsFrom(webElement, country, city, userAgent,
                        isMobile, PAGE_NUMBER_2, secondPageScreenshot))
                .collect(Collectors.toList());
    }

    @VisibleForTesting
    List<WebElement> findWebElements(ChromeDriver chromeDriver, By by,
            BiConsumer<By, Exception> exceptionHandler) {
        try {
            waitTillVisibilityOfAllElementsLocated(by);
            return chromeDriver.findElements(by);
        } catch (Exception e) {
            exceptionHandler.accept(by, e);
            return Collections.emptyList();
        }
    }

    @VisibleForTesting
    GoogleAd findGoogleAdsFrom(WebElement adElement, String country, String city,
            String userAgent, boolean isMobile, int pageNumber,
            Pair<File, String> screenshot) {
        String googleTrackingUrl = findGoogleTrackingUrl(adElement);
        return new GoogleAd(getAdHeadingFrom(adElement), getAdDescriptionFrom(adElement),
                getFinalUrl(googleTrackingUrl, userAgent, 0), getDomainFrom(adElement),
                country, city, googleTrackingUrl, screenshot.getRight(),
                screenshot.getLeft(), pageNumber);
    }

    @VisibleForTesting
    String getAdHeadingFrom(WebElement adElement) {
        By by = getLocatorBy(AD_HEADING);
        WebElement adHeadingElement = findWebElementFrom(adElement, by);
        return !isNull(adHeadingElement) ? stringHelper.truncateToBytes(
                adHeadingElement.getText(), AD_HEADLINE_MAX_BYTE_LENGTH) : EMPTY;
    }

    @VisibleForTesting
    String getAdDescriptionFrom(WebElement adElement) {
        By by = getLocatorBy(AD_DESCRIPTION);
        WebElement adDescriptionElement = findWebElementFrom(adElement, by);
        return !isNull(adDescriptionElement) ? stringHelper.truncateToBytes(
                adDescriptionElement.getText(), AD_DESCRIPTION_MAX_BYTE_LENGTH) : EMPTY;
    }

    @VisibleForTesting
    String findGoogleTrackingUrl(WebElement adElement) {
        By by = getLocatorBy(AD_TAG);
        WebElement tag = findWebElementFrom(adElement, by);
        String googleTrackingUrl = tag.getAttribute(GOOGLE_TRACKING_TAG_ATTRIBUTE_NAME);
        if (googleTrackingUrl == null) {
            googleTrackingUrl = tag.getAttribute(GOOGLE_HREF_TAG_ATTRIBUTE_NAME);
        }
        return googleTrackingUrl;
    }

    @VisibleForTesting
    String getFinalUrl(String url, String userAgent, int numberOfRedirects) {
        if (url != null && !url.isEmpty()
                && numberOfRedirects < getMaximumNumberOfRedirects()) {
            if (getGoogleAdsScraperBlacklistUrls().contains(url) || url.contains(
                    ATNCT3_PARAMETER) || isContainAccessTradeDomain(url)) {
                return url;
            }
            try {
                String redirectUrl = taekkyeonHttpClient.redirect(url, userAgent);
                if (!EMPTY.equals(redirectUrl)) {
                    return getFinalUrl(redirectUrl, userAgent, numberOfRedirects + 1);
                }
            } catch (Exception e) {
                return httpHelper.getFinalUrlBy(url);
            }
            return url;
        }
        return EMPTY;
    }

    @VisibleForTesting
    String getDomainFrom(WebElement adElement) {
        By adDomain = getLocatorBy(AD_DOMAIN);
        WebElement adDomainElement = findWebElementFrom(adElement, adDomain);
        if (adDomainElement != null) {
            By adDomainSpan = getLocatorBy(AD_DOMAIN_SPAN);
            List<WebElement> adDomainSpanElements = findWebElementsFrom(adDomainElement,
                    adDomainSpan);
            if (isIncludedHost(adDomainSpanElements)) {
                String host = EMPTY;
                try {
                    URI uri = new URI(adDomainSpanElements.get(2).getText());
                    host = uri.getHost();
                    return host == null ? EMPTY
                            : stringHelper.truncateToBytes(host,
                                    TRACKING_TEMPLATE_MAX_BYTE_LENGTH);
                } catch (URISyntaxException e) {
                    getLogger().error(String.format(
                            "Failed to get domain. "
                                    + "adDomainSpanElements size is %d, host is %s",
                            adDomainSpanElements.size(), host), e);
                }
            }
        }
        return EMPTY;
    }

    @VisibleForTesting
    boolean isIncludedHost(List<WebElement> adDomainSpanElements) {
        return !adDomainSpanElements.isEmpty() && adDomainSpanElements.size() > 2
                && !isNull(adDomainSpanElements.get(2))
                && adDomainSpanElements.get(2).getText().contains(WWW);
    }

    @VisibleForTesting
    List<WebElement> findWebElementsFrom(WebElement webElement, By by) {
        try {
            return webElement.findElements(by);
        } catch (Exception e) {
            getLogger().error("Failed to find elements from element. By=" + by.toString(),
                    e);
            return Collections.emptyList();
        }
    }

    @VisibleForTesting
    void waitLoadTime() throws InterruptedException {
        long waitingTimer = WAIT_TIME_TO_LOAD;
        while (waitingTimer > 0) {
            chromeDriver.executeScript("window.scrollTo(0, document.body.scrollHeight)");
            sleepFor(WAIT_TIME_TO_RE_TRY);
            if (getFooterElement() != null) {
                waitingTimer = 0;
            }
            waitingTimer = waitingTimer - WAIT_TIME_TO_RE_TRY;
        }
        chromeDriver.executeScript("window.scrollTo(0, 0)");
    }

    @VisibleForTesting
    void sleepFor(long second) throws InterruptedException {
        Thread.sleep(second);
    }

    @VisibleForTesting
    void waitTillElementToBeClickableAndClickBy(WebElement webElement) {
        try {
            wait.until(ExpectedConditions.elementToBeClickable(webElement)).click();
        } catch (Exception e) {
            getLogger().error("Failed to check element to be clickable for "
                    + webElement.toString(), e);
        }
    }

    @VisibleForTesting
    void waitTillVisibilityOfAllElementsLocated(By by) {
        try {
            wait.until(ExpectedConditions.visibilityOfAllElementsLocatedBy(by));
        } catch (Exception e) {
            getSkipException().accept(by, e);
        }
    }

    @VisibleForTesting
    void waitTillElementToBeClickableBy(By by) {
        wait.until(ExpectedConditions.elementToBeClickable(by));
    }

    @VisibleForTesting
    By getLocatorBy(String element) {
        Map<String, String> mapping = getScraperWebElementMapping().get(element);
        if (mapping == null) {
            getLogger().info("mapping is null with element={}", element);
            return By.name("q");
        }
        String key = mapping.get(KEY);
        switch (mapping.get(TYPE)) {
            case ID:
                return By.id(key);
            case CLASS_NAME:
                return By.className(key);
            case CSS_SELECTOR:
                return By.cssSelector(key);
            case NAME:
                return By.name(key);
            case TAG_NAME:
                return By.tagName(key);
            case LINK_TEXT:
            default:
                return By.linkText(key);
        }
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    @VisibleForTesting
    Pair<File, String> takeScreenshot(WebDriver driver) {
        if (isCapturedScreenshot()) {
            try {
                TakesScreenshot takesScreenshot = getTakesScreenshot(driver);
                File screenshotFile = takesScreenshot.getScreenshotAs(OutputType.FILE);
                String screenshotImageUrl = EMPTY;
                if (isCapturedScreenshotOnlyWhenSearching()) {
                    screenshotImageUrl = uploadScreenshot(screenshotFile);
                }
                return Pair.of(screenshotFile, screenshotImageUrl);
            } catch (Exception e) {
                log.error("take_screenshot :: ERROR STORING SCREENSHOT", e);
            }
        }
        return Pair.of(null, EMPTY);
    }

    @VisibleForTesting
    TakesScreenshot getTakesScreenshot(WebDriver driver) {
        return ((TakesScreenshot) driver);
    }

    @VisibleForTesting
    String getCurrentTime() {
        return LocalDateTime.now().format(FORMATTER);
    }

    private ChromeOptions createChromeOptions() {
        WebDriverManager.chromedriver().driverVersion(
                (chromeDriverVersion == null || chromeDriverVersion.isEmpty())
                        ? CHROME_WEB_DRIVER_VERSION : chromeDriverVersion).setup();
        ChromeOptions options = new ChromeOptions();
        options.setExperimentalOption("excludeSwitches",
                new String[] { "enable-automation" });
        options.addArguments("--lang=" + DEFAULT_LANGUAGE);
        options.addArguments("--user-agent=" + DEFAULT_USER_AGENT);
        getChromeDriverOptions().stream().forEach(ChromeDriverOption -> {
            options.addArguments(ChromeDriverOption);
        });
        return options;
    }

    private boolean isNull(WebElement webElement) {
        return webElement == null;
    }
}
