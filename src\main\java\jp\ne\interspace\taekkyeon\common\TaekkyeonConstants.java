/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

import com.google.common.collect.ImmutableMap;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.module.Country;

import static com.google.common.base.Strings.isNullOrEmpty;
import static java.lang.String.format;
import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.MALAYSIA;
import static jp.ne.interspace.taekkyeon.module.Country.PHILIPPINES;
import static jp.ne.interspace.taekkyeon.module.Country.SINGAPORE;
import static jp.ne.interspace.taekkyeon.module.Country.TAIWAN;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Country.VIETNAM;

/**
 * Constants for Taekkyeon application.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonConstants {

    public static final String EMPTY = "";
    public static final String PIPE = "|";
    public static final String SPACE = " ";
    public static final String EQUAL = "=";
    public static final String DOLLAR = "$";
    public static final String HYPHEN = "-";
    public static final String COMMA = ",";
    public static final String DOT = ".";
    public static final String AMPERSAND_TOKEN = "##a##";
    public static final String EQUALITY_SIGN_TOKEN = "##e##";
    public static final String SLASH = "/";
    public static final String DOUBLE_QUOTE = "\"";
    public static final String UNDERSCORE = "_";
    public static final String UTF_8_WITH_BOM = "\uFEFF";
    public static final String COLON = ":";
    public static final String UNKNOWN = "unknown";

    public static final String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";
    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String DATE_FORMAT_YYYY_MM_DD_SLASH = "yyyy/MM/dd";
    public static final String DATE_FORMAT_YYYYMM = "yyyyMM";
    public static final String DATE_FORMAT_YYYY_MM_DD_HHMMSS = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_ISO_8061_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String ISO_8601_TIME_DESIGNATOR = "T";
    public static final String PROPERTY_ERROR_MESSAGE_FORMAT = "The property [%s] not found.";
    public static final String DATE_FORMAT_YYYY_SLASH_MM_SLASH_DD_HHMMSS = "yyyy/MM/dd HH:mm:ss";
    public static final String DATE_FORMAT_YYYY_SLASH_MM_SLASH_DDTHHMMSS = "yyyy/MM/dd'T'HH:mm:ss";
    public static final String DATE_FORMAT_MMM_SPACE_DD_SPACE_Y_POINT_HHMM = "MMM dd, y . HH:mm";
    public static final String TIME_FORMAT_HHMM = "HH:mm";
    public static final String DATE_FORMAT_YYYY_MM_DD_HH_MM = "yyyy-MM-dd-HH-mm";

    public static final String ISO_8601_FORMAT_PATTERN = "yyyy-MM-dd'T'HH:mm:ssZ";

    public static final DateTimeFormatter DATE_FORMAT_YYYY_MM_DD_FORMATTER = DateTimeFormatter
            .ofPattern(DATE_FORMAT_YYYY_MM_DD);
    public static final DateTimeFormatter DATE_FORMAT_YYYY_MM_DD_SLASH_FORMATTER =
            DateTimeFormatter.ofPattern(DATE_FORMAT_YYYY_MM_DD_SLASH);
    public static final DateTimeFormatter JSON_DATE_TIME_FORMATTER = DateTimeFormatter
            .ofPattern(ISO_8601_FORMAT_PATTERN);
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter
            .ofPattern(DATE_FORMAT_YYYY_MM_DD_HHMMSS);
    public static final DateTimeFormatter ISO_8061_DATE_TIME_FORMATTER = DateTimeFormatter
            .ofPattern(DATE_FORMAT_ISO_8061_YYYY_MM_DD_HH_MM_SS);
    public static final DateTimeFormatter DATE_TIME_FORMATTER_YYYY_SLASH_MM_SLASH_DD_HHMMSS =
                DateTimeFormatter.ofPattern(DATE_FORMAT_YYYY_SLASH_MM_SLASH_DD_HHMMSS);
    public static final DateTimeFormatter DATE_TIME_FORMATTER_YYYY_SLASH_MM_SLASH_DDTHHMMSS =
                DateTimeFormatter.ofPattern(DATE_FORMAT_YYYY_SLASH_MM_SLASH_DDTHHMMSS);
    public static final DateTimeFormatter DATE_TIME_FORMATTER_YYYYMMDD = DateTimeFormatter
            .ofPattern(DATE_FORMAT_YYYYMMDD);
    public static final DateTimeFormatter DATE_TIME_FORMATTER_YYYYMM = DateTimeFormatter
            .ofPattern(DATE_FORMAT_YYYYMM);
    public static final DateTimeFormatter DATE_TIME_FORMATTER_MMMDDY_POINT_HHMM = DateTimeFormatter
            .ofPattern(DATE_FORMAT_MMM_SPACE_DD_SPACE_Y_POINT_HHMM);
    public static final DateTimeFormatter TIME_FORMATTER_HHMM = DateTimeFormatter
            .ofPattern(TIME_FORMAT_HHMM);
    public static final DateTimeFormatter DATE_TIME_FORMATTER_YYYY_MM_DD_HH_MM = DateTimeFormatter
            .ofPattern(DATE_FORMAT_YYYY_MM_DD_HH_MM);
    public static final int MAX_NUMBER_OF_SQS_MESSAGES = 10;

    public static final LocalDateTime INVALID_DATE_TIME = LocalDateTime.MIN;

    public static final int DEFAULT_ORACLE_FETCH_SIZE = 20000;

    public static final String INTERMEDIARY_CURRENCY = "USD";
    public static final String GLOBAL_CURRENCY = "SGD";

    public static final String GLOBAL_ZONE_ID = "Asia/Singapore";

    public static final Country GLOBAL_COUNTRY = Country.SINGAPORE;

    public static final String CSV_FILE_EXTENSION = ".csv";

    public static final ImmutableMap<String, String> ZONE_IDS = ImmutableMap.<String, String>builder()
            .put(INDONESIA.getCode(), "Asia/Jakarta")
            .put(MALAYSIA.getCode(), "Asia/Kuala_Lumpur")
            .put(SINGAPORE.getCode(), "Asia/Singapore")
            .put(THAILAND.getCode(), "Asia/Bangkok")
            .put(VIETNAM.getCode(), "Asia/Ho_Chi_Minh")
            .put(PHILIPPINES.getCode(), "Asia/Manila")
            .put(TAIWAN.getCode(), "Asia/Taipei")
            .build();

    public static final String NULL_STRING = "$$NULL$$";

    public static final int FRACTIONAL_DIGIT_COUNT = 2;

    public static final int PERCENTAGE_FRACTIONAL_DIGIT_COUNT = 4;

    public static final boolean IS_RDS_DATABASE_DISABLED = Boolean.getBoolean("isRdsDatabaseDisabled");
    public static final boolean ENABLE_MULTIPLE_DATABASES = Boolean.getBoolean("enableMultipleDatabases");

    public static final boolean DEBUG_MODE = Boolean.getBoolean("debugMode");

    public static final boolean IS_JENKINS_LOG_ENABLED = Boolean.getBoolean("isJenkinsLogEnabled");

    public static final Country TARGET_COUNTRY = Country.getCountryBy(
            System.getProperty("targetCountryCode", GLOBAL_COUNTRY.getCode()));

    public static final int EMBEDDED_DB_TIMEOUT_MILLISECOND = 100_000;

    public static final String SHUT_DOWN_MESSAGE = "Shutting down...";

    public static final String CONVERSION_REPORT_REACH_LIMIT = "reach_over_max_row_count";

    public static final long SHUTDOWN_PERIOD = 3000L;

    public static final int RDS_PARTITION_SIZE = 1000;

    public static final String ROUNDING_FORMAT_PATTERN = "#.##";

    public static final int MAX_WRITE_BATCH_SIZE = 100000;

    public static final int CREATIVE_ID_START_POSITION = 0;
    public static final int CREATIVE_ID_END_POSITION = 6;
    public static final int SITE_ID_START_POSITION = 6;
    public static final int SITE_ID_END_POSITION = 12;

    public static final long ONE_SEC_IN_MILLIS = 1000L;

    public static final String CHROME_WEB_DRIVER_COMMAND_VERSION = "/opt/google/chrome/google-chrome --version";
    public static final String CHROME_WEB_DRIVER_VERSION = "90.0.4430.24";
    public static final String DEFAULT_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36";
    public static final String DEFAULT_LANGUAGE = "en";

    public static final int CITY_LOCATION_FRACTIONAL_DIGIT_COUNT = 4;

    public static final String JDBC_URL_SECRET_KEY = "JDBC_URL";
    public static final String JDBC_USER_NAME_SECRET_KEY = "JDBC_USER_NAME";
    public static final String JDBC_PASSWORD_SECRET_KEY = "JDBC_PASSWORD";

    /**
     * Returns the value of the VM argument corresponding to the given argument name or
     * throws {@link TaekkyeonException} if the property value is untrievable.
     *
     * @param argumentName
     *            the name of the VM argument
     * @return the value of the VM argument corresponding to the given
     *         {@code argumentName}
     * @throws TaekkyeonException
     *             if the property value is untrievable
     */
    public static String getPropertyBy(String argumentName) throws TaekkyeonException {
        String argumentValue = getProperty(argumentName);
        if (isNullOrEmpty(argumentValue)) {
            throw new TaekkyeonException(
                    format(PROPERTY_ERROR_MESSAGE_FORMAT, argumentName));
        }
        return argumentValue;
    }

    /**
     * Returns the optional value of the VM argument corresponding to the given argument
     * name.
     *
     * @param argumentName
     *            the name of the VM argument
     * @return the optional value of the VM argument corresponding to the given
     *         {@code argumentName}
     */
    public static Optional<String> getOptionalPropertyBy(String argumentName) {
        String argumentValue = getProperty(argumentName);
        if (isNullOrEmpty(argumentValue)) {
            return Optional.empty();
        }
        return Optional.of(argumentValue);
    }
}
