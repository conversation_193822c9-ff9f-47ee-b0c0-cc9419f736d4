/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.MerchantAccountMapper;

import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link MerchantAccountService}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class MerchantAccountServiceTest {

    @InjectMocks
    private MerchantAccountService underTest;

    @Mock
    private MerchantAccountMapper merchantAccountMapper;

    @Test
    public void testFindCountryCodeByShouldReturnCorrectCountryCodeWhenCalled() {
        // given
        long campaignId = 1;
        String expected = "ID";

        when(merchantAccountMapper.findCountryCodeBy(campaignId)).thenReturn(expected);

        // when
        String actual = underTest.findCountryCodeBy(campaignId);

        // then
        assertSame(expected, actual);
    }
}
