/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_MOVED_PERMANENTLY;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link HttpHelper}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class HttpHelperTest {

    private static final String URL = "https://www.google.com";
    private static final String USER_AGENT = "userAgent";
    private static final String AUTHORIZATION = "authorization";
    private static final String REQUEST_BODY = "requestBody";
    private static final long SITE_ID = 1;

    @InjectMocks @Spy
    private HttpHelper underTest;

    @Mock
    private Logger logger;

    @Test
    public void testCreateUriShouldReturnCorrectUriWhenGivenNullParameters()
            throws Exception {
        // given
        String url = "http://interspace.com/";

        // when
        URI actual = underTest.createUri(url, null);

        // then
        assertNotNull(actual);
        assertEquals("http://interspace.com/", actual.toString());
    }

    @Test
    public void testCreateUriShouldReturnCorrectUriWhenGivenUrlNonExistentParameters()
            throws Exception {
        // given
        String url = "http://interspace.com/";
        Map<String, String> parameters = new HashMap<>();
        parameters.put("test1", "test2");
        parameters.put("test3", "test4");

        // when
        URI actual = underTest.createUri(url, parameters);

        // then
        assertNotNull(actual);
        assertEquals("http://interspace.com/?test3=test4&test1=test2", actual.toString());
    }

    @Test
    public void testCreateUriShouldReturnCorrectUriWhenGivenUrlExistingParameters()
            throws Exception {
        // given
        String url = "http://interspace.com?abc=def";
        Map<String, String> parameters = new HashMap<>();
        parameters.put("test1", "test2");
        parameters.put("test3", "test4");

        // when
        URI actual = underTest.createUri(url, parameters);

        // then
        assertNotNull(actual);
        assertEquals("http://interspace.com?abc=def&test3=test4&test1=test2",
                actual.toString());
    }

    @Test
    public void testCreateUriShouldReturnCorrectUriWhenGivenUrlContainsSpecialCharacter()
            throws Exception {
        // given
        String url1 = "http://interspace.com{}";
        String url2 = "http://interspace.com!";
        String url3 = "http://interspace.com@";
        String url4 = "http://interspace.com#";
        String url5 = "http://interspace.com$";
        String url6 = "http://interspace.com%";
        Map<String, String> parameters = new HashMap<>();
        parameters.put("p1", "test1");
        parameters.put("p2", "test2");

        // when
        URI actual1 = underTest.createUri(url1, parameters);
        URI actual2 = underTest.createUri(url2, parameters);
        URI actual3 = underTest.createUri(url3, parameters);
        URI actual4 = underTest.createUri(url4, parameters);
        URI actual5 = underTest.createUri(url5, parameters);
        URI actual6 = underTest.createUri(url6, parameters);

        // then
        assertNotNull(actual1);
        assertNotNull(actual2);
        assertNotNull(actual3);
        assertNotNull(actual4);
        assertNotNull(actual5);
        assertNotNull(actual6);
        assertEquals("http://interspace.com%7B%7D?p1=test1&p2=test2",
                actual1.toString());
        assertEquals("http://interspace.com!?p1=test1&p2=test2",
                actual2.toString());
        assertEquals("http://interspace.com@?p1=test1&p2=test2",
                actual3.toString());
        assertEquals("http://interspace.com?p1=test1&p2=test2#",
                actual4.toString());
        assertEquals("http://interspace.com$?p1=test1&p2=test2",
                actual5.toString());
        assertEquals("http://interspace.com%25?p1=test1&p2=test2",
                actual6.toString());
    }

    @Test
    public void testCreateUriShouldReturnCorrectUriWhenGivenUrlContainsBraceAndParameters()
            throws Exception {
        // given
        String url = "http://localhost/abc/def/?uid=52ss1&s1={interspace}#abc";
        Map<String, String> parameters = new HashMap<>();
        parameters.put("p1", "test1");
        parameters.put("p2", "test2");

        // when
        URI actual = underTest.createUri(url, parameters);

        // then
        assertNotNull(actual);
        assertEquals(
                "http://localhost/abc/def/?uid=52ss1&s1=%7Binterspace%7D&p1=test1&p2=test2#abc",
                actual.toString());
    }

    @Test
    public void testSendGetRequestShouldLogResponseWhenSendPostBackSuccessfully()
            throws IOException {
        // given
        MockHttpConnection connection = new MockHttpConnection(URL, SC_OK, false, false);
        doReturn(connection).when(underTest).createHttpConnectionTo(new URL(URL),
                USER_AGENT);
        doReturn(logger).when(underTest).getLogger();

        // when
        String actual = underTest.sendGetRequest(URL, USER_AGENT, SITE_ID);

        // then
        verify(logger)
                .info("Successfully sent GET request to siteId [{}] to URL [{}]", SITE_ID,
                        "https://www.google.com");
        verifyNoMoreInteractions(logger);
        assertEquals("200|", actual);
    }

    @Test
    public void testSendGetRequestShouldLogResponseWhenSendPostBackFailed()
            throws IOException {
        // given
        MockHttpConnection connection = new MockHttpConnection(URL, SC_BAD_REQUEST,
                false);
        doReturn(connection).when(underTest).createHttpConnectionTo(new URL(URL),
                USER_AGENT);
        doReturn(logger).when(underTest).getLogger();

        // when
        String actual = underTest.sendGetRequest(URL, USER_AGENT, SITE_ID);

        // then
        verify(logger).error(
                "Sending postback request to site [{}]"
                        + " by URL[{}] is failed with error code[{}] and error message[{}]",
                SITE_ID, URL, SC_BAD_REQUEST,
                MockHttpConnection.ERROR_MESSAGE);
        verifyNoMoreInteractions(logger);
        assertEquals("400|error", actual);
    }

    @Test
    public void testSendGetRequestShouldCallLogResponseWithoutErrorStreamWhenSendPostBackFailedAndResponseErrorStreamIsNull()
            throws IOException {
        // given
        MockHttpConnection connection = new MockHttpConnection(URL, SC_BAD_REQUEST, false,
                false);
        doReturn(connection).when(underTest).createHttpConnectionTo(new URL(URL),
                USER_AGENT);
        doReturn(logger).when(underTest).getLogger();

        // when
        String actual = underTest.sendGetRequest(URL, USER_AGENT, SITE_ID);

        // then
        verify(logger).error(
                "Sending postback request to site [{}] "
                        + "by URL[{}] is failed with error code[{}]",
                SITE_ID, URL, SC_BAD_REQUEST);
        verifyNoMoreInteractions(logger);
        assertEquals("400|", actual);
    }

    @Test
    public void testSendGetRequestShouldLogExceptionWhenSendPostBackFailed()
            throws IOException {
        // given
        String url = "https://www.error.com";
        String errorMessage = "Sending GET request to URL[https://www.error.com] is failed";
        MockHttpConnection connection = new MockHttpConnection(url, SC_NOT_FOUND, true);
        doReturn(connection).when(underTest).createHttpConnectionTo(new URL(url),
                USER_AGENT);
        doReturn(logger).when(underTest).getLogger();

        // when
        try {
            underTest.sendGetRequest(url, USER_AGENT, SITE_ID);
            fail();

            // then
        } catch (Exception e) {
            verify(logger).error(errorMessage, MockHttpConnection.IO_EXCEPTION);
            verifyNoMoreInteractions(logger);
        }
    }

    @Test
    public void testSendPostRequestShouldReturnCorrectDataWhenSendRequestSuccessfully()
            throws IOException {
        // given
        MockHttpConnection connection = new MockHttpConnection(URL, SC_OK, false, false);
        doReturn(connection).when(underTest).createHttpPostConnectionTo(new URL(URL),
                AUTHORIZATION, REQUEST_BODY);
        doNothing().when(underTest).logPostResponse(URL, REQUEST_BODY, SC_OK, EMPTY);
        doReturn(logger).when(underTest).getLogger();

        // when
        String actual = underTest.sendPostRequest(URL, AUTHORIZATION, REQUEST_BODY);

        // then
        assertEquals("200|", actual);
        verify(underTest).logPostResponse(URL, REQUEST_BODY, SC_OK, EMPTY);
        verify(logger, never()).info(anyString(), any(Exception.class));
    }

    @Test
    public void testSendPostRequestShouldReturnCorrectDataWhenSendRequestFailedAndResponseErrorStreamIsNull()
        throws IOException {
        // given
        MockHttpConnection connection = new MockHttpConnection(URL, SC_BAD_REQUEST, false,
                false);
        doReturn(connection).when(underTest).createHttpPostConnectionTo(new URL(URL),
                AUTHORIZATION, REQUEST_BODY);
        doNothing().when(underTest).logPostResponse(URL, REQUEST_BODY, SC_BAD_REQUEST,
                null);
        doReturn(logger).when(underTest).getLogger();

        // when
        String actual = underTest.sendPostRequest(URL, AUTHORIZATION, REQUEST_BODY);

        // then
        assertEquals("400|", actual);
        verify(underTest).logPostResponse(URL, REQUEST_BODY, SC_BAD_REQUEST, EMPTY);
        verify(logger, never()).info(anyString(), any(Exception.class));
    }

    @Test
    public void testSendPostRequestShouldReturnLogErrorAndThrowExceptionWhenAnErrorOccurredInPostRequest()
            throws IOException {
        // given
        MockHttpConnection connection = new MockHttpConnection(URL, SC_BAD_REQUEST, true,
                false);
        doReturn(connection).when(underTest).createHttpPostConnectionTo(new URL(URL),
                AUTHORIZATION, REQUEST_BODY);
        doReturn(logger).when(underTest).getLogger();

        // when
        try {
            underTest.sendPostRequest(URL, AUTHORIZATION, REQUEST_BODY);
            fail();
        } catch (Exception e) {
            // then
            verify(logger).error(
                    "Sending POST request to URL[https://www.google.com] is failed"
                            + "(requestBody:requestBody)",
                    MockHttpConnection.IO_EXCEPTION);
        }
    }

    @Test
    public void testLogPostResponseShouldLogSuccessWhenResponseCodeIsTwoHundred() {
        // given
        doReturn(logger).when(underTest).getLogger();

        // when
        underTest.logPostResponse(URL, REQUEST_BODY, SC_OK, null);

        // then
        verify(logger).info("Successfully sent POST request to URL "
                + "[https://www.google.com] (requestBody:requestBody)");
    }

    @Test
    public void testLogPostResponseShouldLogCorrectErrorWhenResponseCodeIsNotTwoHundredAndResponseErrorDetailsIsEmpty() {
        // given
        doReturn(logger).when(underTest).getLogger();

        // when
        underTest.logPostResponse(URL, REQUEST_BODY, SC_BAD_REQUEST, "");

        // then
        verify(logger).error(
                "Sending POST request to URL[{}]([{}]) is failed with error code[{}]",
                URL, REQUEST_BODY, SC_BAD_REQUEST);
    }

    @Test
    public void testLogPostResponseShouldLogCorrectErrorWhenResponseCodeIsNotTwoHundredAndResponseErrorDetailsIsNotEmpty() {
        // given
        String responseErrorDetails = "error";
        doReturn(logger).when(underTest).getLogger();

        // when
        underTest.logPostResponse(URL, REQUEST_BODY, SC_BAD_REQUEST,
                responseErrorDetails);

        // then
        verify(logger).error(
                "Sending POST request to URL[{}]([{}]) is failed with error "
                        + "code[{}] and error message[{}]",
                URL, REQUEST_BODY, SC_BAD_REQUEST, responseErrorDetails);
    }

    @Test
    public void testGetFinalUrlByShouldReturnUrlWhenResponseIsBadRequest()
            throws IOException {
        // given
        MockHttpConnection connection = new MockHttpConnection(URL, SC_BAD_REQUEST, false,
                false);
        doReturn(connection).when(underTest).createConnectionTo(new URL(URL));
        doReturn(logger).when(underTest).getLogger();

        // when
        String actual = underTest.getFinalUrlBy(URL);

        // then
        assertSame(URL, actual);
    }

    @Test
    public void testGetFinalUrlByShouldReturnUrlWhenIsRedirectReturnTrue()
            throws IOException {
        // given
        String finalUrl = "https://www.yahoo.co.jp/";
        HttpURLConnection connection = mock(HttpURLConnection.class);
        doReturn(connection).when(underTest).createConnectionTo(new URL(URL));
        doReturn(logger).when(underTest).getLogger();
        when(connection.getResponseCode()).thenReturn(SC_MOVED_PERMANENTLY);
        when(connection.getHeaderField("Location")).thenReturn(finalUrl);
        connection = new MockHttpConnection(finalUrl, SC_OK, false, false);
        doReturn(connection).when(underTest).createConnectionTo(new URL(finalUrl));

        // when
        String actual = underTest.getFinalUrlBy(URL);

        // then
        assertSame(finalUrl, actual);
    }

    @Test
    public void testGetFinalUrlByShouldReturnEmptyWhenExceptionOccured()
            throws IOException {
        // given
        MockHttpConnection connection = new MockHttpConnection(URL, SC_BAD_REQUEST, true,
                false);
        doReturn(connection).when(underTest).createConnectionTo(new URL(URL));
        doReturn(logger).when(underTest).getLogger();

        // when
        String actual = underTest.getFinalUrlBy(URL);

        // then
        assertSame(EMPTY, actual);
        verify(logger).error("Failed to access the URL: URL = [https://www.google.com]",
                MockHttpConnection.IO_EXCEPTION);
    }

}
