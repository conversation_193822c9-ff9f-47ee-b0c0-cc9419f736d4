/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.reader;

import javax.inject.Singleton;

import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.StringRecord;

/**
 * Returns a dummy {@link StringRecord} once to run the record processor exactly once.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class DummyRecordReader implements RecordReader {

    private boolean isRecordRead;

    @Override
    public void open() throws Exception {
        isRecordRead = false;
    }

    @Override
    public StringRecord readRecord() throws Exception {
        if (!isRecordRead) {
            isRecordRead = true;
            return new StringRecord(null, null);
        }
        return null;
    }

    @Override
    public void close() throws Exception {
        // do nothing
    }
}
