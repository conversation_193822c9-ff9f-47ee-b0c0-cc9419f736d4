/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.ses;

import java.io.IOException;
import java.io.StringWriter;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.mail.MessagingException;

import com.amazonaws.services.simpleemail.model.Body;
import com.amazonaws.services.simpleemail.model.Content;
import com.amazonaws.services.simpleemail.model.Destination;
import com.amazonaws.services.simpleemail.model.Message;
import com.amazonaws.services.simpleemail.model.MessageTag;
import com.amazonaws.services.simpleemail.model.SendEmailRequest;
import com.google.common.collect.ImmutableMap;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link TemplateEmailSender}.
 *
 * <AUTHOR> Varga
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class TemplateEmailSenderTest {

    private static final String SENDER = "sender";
    private static final String RECIPIENT = "recipient";
    private static final String SUBJECT = "subject";
    private static final String BODY_TEMPLATE_FILE_NAME = "bodyTemplateFileName";
    private static final String BODY = "body";
    private static final Object TEMPLATE_PARAMETERS = new Object();
    private static final String EMAIL_CHARACTER_SET = "UTF-8";
    private static final String ATTACHMENT_PATH = "attachmentPath";
    private static final Locale LOCALE = Locale.US;
    private static final Map<String, String> TAGS = ImmutableMap.of("key", "value");
    private static final String CONFIGURATION_SET_NAME = "ConfigurationSetName";

    @Spy
    @InjectMocks
    private TemplateEmailSender underTest;

    @Mock
    private Configuration templateConfiguration;

    @Mock
    private Template bodyTemplate;

    @Mock
    private StringWriter bodyWriter;

    @Mock
    private SimpleEmailServiceClient sesClient;

    @Test
    public void testSendShouldSendCorrectEmailWhenBodyIsSuccessfullyCreatedFromTheTemplate()
            throws IOException, TemplateException {
        // given
        when(templateConfiguration.getTemplate(BODY_TEMPLATE_FILE_NAME, LOCALE))
                .thenReturn(bodyTemplate);

        doReturn(bodyWriter).when(underTest).createBodyWriter();

        when(bodyWriter.toString()).thenReturn(BODY);

        ArgumentCaptor<Destination> destinationCaptor = ArgumentCaptor
                .forClass(Destination.class);
        ArgumentCaptor<Message> messageCaptor = ArgumentCaptor.forClass(Message.class);

        // when
        underTest.send(SENDER, RECIPIENT, SUBJECT, BODY_TEMPLATE_FILE_NAME,
                TEMPLATE_PARAMETERS,
                LOCALE);

        // then
        verify(bodyTemplate).process(TEMPLATE_PARAMETERS, bodyWriter);
        verify(sesClient).sendEmail(eq(SENDER), destinationCaptor.capture(),
                messageCaptor.capture());

        Destination actualDestination = destinationCaptor.getValue();
        assertNotNull(actualDestination);
        assertEquals(1, actualDestination.getToAddresses().size());
        assertEquals(RECIPIENT, actualDestination.getToAddresses().get(0));
        assertTrue(actualDestination.getCcAddresses().isEmpty());
        assertTrue(actualDestination.getBccAddresses().isEmpty());

        Message actualMessage = messageCaptor.getValue();
        assertNotNull(actualMessage);

        Content actualSubject = actualMessage.getSubject();
        assertNotNull(actualSubject);
        assertEquals(SUBJECT, actualSubject.getData());
        assertEquals(EMAIL_CHARACTER_SET, actualSubject.getCharset());

        Body actualBody = actualMessage.getBody();
        assertNotNull(actualBody);
        assertNull(actualBody.getText());

        Content actualHtmlBody = actualBody.getHtml();
        assertNotNull(actualHtmlBody);
        assertEquals(BODY, actualHtmlBody.getData());
        assertEquals(EMAIL_CHARACTER_SET, actualHtmlBody.getCharset());
    }

    @Test
    public void testSendWithCustomTagsShouldWorkCorrectWhenCalled() {
        // given
        ArgumentCaptor<SendEmailRequest> requestCaptor = ArgumentCaptor
                .forClass(SendEmailRequest.class);
        doReturn(CONFIGURATION_SET_NAME).when(underTest).getConfigurationSetName();

        // when
        underTest.sendWithCustomTags(SENDER, RECIPIENT, SUBJECT, BODY, TAGS);

        // then
        verify(sesClient).sendEmail(requestCaptor.capture());

        SendEmailRequest actualRequest = requestCaptor.getValue();
        assertNotNull(actualRequest);
        assertSame(SENDER, actualRequest.getSource());
        Destination actualDestination = actualRequest.getDestination();
        assertNotNull(actualDestination);
        assertEquals(1, actualDestination.getToAddresses().size());
        assertSame(RECIPIENT, actualDestination.getToAddresses().get(0));
        assertTrue(actualDestination.getCcAddresses().isEmpty());
        assertTrue(actualDestination.getBccAddresses().isEmpty());

        Message actualMessage = actualRequest.getMessage();
        assertNotNull(actualMessage);
        Content actualSubject = actualMessage.getSubject();
        assertNotNull(actualSubject);
        assertSame(SUBJECT, actualSubject.getData());
        assertSame(EMAIL_CHARACTER_SET, actualSubject.getCharset());

        Body actualBody = actualMessage.getBody();
        assertNotNull(actualBody);
        assertNull(actualBody.getText());

        Content actualHtmlBody = actualBody.getHtml();
        assertNotNull(actualHtmlBody);
        assertSame(BODY, actualHtmlBody.getData());
        assertSame(EMAIL_CHARACTER_SET, actualHtmlBody.getCharset());
        assertSame(CONFIGURATION_SET_NAME, actualRequest.getConfigurationSetName());

        List<MessageTag> actualTags = actualRequest.getTags();
        assertEquals(1, actualTags.size());
        MessageTag actualTag = actualTags.get(0);
        assertSame("key", actualTag.getName());
        assertEquals("value", actualTag.getValue());
    }

    @Test
    public void testSendWithAttachmentFileShouldSendCorrectEmailWithAttachmentWhenCalled()
            throws IOException, TemplateException, MessagingException {
        // given
        when(templateConfiguration.getTemplate(BODY_TEMPLATE_FILE_NAME, LOCALE))
                .thenReturn(bodyTemplate);

        doReturn(bodyWriter).when(underTest).createBodyWriter();

        when(bodyWriter.toString()).thenReturn(BODY);

        // when
        underTest.sendWithAttachmentFile(SENDER, RECIPIENT, SUBJECT,
                BODY_TEMPLATE_FILE_NAME, TEMPLATE_PARAMETERS, ATTACHMENT_PATH, LOCALE);

        // then
        verify(bodyTemplate).process(TEMPLATE_PARAMETERS, bodyWriter);
        verify(sesClient).sendMailWithAttachment(SENDER, RECIPIENT, SUBJECT, BODY,
                ATTACHMENT_PATH);
    }
}
