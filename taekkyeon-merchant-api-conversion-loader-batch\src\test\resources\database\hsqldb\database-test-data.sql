SET DATABASE SQL SYNTAX ORA TRUE;

INSERT INTO MERCHANT_CAMPAIGN(CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, CATEGORY1, CA<PERSON><PERSON>ORY2, <PERSON><PERSON>GORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE, AD_PLATFORM_ID, CURRENCY)
VALUES(3000, 3, 1, 'campaignName', 'campaignUrl', 'imageUrl', 0, 0, 0, 0, 0, 0, 1, 0, TO_DATE('2016/05/03', 'YYYY/MM/DD'), TO_DATE('2016/07/03', 'YYYY/MM/DD'), 0, 1,'SGD');

INSERT INTO campaign_integration_condition (ID, CAMPAIGN_BRAND, CAMPAIGN_ID, <PERSON><PERSON><PERSON>ION_NAME, CONDITION_VALUES, RELATIVE_CLAUSE_OPERATOR)
VALUES (1, 'SHOPEE', 6320, 'shopId', '*********', 'AND');
INSERT INTO campaign_integration_condition (ID, CAMPAIGN_BRAND, CAMPAIGN_ID, CONDITION_NAME, CONDITION_VALUES, RELATIVE_CLAUSE_OPERATOR, GROUP_SHOP_ID_VALUES)
VALUES (2, 'SHOPEE', 6320, 'itemName', 'noroid', 'AND','*********');

INSERT INTO MERCHANT_INTEGRATION_HISTORY(KEY, CAMPAIGN_ID, CONVERSION_OCCURS_DATE, DATA, MERCHANT)
VALUES ('testKey', 111, TO_DATE('2023/02/12 12:34:56', 'YYYY/MM/DD HH24:MI:SS'), 'test data', 'SHOPEE');

INSERT INTO MERCHANT_INTEGRATION_HISTORY(KEY, CAMPAIGN_ID, CONVERSION_OCCURS_DATE, DATA, MERCHANT)
VALUES ('testKey2', 222, TO_DATE('2023/02/12 12:34:56', 'YYYY/MM/DD HH24:MI:SS'), 'test data2', 'SHOPEE');

INSERT INTO MERCHANT_INTEGRATION_HISTORY(KEY, CAMPAIGN_ID, CONVERSION_OCCURS_DATE, DATA, MERCHANT)
VALUES ('testKey3', 333, TO_DATE('2023/02/12 12:34:56', 'YYYY/MM/DD HH24:MI:SS'), 'test data3', 'SHOPEE');

INSERT INTO MERCHANT_INTEGRATION_HISTORY(KEY, CAMPAIGN_ID, CONVERSION_OCCURS_DATE, DATA, MERCHANT)
VALUES ('testKey4', 444, TO_DATE('2023/02/12 12:34:56', 'YYYY/MM/DD HH24:MI:SS'), 'test data4', 'SHOPEE');

INSERT INTO MERCHANT_INTEGRATION_HISTORY(KEY, CAMPAIGN_ID, CONVERSION_OCCURS_DATE, DATA, MERCHANT)
VALUES ('testKey5', 555, TO_DATE('2023/02/12 12:34:56', 'YYYY/MM/DD HH24:MI:SS'), 'test data5', 'SHOPEE');

INSERT INTO CURRENCY_EXCHANGE_RATE_HISTORY (CURRENCY, QUOTE_CURRENCY, TARGET_MONTH, RATE)
VALUES ('PHP', 'SGD', TO_DATE('202002', 'YYYYMM'), 5);

INSERT INTO CURRENCY_EXCHANGE_RATE_HISTORY (CURRENCY, QUOTE_CURRENCY, TARGET_MONTH, RATE)
VALUES ('SGD', 'SGD', TO_DATE('202002', 'YYYYMM'), 1);

INSERT INTO MERCHANT_ACCOUNT (ACCOUNT_NO, MERCHANT_TYPE_ID, ACCOUNT_STATE, FOSTER_EMAIL, COUNTRY_CODE)
VALUES (3, 1, 1, 'accesstrade-sg.com', 'SG');

INSERT INTO COUNTRY (CODE,NAME,CURRENCY,SUPPORT_EMAIL,ACCESSTRADE_URL,PHONE,MINIMUM_AMOUNT,CJ_API_KEY, CJ_PUBLISHER_ID, ZONE_ID, GLOBAL_PUBLISHERS_ENABLED)
VALUES ('SG','Singapore','SGD','<EMAIL>','https://www.accesstrade.global/th','+65 6829-7064', 50, null, null, 'Asia/Bangkok', 0);

INSERT INTO STAFF_ACCOUNT(STAFF_NO, LASTNAME, FIRSTNAME, EMAIL, LOGIN_NAME, U_ID, ACCOUNT_STATE, COUNTRY_CODE)
VALUES (11, 'testLastName1', 'testFirstName1', '<EMAIL>', 'staff1LoginName', '0123456789abcdefghijklmnopqrstu1', 1, 'TH');

INSERT INTO API_KEY_NAME(CAMPAIGN_ID, CAMPAIGN_ADV_TYPE, ADV_PARENT_CAMPAIGN_ID, SHOP_ID_MAPPING, ADV_PLATFORM)
VALUES (2000, 'BRAND', 1000, '123456', 'SHOPEE');

INSERT INTO API_KEY_NAME(CAMPAIGN_ID, CAMPAIGN_ADV_TYPE, ADV_PARENT_CAMPAIGN_ID, SHOP_ID_MAPPING, ADV_PLATFORM)
VALUES (2001, 'BRAND', 1000, '654321', 'SHOPEE');

INSERT INTO API_KEY_NAME(CAMPAIGN_ID, CAMPAIGN_ADV_TYPE, ADV_PARENT_CAMPAIGN_ID, SHOP_ID_MAPPING, ADV_PLATFORM)
VALUES (3000, 'BRAND', 1001, '789012', 'SHOPEE');

INSERT INTO API_KEY_NAME(CAMPAIGN_ID, CAMPAIGN_ADV_TYPE, ADV_PARENT_CAMPAIGN_ID, SHOP_ID_MAPPING, ADV_PLATFORM)
VALUES (4000, 'REGULAR', 1002, '345678', 'LAZADA');