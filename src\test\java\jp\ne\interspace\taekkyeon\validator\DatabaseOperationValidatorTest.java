/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.validator;

import org.junit.Test;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

/**
 * Unit test for {@link DatabaseOperationValidator}.
 *
 * <AUTHOR> Shin
 */
public class DatabaseOperationValidatorTest {

    private static final String INSERTION_FAILED_ERROR_MESSAGE = "Insertion failed due to a database error.";
    private static final String UPDATE_FAILED_ERROR_MESSAGE = "Update failed due to a database error.";
    private static final String NO_ROW_UPDATED_ERROR_MESSAGE = "noRowUpdatedErrorMessage";
    private static final String TOO_MANY_ROWS_UPDATED_ERROR_MESSAGE = "Update failed because updated row count: 11 "
            + "and expected row count: 10 are different.";
    private static final String TOO_FEW_ROWS_UPDATED_ERROR_MESSAGE = "Update failed because updated row count: 9 "
            + "and expected row count: 10 are different.";

    private DatabaseOperationValidator underTest = new DatabaseOperationValidator();

    @Test
    public void testValidateAutoGeneratedIdShouldNotThrowExceptionWhenIdIsNotZero() {
        // given
        int id = 1;

        // when
        underTest.validateAutoGeneratedId(id);
    }

    @Test
    public void testValidateAutoGeneratedIdShouldThrowExceptionWhenIdIsZero() {
        // given
        int id = 0;

        // when
        try {
            underTest.validateAutoGeneratedId(id);
            fail();

            // then
        } catch (TaekkyeonException ex) {
            assertEquals(INSERTION_FAILED_ERROR_MESSAGE, ex.getMessage());
        }
    }

    @Test
    public void testValidateInsertedRowCountShouldThrowExceptionWhenNoRowWasInserted() {
        // given
        int insertedRowCount = 0;

        // when
        try {
            underTest.validateInsertedRowCount(insertedRowCount);
            fail();

            // then
        } catch (TaekkyeonException ex) {
            assertEquals(INSERTION_FAILED_ERROR_MESSAGE, ex.getMessage());
        }
    }

    @Test
    public void testValidateInsertedRowCountShouldThrowExceptionWhenMoreThanOneRowWasInserted() {
        // given
        int insertedRowCount = 2;

        // when
        try {
            underTest.validateInsertedRowCount(insertedRowCount);
            fail();

            // then
        } catch (TaekkyeonException ex) {
            assertEquals(INSERTION_FAILED_ERROR_MESSAGE, ex.getMessage());
        }
    }

    @Test
    public void testValidateModifiedRowCountShouldNotThrowExceptionWhenOneRowWasModified() {
        // given
        int modifiedRowCount = 1;

        // when
        underTest.validateModifiedRowCount(modifiedRowCount, NO_ROW_UPDATED_ERROR_MESSAGE);
    }

    @Test
    public void testValidateModifiedRowCountShouldThrowExceptionWhenNoRowsWereModified() {
        // given
        int modifiedRowCount = 0;

        // when
        try {
            underTest.validateModifiedRowCount(modifiedRowCount, NO_ROW_UPDATED_ERROR_MESSAGE);
            fail();

            // then
        } catch (TaekkyeonException ex) {
            assertEquals(NO_ROW_UPDATED_ERROR_MESSAGE, ex.getMessage());
        }
    }

    @Test
    public void testValidateModifiedRowCountShouldThrowExceptionWhenMoreThanOneRowWasModified() {
        // given
        int modifiedRowCount = 2;

        // when
        try {
            underTest.validateModifiedRowCount(modifiedRowCount, NO_ROW_UPDATED_ERROR_MESSAGE);
            fail();

            // then
        } catch (TaekkyeonException ex) {
            assertEquals(UPDATE_FAILED_ERROR_MESSAGE, ex.getMessage());
        }
    }

    @Test
    public void testValidateModifiedRowCountShouldNotThrowExceptionWhenModifiedRowCountEqualsTheExpectedRowCount() {
        // given
        int modifiedRowCount = 10;
        int expectedModifiedRowCount = 10;

        // when
        underTest.validateModifiedRowCount(modifiedRowCount, expectedModifiedRowCount);
    }

    @Test
    public void testValidateModifiedRowCountShouldThrowExceptionWhenTooManyRowsWereModified() {
        // given
        int modifiedRowCount = 11;
        int expectedModifiedRowCount = 10;

        // when
        try {
            underTest.validateModifiedRowCount(modifiedRowCount,
                    expectedModifiedRowCount);
            fail();

            // then
        } catch (TaekkyeonException ex) {
            assertEquals(TOO_MANY_ROWS_UPDATED_ERROR_MESSAGE, ex.getMessage());
        }
    }

    @Test
    public void testValidateModifiedRowCountShouldThrowExceptionWhenTooFewRowsWereModified() {
        // given
        int modifiedRowCount = 9;
        int expectedModifiedRowCount = 10;

        // when
        try {
            underTest.validateModifiedRowCount(modifiedRowCount,
                    expectedModifiedRowCount);
            fail();

            // then
        } catch (TaekkyeonException ex) {
            assertEquals(TOO_FEW_ROWS_UPDATED_ERROR_MESSAGE, ex.getMessage());
        }
    }

    @Test
    public void testValidateModifiedSingleOrZeroRowShouldNotThrowExceptionWhenNoRowsWereModified() {
        // given
        int modifiedRowCount = 0;

        // when
        underTest.validateModifiedSingleOrZeroRow(modifiedRowCount);
    }

    @Test
    public void testValidateModifiedSingleOrZeroRowShouldNotThrowExceptionWhenOneRowWasModified() {
        // given
        int modifiedRowCount = 1;

        // when
        underTest.validateModifiedSingleOrZeroRow(modifiedRowCount);
    }

    @Test
    public void testValidateModifiedSingleOrZeroRowShouldThrowExceptionWhenMultipleRowsWereModified() {
        // given
        int modifiedRowCount = 2;

        // when
        try {
            underTest.validateModifiedSingleOrZeroRow(modifiedRowCount);
            fail();

            // then
        } catch (TaekkyeonException ex) {
            assertEquals(UPDATE_FAILED_ERROR_MESSAGE, ex.getMessage());
        }
    }
}
