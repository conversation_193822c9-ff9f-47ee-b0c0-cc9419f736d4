/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.json;

import java.lang.reflect.Type;

import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import jp.ne.interspace.taekkyeon.model.ProductFeedType;

/**
 * Json serializer for {@link ProductFeedType}.
 *
 * <AUTHOR> Vuong
 */
public class ProductFeedTypeAdapter implements JsonSerializer<ProductFeedType> {

    @Override
    public JsonElement serialize(ProductFeedType productFeedType, Type type,
            JsonSerializationContext context) {
        return new JsonPrimitive(String.valueOf(productFeedType.getValue()));
    }
}
