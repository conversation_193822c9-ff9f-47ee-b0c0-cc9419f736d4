/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * DTO for holding the data of conversion rewards.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @RequiredArgsConstructor @Getter @Setter
public class ConversionRewards {

    @NonNull
    private final BigDecimal salesReward;
    @NonNull
    private final BigDecimal totalPriceReward;
    @NonNull
    private final BigDecimal atCommission;
    @NonNull
    private final BigDecimal agentCommission;
    @NonNull
    private final BigDecimal publisherAgentCommission;
    @NonNull
    private final BigDecimal publisherRewardInUsd;
    @NonNull
    private final BigDecimal atCommissionInUsd;
    @NonNull
    private final BigDecimal agentCommissionInUsd;
    @NonNull
    private final BigDecimal publisherAgentCommissionInUsd;
    private BigDecimal transactionAmountInUsd;
    private BigDecimal discountInUsd;
    private BigDecimal unitPriceInUsd;

    /**
     * Returns the total value of conversion rewards.
     *
     * @return the total value of conversion rewards
     */
    public BigDecimal getTotal() {
        return salesReward.add(totalPriceReward).add(atCommission).add(agentCommission)
                .add(publisherAgentCommission);
    }
}
