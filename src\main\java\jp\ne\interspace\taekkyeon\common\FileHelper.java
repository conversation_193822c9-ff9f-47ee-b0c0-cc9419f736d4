/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import java.io.File;
import java.io.FileNotFoundException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

import javax.inject.Singleton;

/**
 * Convenience class for handling files.
 *
 * <AUTHOR>
 */
@Singleton
public class FileHelper {

    /**
     * Moves a file from {@code sourcePath} to {@code targetPath}.
     *
     * @param sourcePath
     *          the source file path
     * @param targetPath
     *          the target file path
     * @throws Exception when a problem occurs
     */
    public void move(String sourcePath, String targetPath) throws Exception {
        move(getPathFrom(sourcePath), getPathFrom(targetPath));
    }

    /**
     * Moves a file from {@link Path} to {@link Path}.
     *
     * @param from
     *          the source file {@link Path}
     * @param to
     *          the target file {@link Path}
     * @throws Exception when a problem occurs
     */
    public void move(Path from, Path to) throws Exception {
        if (doesFileExistOn(from)) {
            Files.move(from, to.resolve(from.getFileName()),
                    StandardCopyOption.REPLACE_EXISTING);
        } else {
            throw new FileNotFoundException();
        }
    }

    /**
     * Returns the {@link Path} by the given path string.
     *
     * @param pathString
     *          the file path
     * @return the {@link Path} by the given path string
     */
    public Path getPathFrom(String pathString) {
        return Paths.get(pathString);
    }

    /**
     * Returns {@code true} if the file in the given {@link Path} exists,
     * or {@code false} otherwise.
     *
     *
     * @param path
     *          the file {@link Path}
     * @return {@code true} if the file in the given {@link Path} exists,
     *          or {@code false} otherwise
     */
    public boolean doesFileExistOn(Path path) {
        return Files.exists(path);
    }

    /**
     * Returns the {@link File} by the given {@code filePath}.
     *
     * @param filePath
     *          the file path
     * @return the {@link File} by the given {@code filePath}
     */
    public File createFileBy(String filePath) {
        return new File(filePath);
    }
}
