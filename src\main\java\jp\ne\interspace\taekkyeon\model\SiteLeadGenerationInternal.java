/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

import jp.ne.interspace.taekkyeon.common.TaekkyeonConstants;

/**
 * Enumeration for holding site lead generation internal data.
 *
 * <AUTHOR> Mayur
 */
@Getter @AllArgsConstructor
public enum SiteLeadGenerationInternal {

    ATRS(1, "ATRS"),
    AFFILIATE_NETWORK(2, "Affiliate Network"),
    COMPARISON_SITE(3, "Comparison Site"),
    COUPON_AND_DISCOUNT_CODES(4, "Coupon & Discount Codes"),
    DIRECT_LINKING(5, "Direct Linking"),
    DISPLAY_BANNER(6, "Display Banner"),
    EMAIL_MARKETING(7, "Email Marketing"),
    INCENTIVE_TRAFFIC_AND_LOYALTY(8, "Incentive traffic & Loyalty"),
    <PERSON><PERSON>(9, "KOC"),
    <PERSON><PERSON>(10, "KOL"),
    MEDIA_BUYING(11, "Media Buying"),
    POP_TRAFFIC(12, "Pop Traffic"),
    PUSH_NOTIFICATION(13, "Push Notification"),
    REVIEW_SITE(14, "Review Site"),
    SOCIAL_MEDIA_PLATFORM(15, "Social Media Platform"),
    SOCIAL_MESSENGER_APP(16, "Social Messenger App"),
    SEM(17, "SEM"),
    NATIVE_ADS(18, "Native Ads"),
    DISPLAY_ADS(19, "Display Ads"),
    SOCIAL_MEDIA_ADS(20, "Social Media Ads"),
    CONTENT_SITE(21, "Content Site"),
    MEDIA_SITE(22, "Media Site"),
    AD_NETWORK(23, "Ad network");

    private int value;
    private String name;

    /**
     *  Returns name for the given {@code value}.
     * @param value
     *          the given value
     * @return
     *          name for the given {@code value}
     */
    public static String getNameBy(int value) {
        for (SiteLeadGenerationInternal item : SiteLeadGenerationInternal.values()) {
            if (item.getValue() == value) {
                return item.getName();
            }
        }
        return TaekkyeonConstants.EMPTY;
    }

}

