/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding all the different site statuses.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum SiteStatus implements ValueEnum {

    REVIEWING(0),
    APPROVED(1),
    REJECTED(2),
    DELETED(3);

    private final int value;
}
