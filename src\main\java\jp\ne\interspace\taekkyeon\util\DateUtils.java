/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Date;

import javax.inject.Singleton;

/**
 * Utility class for date functions.
 *
 * <AUTHOR>
 */
@Singleton
public class DateUtils {

    private static final ZoneOffset DEFAULT_ZONE_OFFSET = ZoneOffset.of("+07:00");

    /**
     * Returns instance of {@link Date} from {@link ZonedDateTime} Instant.
     *
     * @param dateTime
     *            {@link ZonedDateTime} holding the date and time to convert
     * @return instance of {@link Date} from {@link ZonedDateTime} Instant
     */
    public Date createDateFrom(ZonedDateTime dateTime) {
        return Date.from(dateTime.toInstant());
    }

    /**
     * Returns the converted date-time in the given time zone.
     *
     * @param dateTime
     *            the given date time
     * @param zoneId
     *            the given zone ID
     * @return the converted date-time in the given time zone
     */
    public ZonedDateTime convertByTimeZone(LocalDateTime dateTime, String zoneId) {
        return dateTime.atOffset(DEFAULT_ZONE_OFFSET).toZonedDateTime()
                .withZoneSameInstant(ZoneId.of(zoneId));
    }
}
