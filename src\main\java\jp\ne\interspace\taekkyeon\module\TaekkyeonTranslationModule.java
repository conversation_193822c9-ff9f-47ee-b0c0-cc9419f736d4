/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.translate.Translate;
import com.google.cloud.translate.TranslateOptions;
import com.google.common.collect.ImmutableMap;
import com.google.inject.AbstractModule;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.persist.aws.secretsmanager.SecretsManagerClient;

import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Gurkha module for accessing text translation api.
 *
 * <AUTHOR>
 */
public class TaekkyeonTranslationModule extends AbstractModule {

    private static final String SERVICE_API_KEY_FILE = "accesstrade-influencers-dev-ef9ffc236b6d.json";
    private static final ImmutableMap<Environment, String> TRANSLATION_SERVICE_API_KEY =
            new ImmutableMap.Builder<Environment, String>()
                    .put(DEV, "")
                    .put(STAGING, "")
                    .put(PRODUCTION, "Google-Service-Key")
                    .build();

    @Override
    protected void configure() {
        bind(Translate.class).toInstance(buildTranslateService());
    }

    private Translate buildTranslateService() {
        try {
            return TranslateOptions.newBuilder()
                    .setCredentials(ServiceAccountCredentials.fromStream(
                            getStreamOfGoogleServiceApi()))
                    .build().getService();
        } catch (IOException e) {
            throw new TaekkyeonException(
                    "There is a problem while building translation module.");
        }
    }

    private InputStream getStreamOfGoogleServiceApi() {
        String googleServiceApiKey = TRANSLATION_SERVICE_API_KEY.get(
                getCurrentEnvironment());
        return googleServiceApiKey.isEmpty()
                ? getClass().getClassLoader().getResourceAsStream(SERVICE_API_KEY_FILE)
                : new ByteArrayInputStream(SecretsManagerClient.getInstance()
                        .getSecretValue(googleServiceApiKey).getBytes());
    }
}
