/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

import lombok.Getter;

/**
 * DTO for holding conversion registration details with sub campaign ID.
 *
 * <AUTHOR>
 */
@Getter
public class ClickConversionRegistrationDetails extends ConversionRegistrationDetails {

    private final Long subCampaignId;

    /**
     * Constructor to be used by the relevant MyBatis mapper. Cannot be auto-generated
     * because of the {@code super()} call.
     */
    public ClickConversionRegistrationDetails(ZonedDateTime conversionTime,
            String transactionId, int resultId, String customerType,
            String productCategoryId, String productId, BigDecimal productUnitPrice,
            String clickId, Long subCampaignId) {
        super(conversionTime, transactionId, resultId, customerType, productCategoryId,
                productId, productUnitPrice, clickId);
        this.subCampaignId = subCampaignId;
    }
}
