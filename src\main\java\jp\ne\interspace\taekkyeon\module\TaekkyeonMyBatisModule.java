/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.util.Collection;
import java.util.Properties;
import java.util.Set;

import javax.sql.DataSource;

import com.google.common.collect.ImmutableMap;
import com.google.inject.PrivateModule;
import com.google.inject.Provider;
import com.google.inject.name.Names;

import org.apache.ibatis.io.ResolverUtil;
import org.apache.ibatis.io.ResolverUtil.Test;
import org.apache.ibatis.session.LocalCacheScope;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionManager;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.mybatis.guice.MyBatisModule;
import org.mybatis.guice.datasource.builtin.PooledDataSourceProvider;
import org.mybatis.guice.session.SqlSessionFactoryProvider;
import org.mybatis.guice.session.SqlSessionManagerProvider;

import static com.google.inject.name.Names.named;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.MARIADB;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.ORACLE;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.REDSHIFT;

/**
 * Taekkyeon module for mybatis.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonMyBatisModule extends PrivateModule {

    public static final String BIND_KEY_MYBATIS_ENVIRONMENT_ID = "mybatis.environment.id";
    public static final String DEFAULT_MYBATIS_ENVIRONMENT_NAME = "Taekkyeon Batch";

    public static final ImmutableMap<DatabaseType, String> MYBATIS_SQL_MAPPER_PACKAGES = new ImmutableMap.Builder<DatabaseType, String>()
            .put(ORACLE, "jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper")
            .put(REDSHIFT, "jp.ne.interspace.taekkyeon.persist.aws.redshift.mapper")
            .put(MARIADB, "jp.ne.interspace.taekkyeon.persist.mariadb.mapper")
            .build();

    public static final String MYBATIS_TYPE_HANDLER_PACKAGE = "jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler";

    private static final String KEY_DISABLE_RDS_QUERY_CACHING = "disableRdsQueryCaching";
    private static final String KEY_ENABLE_REDSHIFT_PING_QUERY = "enableRedshiftPingQuery";

    private final DatabaseType databaseType;
    private final Provider<Properties> properties;

    public TaekkyeonMyBatisModule(DatabaseType databaseType) {
        this.databaseType = databaseType;
        this.properties = new TaekkyeonMyBatisPropertiesProvider(databaseType);
    }

    @Override
    protected void configure() {
        install(new MyBatisModule() {
            @Override
            protected void initialize() {
                Names.bindProperties(binder(), properties.get());
                if (Boolean.getBoolean(KEY_DISABLE_RDS_QUERY_CACHING)) {
                    useCacheEnabled(false);
                    localCacheScope(LocalCacheScope.STATEMENT);
                }
                if (databaseType == REDSHIFT
                        && Boolean.getBoolean(KEY_ENABLE_REDSHIFT_PING_QUERY)) {
                    bindConstant().annotatedWith(named("mybatis.pooled.pingEnabled"))
                            .to(true);
                    bindConstant().annotatedWith(named("mybatis.pooled.pingQuery"))
                            .to("SELECT 1");
                }
                bindDataSourceProviderType(PooledDataSourceProvider.class);
                bindTransactionFactoryType(JdbcTransactionFactory.class);

                addMapperClassesIn(MYBATIS_SQL_MAPPER_PACKAGES.get(databaseType));
                addTypeHandlerClasses(MYBATIS_TYPE_HANDLER_PACKAGE);

                bindSessionFactory();
                bindSessionManager();
                bindDataSourceProvider();
            }

            private void bindSessionManager() {
                bind(SqlSessionManager.class)
                        .annotatedWith(databaseType.getAnnotationType())
                        .toProvider(SqlSessionManagerProvider.class);
                expose(SqlSessionManager.class)
                        .annotatedWith(databaseType.getAnnotationType());
            }

            private void bindSessionFactory() {
                bind(SqlSessionFactory.class)
                        .annotatedWith(databaseType.getAnnotationType())
                        .toProvider(SqlSessionFactoryProvider.class);
                expose(SqlSessionFactory.class)
                        .annotatedWith(databaseType.getAnnotationType());
            }

            private void bindDataSourceProvider() {
                bind(DataSource.class).annotatedWith(databaseType.getAnnotationType())
                        .toProvider(PooledDataSourceProvider.class);
                expose(DataSource.class).annotatedWith(databaseType.getAnnotationType());
            }

            private void addMapperClassesIn(String packageName) {
                bind(getClassesIn(packageName));
            }

            private Set<Class<?>> getClassesIn(String packageName) {
                Test isObject = new ResolverUtil.IsA(Object.class);
                return new ResolverUtil<Object>().find(isObject, packageName)
                        .getClasses();
            }

            private void bind(Collection<Class<?>> mappers) {
                for (Class<?> mapper : mappers) {
                    addMapperClass(mapper);
                    expose(mapper);
                }
            }
        });
    }
}
