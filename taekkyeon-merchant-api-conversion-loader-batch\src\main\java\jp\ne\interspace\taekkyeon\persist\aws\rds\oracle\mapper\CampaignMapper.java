/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.CampaignConditionDetails;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybat<PERSON> mapper for campaign.
 *
 * <AUTHOR>
 */
public interface CampaignMapper {

    /**
        SELECT
            campaign_name
        FROM
            merchant_campaign
        WHERE
            campaign_no = #{campaignId}
     */
    @Multiline String SELECT_CAMPAIGN_NAME = "";

    /**
     * Returns the campaign name by the given campaign ID.
     *
     * @param campaignId
     *            ID of the given campaign
     * @return the campaign name by the given campaign ID
     */
    @Select(SELECT_CAMPAIGN_NAME)
    String findCampaignNameBy(long campaignId);

    /**
         SELECT
             campaign_id AS campaignId,
             condition_name AS conditionName,
             condition_values AS conditionValues,
             group_shop_id_values AS groupShopId,
             relative_clause_operator AS relativeClauseOperator
         FROM
             campaign_integration_condition
         WHERE
            campaign_id = #{campaignId}
     */
    @Multiline String SELECT_CAMPAIGN_INTEGRATION_CONDITION = "";
    /**
     * Return the {@link CampaignConditionDetails}s by search parameters.
     *
     * @param campaignId
     *            ID of the given campaign
     * @return the {@link CampaignConditionDetails}s by search parameters
     * @see #SELECT_CAMPAIGN_INTEGRATION_CONDITION
     */
    @Select(SELECT_CAMPAIGN_INTEGRATION_CONDITION)
    @ConstructorArgs({ @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "conditionName", javaType = String.class),
            @Arg(column = "conditionValues", javaType = String.class),
            @Arg(column = "groupShopId", javaType = String.class),
            @Arg(column = "relativeClauseOperator", javaType = String.class) })
    List<CampaignConditionDetails> findCampaignConditionBy(
            @Param("campaignId") long campaignId);
}
