/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.google.gson.JsonParser;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import jp.ne.interspace.taekkyeon.model.ClickConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.Merchant;
import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryInsertRequest;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;

import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.KOL;
import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.REGULAR;
import static jp.ne.interspace.taekkyeon.model.Merchant.SHOPEE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link MalaysiaShopeeService}.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class MalaysiaShopeeServiceTest {

    private static final long CAMPAIGN_ID = 3L;
    private static final String PRODUCT_ID_SHOPEE = "orderId_ShopId_id1_modelId_globalCategoryLv3Name_ShopeeComm";
    private static final Long SUB_CAMPAIGN_ID = 123L;
    private static final String CATEGORY_ID_BRAND_COMMISSION = "Brand Commission";
    private static final ZoneId ZONE_ID = ZoneId.of("Asia/Kuala_Lumpur");
    private static final int PRODUCT_RESULT_ID = 3;
    private static final int CATEGORY_RESULT_ID = 30;
    private static final String CUSTOMER_TYPE = "customerType";
    private static final String CLICK_ID = "clickId";
    private static final String CATEGORY_NAME = "category name";
    private static final String CHECKOUT_ID = "checkoutId";
    private static final String CONVERSION_ID = "conversionId";
    private static final BigDecimal GROSS_COMMISSION = new BigDecimal("400");
    private static final long PURCHASE_TIME1 = 1689609500;
    private static final ZonedDateTime TIME1 = ZonedDateTime.of(2023, 7, 17, 23, 58, 20,
            0, ZoneId.of("Asia/Kuala_Lumpur"));
    private static final BigDecimal CAPPED_COMMISSION = new BigDecimal("410");
    private static final BigDecimal TOTAL_BRAND_COMMISSION = new BigDecimal("420");
    private static final BigDecimal ESTIMATED_TOTAL_COMMISSION = new BigDecimal("430");
    private static final BigDecimal ITEM_PRICE = new BigDecimal("100");
    private static final BigDecimal ACTUAL_AMOUNT = new BigDecimal("110");
    private static final BigDecimal ITEM_COMMISSION = new BigDecimal("10");
    private static final BigDecimal GROSS_BRAND_COMMISSION = new BigDecimal("20");
    private static final BigDecimal ITEM_SELLER_COMMISSION = new BigDecimal("22.4");
    private static final String MODEL_ID = "modelId";
    private static final String GLOBAL_CATEGORY_LV1_NAME = "category, name";
    private static final String GLOBAL_CATEGORY_LV3_NAME = "Lips";
    private static final String BUYER_TYPE = "EXISTING";
    private static final String UTM_CONTENT = "12345-RKVALUEQQQ-url";
    private static final String TARGET_MCN_ID = "0f840be9b8db4d3fbd5ba2ce59211f55";
    private static final String MCN_ID_DEFAULT = "fec8d47d412bcbeece3d9128ae855a7a";
    private static final Merchant SHOPEE_MERCHANT = SHOPEE;
    private static final String ORDERED_IN_SAME_SHOP = "ORDERED_IN_SAME_SHOP";
    private static final String SHOP_ID = "ShopId";
    private static final String ORDER_ID = "orderId";
    private static final String SHOP_TYPE = "shopType";
    private static final String ITEM_ID = "id1";
    private static final String EXPECTED_EXTRA_BONUS_PRODUCT_ID = "orderId_ShopId_id1_modelId_Lips_XTRAComm";
    private static final String ACTUAL_PRODUCT_ID = "id1_modelId";
    private static final int EXPECTED_RESULT_SIZE_WITH_EXTRA_BONUS_UPDATED = 3;
    private static final int EXPECTED_RESULT_SIZE_WITHOUT_SUB_EXTRA_BONUS = 3;

    @InjectMocks @Spy
    private MalaysiaShopeeService underTest;

    @Test
    public void testGetClickIdFromShouldReturnEmptyWhenSingleData() throws Exception {
        // given
        String[] utmContent = new String[] { "1" };

        // when
        String actual = underTest.getClickIdFrom(utmContent);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testParseShouldReturnEmptyListWhenResponseBodyIsNull() throws Exception {
        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(null);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testParseShouldReturnCorrectWhenCalled() throws Exception {
        // given
        ShopeeOrder shopeeOrder = mock(ShopeeOrder.class);
        when(shopeeOrder.getOrderId()).thenReturn("orderId");
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION, PURCHASE_TIME1, CAPPED_COMMISSION,
                TOTAL_BRAND_COMMISSION, ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE,
                UTM_CONTENT, null, null, Arrays.asList(shopeeOrder));
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(
                Arrays.asList(shopeeNode), null);
        String responseBody = "{}";
        doReturn(ZONE_ID).when(underTest).getZoneId();
        doReturn(conversionReport).when(underTest).parseConversionReport(
                new JsonParser().parse(responseBody).getAsJsonObject());
        long campaignId = 12;
        doReturn(campaignId).when(underTest).getCampaignId();
        doReturn(TARGET_MCN_ID).when(underTest).hashMd5By(campaignId);
        String[] utmContent = new String[] { "12345", "RKVALUEQQQ", "url" };
        doReturn(true).when(underTest).isTargetCampaignId(utmContent, TARGET_MCN_ID);

        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(responseBody);

        // then
        assertNotNull(actual);
        verify(underTest).createConversionDetails("RKVALUEQQQ", "existing", TIME1,
                "checkoutId-orderId", shopeeOrder, TARGET_MCN_ID);
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenIsMcnIdDefault()
            throws Exception {
        // given
        ShopeeItem shopeeItem1 = new ShopeeItem("id1", SHOP_ID, null, 1, ITEM_PRICE,
                BigDecimal.ZERO, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, GLOBAL_CATEGORY_LV3_NAME,
                ORDERED_IN_SAME_SHOP);
        ShopeeItem shopeeItem2 = new ShopeeItem("id1", SHOP_ID, null, 1, ITEM_PRICE,
                new BigDecimal("2000"), null, null, ITEM_SELLER_COMMISSION,
                MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null, GLOBAL_CATEGORY_LV3_NAME,
                ORDERED_IN_SAME_SHOP);
        ShopeeItem shopeeItem3 = new ShopeeItem("id3", SHOP_ID, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, GROSS_BRAND_COMMISSION, null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, GLOBAL_CATEGORY_LV3_NAME,
                ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                Arrays.asList(shopeeItem1, shopeeItem2, shopeeItem3));
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).validateDateTimeAfter(TIME1);
        doReturn(false).when(underTest).isBrandItem(SHOP_ID);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID, shopeeOrder, MCN_ID_DEFAULT);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        assertFieldConversionRegistrationDetails(actual.get(0), TIME1, CHECKOUT_ID, 100,
                CUSTOMER_TYPE, CATEGORY_NAME,
                "orderId_ShopId_id1_modelId_Lips_ShopeeComm", new BigDecimal("2110"),
                CLICK_ID);
        assertFieldConversionRegistrationDetails(actual.get(1), TIME1, CHECKOUT_ID, 100,
                CUSTOMER_TYPE, CATEGORY_NAME,
                "id1_modelId-AT1", new BigDecimal("2110"),
                CLICK_ID);
        assertFieldConversionRegistrationDetails(actual.get(2), TIME1, CHECKOUT_ID, 100,
                CUSTOMER_TYPE, CATEGORY_NAME,
                "orderId_ShopId_id3_modelId_Lips_ShopeeComm", new BigDecimal("2110"),
                CLICK_ID);
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenIsTargetCampaignId()
            throws Exception {
        // given
        String expectedShopeeItem1 = "orderId_ShopId_id1_modelId_Lips_ShopeeComm";
        String expectedItem1 = "id1_modelId-AT1";
        String expectedShopeeItem3 = "orderId_ShopId_id3_modelId_Lips_ShopeeComm";

        ShopeeItem shopeeItem1 = new ShopeeItem("id1", SHOP_ID, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, null, null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, GLOBAL_CATEGORY_LV3_NAME,
                ORDERED_IN_SAME_SHOP);
        ShopeeItem shopeeItem2 = new ShopeeItem("id1", SHOP_ID, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, null, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, GLOBAL_CATEGORY_LV3_NAME,
                ORDERED_IN_SAME_SHOP);
        ShopeeItem shopeeItem3 = new ShopeeItem("id3", SHOP_ID, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, GROSS_BRAND_COMMISSION, null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, GLOBAL_CATEGORY_LV3_NAME,
                ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                Arrays.asList(shopeeItem1, shopeeItem2, shopeeItem3));
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).validateDateTimeAfter(TIME1);
        doReturn(false).when(underTest).isBrandItem(SHOP_ID);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID, shopeeOrder, TARGET_MCN_ID);

        // then
        assertNotNull(actual);
        assertEquals(5, actual.size());
        assertFieldConversionRegistrationDetails(actual.get(0), TIME1, CHECKOUT_ID,
                CATEGORY_RESULT_ID, CUSTOMER_TYPE, CATEGORY_NAME, expectedShopeeItem1,
                ACTUAL_AMOUNT, CLICK_ID);
        assertFieldConversionRegistrationDetails(actual.get(1), TIME1, CHECKOUT_ID,
                CATEGORY_RESULT_ID, CUSTOMER_TYPE, CATEGORY_NAME, expectedItem1,
                ACTUAL_AMOUNT, CLICK_ID);
        assertFieldConversionRegistrationDetails(actual.get(2), TIME1, CHECKOUT_ID,
                3, CUSTOMER_TYPE, "Brand Commission",
                "orderId_ShopId_id1_modelId_Lips_XTRAComm", new BigDecimal("22.4"), CLICK_ID);
        assertFieldConversionRegistrationDetails(actual.get(3), TIME1, CHECKOUT_ID,
                CATEGORY_RESULT_ID, CUSTOMER_TYPE, CATEGORY_NAME,
                expectedShopeeItem3, ACTUAL_AMOUNT, CLICK_ID);
        assertFieldConversionRegistrationDetails(actual.get(4), TIME1, CHECKOUT_ID,
                3, CUSTOMER_TYPE, "Brand Commission",
                "orderId_ShopId_id3_modelId_Lips_XTRAComm", new BigDecimal("20"), CLICK_ID);
    }

    @Test
    public void testCreateConversionDetailsShouldNotReturnDataDuplicateWhenIsDuplicateConversionCheckEnabledAndDuplicateConversion()
            throws Exception {
        // given
        ShopeeItem shopeeItem1 = new ShopeeItem("id1", null, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                Arrays.asList(shopeeItem1));
        MerchantIntegrationHistoryInsertRequest request = mock(
                MerchantIntegrationHistoryInsertRequest.class);
        String key = "0-20230717-checkoutId-orderId-id1-modelId";
        doReturn(true).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(true).when(underTest).isDuplicateConversion(key);
        doReturn(0L).when(underTest).getCampaignId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID, shopeeOrder, TARGET_MCN_ID);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.size());
        verify(underTest, never()).insertDataProceeded(request);
    }

    @Test
    public void testCreateConversionDetailsShouldAddExtraBonusWhenSubCampaignIsNotBrandCampaign()
            throws Exception {
        // given
        ShopeeItem shopeeItem = new ShopeeItem("id1", SHOP_ID, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, GROSS_BRAND_COMMISSION,
                ITEM_SELLER_COMMISSION, MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null,
                GLOBAL_CATEGORY_LV3_NAME, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                Arrays.asList(shopeeItem));
        doReturn(true).when(underTest).isBrandItem(SHOP_ID);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(SUB_CAMPAIGN_ID).when(underTest).getBrandSubCampaignIdFrom(REGULAR, SHOP_ID);
        doReturn(false).when(underTest).isBrandCampaign(SUB_CAMPAIGN_ID);
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).validateDateTimeAfter(TIME1);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(true).when(underTest).isConversionUnique(anyString());
        doReturn(true).when(underTest).isValidCommission(ITEM_SELLER_COMMISSION);
        doReturn(EXPECTED_EXTRA_BONUS_PRODUCT_ID).when(underTest)
                .getProductId(shopeeItem, ORDER_ID, ACTUAL_PRODUCT_ID,
                        ITEM_SELLER_COMMISSION, "XTRAComm");

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID, shopeeOrder, TARGET_MCN_ID);

        // then
        assertNotNull(actual);
        assertEquals(4, actual.size());
        assertEquals(CATEGORY_RESULT_ID, actual.get(0).getResultId());
        assertEquals("category name", actual.get(0).getProductCategoryId());
        assertEquals(ACTUAL_AMOUNT, actual.get(0).getProductUnitPrice());

        assertEquals(CATEGORY_RESULT_ID, actual.get(1).getResultId());
        assertEquals("category name", actual.get(1).getProductCategoryId());
        assertEquals(ACTUAL_AMOUNT, actual.get(1).getProductUnitPrice());

        assertEquals(3, actual.get(2).getResultId());
        assertEquals("Brand Commission", actual.get(2).getProductCategoryId());
        assertEquals("orderId_ShopId_id1_modelId_Lips_XTRAComm", actual.get(2).getProductId());
        assertEquals(new BigDecimal("22.4"), actual.get(2).getProductUnitPrice());

        assertEquals(3, actual.get(3).getResultId());
        assertEquals("Brand Commission", actual.get(3).getProductCategoryId());
        assertEquals("orderId_ShopId_id1_modelId_Lips_XTRAComm", actual.get(3).getProductId());
        assertEquals(new BigDecimal("22.4"), actual.get(3).getProductUnitPrice());
    }

    @Test
    public void testCreateConversionDetailsShouldNotAddExtraBonusWhenSubCampaignIsBrandCampaign()
            throws Exception {
        // given
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, SHOP_ID, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, GROSS_BRAND_COMMISSION,
                ITEM_SELLER_COMMISSION, MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null,
                GLOBAL_CATEGORY_LV3_NAME, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder(ORDER_ID, SHOP_TYPE,
                Arrays.asList(shopeeItem));

        doReturn(true).when(underTest).isBrandItem(SHOP_ID);
        doReturn(KOL).when(underTest).findCampaignType(CAMPAIGN_ID);

        doReturn(SUB_CAMPAIGN_ID).when(underTest).getBrandSubCampaignIdFrom(KOL, SHOP_ID);
        doReturn(true).when(underTest).isBrandCampaign(SUB_CAMPAIGN_ID);

        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).validateDateTimeAfter(TIME1);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(true).when(underTest).isConversionUnique(anyString());

        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>();
        ConversionRegistrationDetails mainConversion = new ConversionRegistrationDetails(
                TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID, CUSTOMER_TYPE,
                GLOBAL_CATEGORY_LV1_NAME, PRODUCT_ID_SHOPEE, ACTUAL_AMOUNT, CLICK_ID);
        ClickConversionRegistrationDetails subConversion =
                new ClickConversionRegistrationDetails(TIME1, CHECKOUT_ID,
                        CATEGORY_RESULT_ID, CUSTOMER_TYPE, GLOBAL_CATEGORY_LV1_NAME,
                        PRODUCT_ID_SHOPEE, ACTUAL_AMOUNT, CLICK_ID, SUB_CAMPAIGN_ID);
        brandItemConversions.add(mainConversion);
        brandItemConversions.add(subConversion);

        ConversionRegistrationDetails extraBonusForMainCampaign =
                new ConversionRegistrationDetails(TIME1, CHECKOUT_ID, PRODUCT_RESULT_ID,
                        CUSTOMER_TYPE, CATEGORY_ID_BRAND_COMMISSION,
                        EXPECTED_EXTRA_BONUS_PRODUCT_ID,
                        ITEM_SELLER_COMMISSION, CLICK_ID);

        List<ConversionRegistrationDetails> processedConversions = new ArrayList<>(
                brandItemConversions);
        processedConversions.add(extraBonusForMainCampaign);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID, shopeeOrder, TARGET_MCN_ID);

        // then
        assertNotNull(actual);
        assertEquals(EXPECTED_RESULT_SIZE_WITHOUT_SUB_EXTRA_BONUS, actual.size());

        assertEquals(CATEGORY_RESULT_ID, actual.get(0).getResultId());
        assertTrue(actual.get(1) instanceof ClickConversionRegistrationDetails);
        assertEquals(SUB_CAMPAIGN_ID,
                ((ClickConversionRegistrationDetails) actual.get(1)).getSubCampaignId());
        assertFalse(actual.get(2) instanceof ClickConversionRegistrationDetails);
        assertEquals(PRODUCT_RESULT_ID, actual.get(2).getResultId());
        assertEquals(CATEGORY_ID_BRAND_COMMISSION, actual.get(2).getProductCategoryId());
        assertEquals(ITEM_SELLER_COMMISSION, actual.get(2).getProductUnitPrice());
    }

    private void assertFieldConversionRegistrationDetails(
            ConversionRegistrationDetails actual, ZonedDateTime conversionTime,
            String transactionId, int resultId, String customerType,
            String productCategoryId, String productId, BigDecimal productUnitPrice,
            String clickId) {
        assertNotNull(actual);
        assertEquals(conversionTime, actual.getConversionTime());
        assertEquals(transactionId, actual.getTransactionId());
        assertEquals(resultId, actual.getResultId());
        assertEquals(customerType, actual.getCustomerType());
        assertEquals(productCategoryId, actual.getProductCategoryId());
        assertEquals(productId, actual.getProductId());
        assertEquals(productUnitPrice, actual.getProductUnitPrice());
        assertEquals(clickId, actual.getClickId());
    }

    private void assertMainConversionDetails(ConversionRegistrationDetails actual) {
        assertNotNull(actual);
        assertEquals(CATEGORY_RESULT_ID, actual.getResultId());
        assertEquals(TIME1, actual.getConversionTime());
        assertEquals(CHECKOUT_ID, actual.getTransactionId());
        assertEquals(CUSTOMER_TYPE, actual.getCustomerType());
        assertEquals("category name", actual.getProductCategoryId());
        assertEquals(ACTUAL_AMOUNT, actual.getProductUnitPrice());
        assertEquals(CLICK_ID, actual.getClickId());
        assertFalse(actual instanceof ClickConversionRegistrationDetails);
    }

    private void assertSubConversionDetails(ConversionRegistrationDetails actual) {
        assertNotNull(actual);
        assertTrue(actual instanceof ClickConversionRegistrationDetails);
        ClickConversionRegistrationDetails subConversion =
                (ClickConversionRegistrationDetails) actual;
        assertEquals(SUB_CAMPAIGN_ID, subConversion.getSubCampaignId());
        assertEquals(CATEGORY_RESULT_ID, actual.getResultId());
        assertEquals(TIME1, actual.getConversionTime());
        assertEquals(CHECKOUT_ID, actual.getTransactionId());
        assertEquals(CUSTOMER_TYPE, actual.getCustomerType());
        assertEquals("category name", actual.getProductCategoryId());
        assertEquals(ACTUAL_AMOUNT, actual.getProductUnitPrice());
        assertEquals(CLICK_ID, actual.getClickId());
    }

    private void assertExtraBonusConversionDetails(ConversionRegistrationDetails actual) {
        assertNotNull(actual);
        assertTrue(actual instanceof ClickConversionRegistrationDetails);
        ClickConversionRegistrationDetails extraBonus =
                (ClickConversionRegistrationDetails) actual;
        assertEquals(SUB_CAMPAIGN_ID, extraBonus.getSubCampaignId());
        assertEquals(PRODUCT_RESULT_ID, actual.getResultId());
        assertEquals(TIME1, actual.getConversionTime());
        assertEquals(CHECKOUT_ID, actual.getTransactionId());
        assertEquals(CUSTOMER_TYPE, actual.getCustomerType());
        assertEquals(CATEGORY_ID_BRAND_COMMISSION, actual.getProductCategoryId());
        assertEquals(EXPECTED_EXTRA_BONUS_PRODUCT_ID, actual.getProductId());
        assertEquals(ITEM_SELLER_COMMISSION, actual.getProductUnitPrice());
        assertEquals(CLICK_ID, actual.getClickId());
    }
}
