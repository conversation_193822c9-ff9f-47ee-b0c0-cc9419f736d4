/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for brand bidding device.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum BrandBiddingDetectionDevice implements ValueEnum {

    IPHONE(0, true),
    IPAD_PRO(1, true),
    SAMSUNG_GALAXY_S20(2,true),
    SAMSUNG_GALAXY_TAB_S7(3, true),
    WINDOWS_PC(4, false),
    MACBOOK_PRO(5, false);

    private final int value;
    private final boolean isMobile;
}
