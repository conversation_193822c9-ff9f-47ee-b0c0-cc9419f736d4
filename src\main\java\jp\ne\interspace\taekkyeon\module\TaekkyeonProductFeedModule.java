/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.util.Map;

import javax.inject.Singleton;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableTable;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.name.Named;

import jp.ne.interspace.taekkyeon.model.ProductCsvColumnIndexes;

import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Country.VIETNAM;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Taekkyeon module for product feed.
 *
 * <AUTHOR>
 */
public class TaekkyeonProductFeedModule extends AbstractModule {

    public static final String BIND_KEY_PRODUCT_COLUMN_INDEXES = "product.column.indexes";
    public static final String BIND_KEY_PRODUCT_FEED_PATH = "product.feed.paths";

    private static final ImmutableTable<Country, Environment, String> S3_BUCKET_PATHS = new ImmutableTable.Builder<Country, Environment, String>()
            .put(THAILAND, DEV, "datafeed.accesstrade.test/output")
            .put(INDONESIA, DEV, "datafeed.accesstrade.test/output")
            .put(VIETNAM, DEV, "datafeed.accesstrade.test/output")
            .put(THAILAND, STAGING, "datafeed.accesstrade.test/output")
            .put(INDONESIA, STAGING, "datafeed.accesstrade.test/output")
            .put(VIETNAM, STAGING, "datafeed.accesstrade.test/output")
            .put(THAILAND, PRODUCTION, "dfo.accesstrade.co.id/production_th")
            .put(INDONESIA, PRODUCTION, "dfo.accesstrade.co.id/production")
            .put(VIETNAM, PRODUCTION, "datafeed.accesstrade.vn/output")
            .build();

    private static final Map<Country, ProductCsvColumnIndexes> PRODUCT_COLUMNS_INDEXES = new ImmutableMap.Builder<Country, ProductCsvColumnIndexes>()
            .put(INDONESIA, new ProductCsvColumnIndexes(0, 1, 2, 4, 6, 7, 8, 13))
            .put(THAILAND, new ProductCsvColumnIndexes(0, 1, 2, 4, 6, 7, 8, 13))
            .put(VIETNAM, new ProductCsvColumnIndexes(0, 1, 2, 3, 4, 5, 6, 7)).build();

    @Override
    protected void configure() {
        // do nothing.
    }

    @Provides @Singleton @Named(BIND_KEY_PRODUCT_FEED_PATH)
    private String provideProductFeedPaths() {
        return S3_BUCKET_PATHS.get(getCurrentCountry(), getCurrentEnvironment());
    }

    @Provides @Singleton @Named(BIND_KEY_PRODUCT_COLUMN_INDEXES)
    private ProductCsvColumnIndexes provideProductColumnIndexes() {
        return PRODUCT_COLUMNS_INDEXES.get(getCurrentCountry());
    }
}
