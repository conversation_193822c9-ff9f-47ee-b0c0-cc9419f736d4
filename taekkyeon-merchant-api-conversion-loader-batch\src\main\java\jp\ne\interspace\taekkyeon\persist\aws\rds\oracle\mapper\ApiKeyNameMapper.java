/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.CampaignAdvType;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybatis mapper for API key name data.
 *
 * <AUTHOR> Van
 */
public interface ApiKeyNameMapper {

    /**
        SELECT
            adv_parent_campaign_id
        FROM
            api_key_name
        WHERE
            campaign_id = #{brandCampaignId}
        AND
            campaign_adv_type = #{campaignAdvType}
     */
    @Multiline String SELECT_PARENT_CAMPAIGN_ID_BY_BRAND_CAMPAIGN = "";

    /**
     * Returns the campaign ID by the given parameters.
     *
     * @param campaignAdvType
     *            the given campaign ADV type
     * @param brandCampaignId
     *            the given campaign ID
     * @return the campaign ID by the given parameters
     */
    @Select(SELECT_PARENT_CAMPAIGN_ID_BY_BRAND_CAMPAIGN)
    Long findParentCampaignIdBy(@Param("campaignAdvType") String campaignAdvType,
            @Param("brandCampaignId") long brandCampaignId);

    /**
        SELECT
            campaign_id
        FROM
            api_key_name
        WHERE
            adv_parent_campaign_id = #{parentCampaignId}
        AND
            shop_id_mapping = #{shopId}
     */
    @Multiline String SELECT_BRAND_CAMPAIGN_BY_PARENT_CAMPAIGN = "";

    /**
     * Returns the campaign ID by the given parameters.
     *
     * @param shopId
     *            the given shopId of the shop
     * @param parentCampaignId
     *            the given parent campaignId
     * @return campaign ID by the given parameters
     */
    @Select(SELECT_BRAND_CAMPAIGN_BY_PARENT_CAMPAIGN)
    Long findBrandCampaignId(@Param("shopId") String shopId,
            @Param("parentCampaignId") Long parentCampaignId);

    /**
        SELECT
            campaign_adv_type
        FROM
            api_key_name
        WHERE
            campaign_id = #{campaignId}
        AND
            adv_platform = 'SHOPEE'
     */
    @Multiline String SELECT_ADV_CAMPAIGN_BY = "";

    /**
     * Returns the campaign ADV type by the given {@code campaignId}.
     *
     * @param campaignId
     *            the given campaign ID
     * @return the campaign ADV type
     */
    @Select(SELECT_ADV_CAMPAIGN_BY)
    CampaignAdvType findAdvCampaignBy(long campaignId);

    /**
        SELECT
            COUNT(*)
        FROM
            api_key_name
        WHERE
            shop_id_mapping = #{shopId}
        AND
            adv_platform = 'SHOPEE'
     */
    @Multiline String COUNT_BRAND_ITEM_BY = "";

    /**
     * Returns true, when shop ID is found for given shop ID, otherwise false.
     *
     * @param shopId
     *            the given shop ID
     * @return true, when shop ID is found, otherwise false
     */
    @Select(COUNT_BRAND_ITEM_BY)
    boolean isBrandItem(String shopId);

    /**
        SELECT
            campaign_id
        FROM
            api_key_name
        WHERE
            shop_id_mapping = #{shopId}
        AND
            adv_platform = 'SHOPEE'
     */
    @Multiline String FIND_CAMPAIGN_BY_SHOP_ID = "";

    /**
     * Returns the campaign ID by the given shop ID.
     *
     * @param shopId
     *            the given shop ID
     * @return campaign ID for the given shop ID
     */
    @Select(FIND_CAMPAIGN_BY_SHOP_ID)
    Long findCampaignBy(@Param("shopId") String shopId);
}
