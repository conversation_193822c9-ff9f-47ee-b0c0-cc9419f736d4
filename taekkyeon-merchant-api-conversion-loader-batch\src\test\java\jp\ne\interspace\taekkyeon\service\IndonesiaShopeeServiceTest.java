/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.common.collect.ImmutableMap;
import com.google.gson.JsonParser;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import jp.ne.interspace.taekkyeon.model.CampaignAdvType;
import jp.ne.interspace.taekkyeon.model.CampaignConditionDetails;
import jp.ne.interspace.taekkyeon.model.ClickConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.Merchant;
import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryInsertRequest;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.MerchantIntegrationHistoryMapper;

import static com.google.common.collect.ImmutableMap.of;
import static java.util.Arrays.asList;
import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.REGULAR;
import static jp.ne.interspace.taekkyeon.model.Merchant.SHOPEE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link IndonesiaShopeeService}.
 *
 * <AUTHOR> Shin
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class IndonesiaShopeeServiceTest {

    private static final long CAMPAIGN_ID = 123L;
    private static final ZoneId ZONE_ID = ZoneId.of("Asia/Jakarta");
    public static final String INDONESIA_SHOPEE_API_KEY_STORES = "INDONESIA_SHOPEE_API_KEY_STORES";
    private static final int CATEGORY_RESULT_ID = 30;
    private static final int RESULT_ID = 3;
    private static final String CUSTOMER_TYPE = "customerType";
    private static final String CLICK_ID = "clickId";
    private static final String CHECKOUT_ID = "checkoutId";
    private static final String CONVERSION_ID = "conversionId";
    private static final BigDecimal GROSS_COMMISSION = new BigDecimal("400");
    private static final long PURCHASE_TIME1 = 1689613100;
    private static final ZonedDateTime TIME1 = ZonedDateTime.of(2023, 7, 17, 23, 58, 20,
            0, ZoneId.of("Asia/Jakarta"));
    private static final BigDecimal CAPPED_COMMISSION = new BigDecimal("410");
    private static final BigDecimal TOTAL_BRAND_COMMISSION = new BigDecimal("420");
    private static final BigDecimal ESTIMATED_TOTAL_COMMISSION = new BigDecimal("430");
    private static final BigDecimal ITEM_PRICE = new BigDecimal("100");
    private static final BigDecimal ACTUAL_AMOUNT = new BigDecimal("110");
    private static final BigDecimal ITEM_COMMISSION = new BigDecimal("10");
    private static final BigDecimal GROSS_BRAND_COMMISSION = new BigDecimal("20");
    private static final String MODEL_ID = "modelId";
    private static final String GLOBAL_CATEGORY_LV1_NAME = "category, name";
    private static final String BUYER_TYPE = "EXISTING";
    private static final String UTM_CONTENT_MERCHANT_CAMP_973 = "12345-RKVALUEQQQ-url-adf854f418fc96fb01ad92a2ed2fc35c";
    private static final String UTM_CONTENT_MERCHANT_CAMP_6081 = "12345-RKVALUEQQQ-url-ca75910166da03ff9d4655a0338e6b09";
    private static final String MD5_HASHED_CAMPAIGN_ID_973 = "adf854f418fc96fb01ad92a2ed2fc35c";
    private static final String MD5_HASHED_CAMPAIGN_ID_6081 = "ca75910166da03ff9d4655a0338e6b09";
    private static final String ITEM_NAME = "itemName";
    private static final String SHOP_ID = "shopId";
    private static final String AND = "AND";
    private static final String OR = "OR";
    private static final String GROUP_ID = "groupId";
    private static final int CAMPAIGN_ID_123 = 123;
    private static final int CAMPAIGN_ID_111 = 111;
    private static final String SHOP_ID_VALUES = "test_shopId";
    private static final String ITEM_NAME_VALUES = "test_itemName";
    private static final Merchant SHOPEE_MERCHANT = SHOPEE;
    private static final String ORDERED_IN_SAME_SHOP = "ORDERED_IN_SAME_SHOP";
    private static final BigDecimal ITEM_SELLER_COMMISSION = new BigDecimal("20");

    @InjectMocks @Spy
    private IndonesiaShopeeService underTest;

    @Mock
    private MerchantIntegrationHistoryMapper merchantIntegrationHistoryMapper;

    @Mock
    private CampaignMapper campaignMapper;

    @Test
    public void testParseShouldReturnEmptyListWhenResponseBodyIsNull() throws Exception {
        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(null);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testParseShouldCallCreateConversionDetailsMethodWhenConversionTimeIsAfterTargetDate()
            throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = mock(
                CampaignConditionDetails.class);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                of("123", conditionDetails);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType", null);
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION, PURCHASE_TIME1, CAPPED_COMMISSION,
                TOTAL_BRAND_COMMISSION, ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE,
                UTM_CONTENT_MERCHANT_CAMP_6081, null, null, asList(shopeeOrder));
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(
                asList(shopeeNode), null);
        String responseBody = "{}";
        doReturn(conversionReport).when(underTest).parseConversionReport(
                new JsonParser().parse(responseBody).getAsJsonObject());
        List<ConversionRegistrationDetails> details =
                asList(mock(ConversionRegistrationDetails.class));
        long campaignId = 6081;
        doReturn(campaignId).when(underTest).getCampaignId();
        doReturn(MD5_HASHED_CAMPAIGN_ID_6081).when(underTest).hashMd5By(campaignId);
        doReturn(details).when(underTest)
                .createConversionDetails("RKVALUEQQQ", "existing", TIME1,
                        "checkoutId", shopeeOrder, campaignConditionDetails);
        doReturn(campaignConditionDetails).when(underTest)
                .groupConditionDetailsCacheKey(campaignId);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(responseBody);

        // then
        assertNotNull(actual);
        verify(underTest).createConversionDetails("RKVALUEQQQ", "existing", TIME1,
                "checkoutId",
                shopeeOrder, campaignConditionDetails);
    }

    @Test
    public void testParseShouldCallCreateConversionDetailsMethodWhenCampaignIdIsKol1()
            throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = mock(
                CampaignConditionDetails.class);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                of("123", conditionDetails);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType", null);
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION, PURCHASE_TIME1, CAPPED_COMMISSION,
                TOTAL_BRAND_COMMISSION, ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE,
                UTM_CONTENT_MERCHANT_CAMP_973, null, null, asList(shopeeOrder));
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(
                asList(shopeeNode), null);
        String responseBody = "{}";
        doReturn(conversionReport).when(underTest).parseConversionReport(
                new JsonParser().parse(responseBody).getAsJsonObject());
        List<ConversionRegistrationDetails> details =
                asList(mock(ConversionRegistrationDetails.class));
        long campaignId = 973;
        doReturn(campaignId).when(underTest).getCampaignId();
        doReturn(MD5_HASHED_CAMPAIGN_ID_973).when(underTest).hashMd5By(campaignId);
        doReturn(details).when(underTest)
                .createConversionDetails("RKVALUEQQQ", "existing", TIME1,
                "checkoutId", shopeeOrder, campaignConditionDetails);
        doReturn(campaignConditionDetails).when(underTest)
                .groupConditionDetailsCacheKey(campaignId);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(responseBody);

        // then
        assertNotNull(actual);
        verify(underTest).createConversionDetails("RKVALUEQQQ", "existing", TIME1,
                "checkoutId",
                shopeeOrder, campaignConditionDetails);
    }

    @Test
    public void testIsStoreCampaignShouldReturnTrueWhenSecretNameEqualShopeeApiKey()
            throws Exception {
        // given
        when(underTest.getKeySecretName()).thenReturn(INDONESIA_SHOPEE_API_KEY_STORES);

        // when
        boolean actual = underTest.isStoreCampaign();

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsStoreCampaignShouldReturnFalseWhenSecretNameEqualShopeeApiKey()
            throws Exception {
        // given
        when(underTest.getKeySecretName()).thenReturn("STORES");

        // when
        boolean actual = underTest.isStoreCampaign();

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsConversionValidShouldReturnFalseWhenItemNameRequestNullOrEmpty()
            throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = new CampaignConditionDetails(
                123, ITEM_NAME, "test", null, AND);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                of("123", conditionDetails);

        ShopeeItem shopeeItem = new ShopeeItem("id1", SHOP_ID, null, 1, ITEM_PRICE, null,
                ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);

        // when
        boolean actual = underTest
                .isConversionValid(campaignConditionDetails, shopeeItem);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsConversionValidShouldReturnFalseWhenShopIdRequestNullOrEmpty()
            throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = new CampaignConditionDetails(
                123, SHOP_ID, "test", null, AND);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                of("123", conditionDetails);
        ShopeeItem shopeeItem = new ShopeeItem("id1", null, ITEM_NAME, 1, ITEM_PRICE,
                null, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);

        // when
        boolean actual = underTest
                .isConversionValid(campaignConditionDetails, shopeeItem);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsConversionValidShouldReturnFalseWhenShopIdIsValid()
            throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = new CampaignConditionDetails(
                123, SHOP_ID, "test", null, AND);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                of("123", conditionDetails);
        ShopeeItem shopeeItem = new ShopeeItem("id1", null, ITEM_NAME, 1, ITEM_PRICE,
                null, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);

        // when
        boolean actual = underTest
                .isConversionValid(campaignConditionDetails, shopeeItem);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsConversionValidShouldReturnTrueWhenConditionValueHasShopIdIsOr()
            throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = new CampaignConditionDetails(
                123, SHOP_ID, "test", null, OR);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                of("123", conditionDetails);
        ShopeeItem shopeeItem = new ShopeeItem("id1", "test", ITEM_NAME, 1, ITEM_PRICE,
                null, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);

        // when
        boolean actual = underTest
                .isConversionValid(campaignConditionDetails, shopeeItem);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsConversionValidShouldReturnTrueWhenOperatorIsOrAndMatchOperatorDatabase()
            throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = new CampaignConditionDetails(
                123, SHOP_ID, "161143541,2342343", "Group_And", AND);
        CampaignConditionDetails campaignConditionDetail2 = new CampaignConditionDetails(
                123, ITEM_NAME, "imboo", "Group_And", AND);
        List<CampaignConditionDetails> conditionDetails = asList(
                campaignConditionDetail, campaignConditionDetail2);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                of("123", conditionDetails);
        ShopeeItem shopeeItem = new ShopeeItem("id1", "161143541", "imboost", 1,
                ITEM_PRICE, null, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);

        // when
        boolean actual = underTest
                .isConversionValid(campaignConditionDetails, shopeeItem);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsConversionValidShouldReturnTrueWhenTwoGroupOfConditionsAndOneOfGroupConditionGotSatisfiedTheCondition()
            throws Exception {
        // given
        CampaignConditionDetails campaignShopIdConditionDetail1 = new CampaignConditionDetails(
                123, SHOP_ID, "161143wer2343", "Group_And", AND);
        CampaignConditionDetails campaignItemNameConditionDetail1 = new CampaignConditionDetails(
                123, ITEM_NAME, "imwero", "Group_And", AND);
        CampaignConditionDetails campaignShopIdConditionDetail2 = new CampaignConditionDetails(
                124, SHOP_ID, "16114342343", "Group_OR", OR);
        CampaignConditionDetails campaignItemNameConditionDetail2 = new CampaignConditionDetails(
                124, ITEM_NAME, "imbo", "Group_OR", OR);
        List<CampaignConditionDetails> conditionDetails1 = asList(
                campaignShopIdConditionDetail1, campaignItemNameConditionDetail1);
        List<CampaignConditionDetails> conditionDetails2 = asList(
                campaignShopIdConditionDetail2, campaignItemNameConditionDetail2);
        Map<String, List<CampaignConditionDetails>> campaignConditionDetails = new HashMap<>();
        campaignConditionDetails.put("123",conditionDetails1);
        campaignConditionDetails.put("124",conditionDetails2);

        ShopeeItem shopeeItem = new ShopeeItem("id1", "161143541", "imboost", 1,
                ITEM_PRICE, null, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION,
                MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);

        // when
        boolean actual = underTest
                .isConversionValid(campaignConditionDetails, shopeeItem);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsConversionValidShouldReturnFalseWhenTwoGroupOfConditionsAndBothOfGroupConditionNotSatisfiedCondition()
            throws Exception {
        // given
        CampaignConditionDetails campaignShopIdConditionDetail1 = new CampaignConditionDetails(
                123, SHOP_ID, "161143wer2343", "Group_And", AND);
        CampaignConditionDetails campaignItemNameConditionDetail1 = new CampaignConditionDetails(
                123, ITEM_NAME, "imwero", "Group_And", AND);
        CampaignConditionDetails campaignShopIdConditionDetail2 = new CampaignConditionDetails(
                124, SHOP_ID, "16114342343", "Group_OR", OR);
        CampaignConditionDetails campaignItemNameConditionDetail2 = new CampaignConditionDetails(
                124, ITEM_NAME, "imbsdfdso", "Group_OR", OR);
        List<CampaignConditionDetails> conditionDetails1 = asList(
                campaignShopIdConditionDetail1, campaignItemNameConditionDetail1);
        List<CampaignConditionDetails> conditionDetails2 = asList(
                campaignShopIdConditionDetail2, campaignItemNameConditionDetail2);
        Map<String, List<CampaignConditionDetails>> campaignConditionDetails = new HashMap<>();
        campaignConditionDetails.put("123",conditionDetails1);
        campaignConditionDetails.put("124",conditionDetails2);

        ShopeeItem shopeeItem = new ShopeeItem("id1", "161143541", "imboost", 1,
                ITEM_PRICE, null, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);

        // when
        boolean actual = underTest
                .isConversionValid(campaignConditionDetails, shopeeItem);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsConversionValidShouldReturnCheckItemNameWhenShopIdInRequestIsNull()
            throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail2 = new CampaignConditionDetails(
                123, ITEM_NAME, "imboost", "Group_And", AND);
        List<CampaignConditionDetails> conditionDetails = asList(
                campaignConditionDetail2);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails = of(
                "123", conditionDetails);
        ShopeeItem shopeeItem = new ShopeeItem("id1", null, "imboost", 1, ITEM_PRICE,
                null, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);

        // when
        boolean actual = underTest
                .isConversionValid(campaignConditionDetails, shopeeItem);

        // then
        assertTrue(actual);
    }

    @Test
    public void testGroupConditionDetailsCacheKeyShouldReturnCorrectWhenCalled()
            throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = new CampaignConditionDetails(
                CAMPAIGN_ID_123, SHOP_ID, "test", GROUP_ID, AND);
        List<CampaignConditionDetails> conditionDetails = asList(
                campaignConditionDetail);
        when(campaignMapper.findCampaignConditionBy(CAMPAIGN_ID_123)).thenReturn(
                conditionDetails);
        ImmutableMap<String, List<CampaignConditionDetails>> expected =
                of(GROUP_ID, conditionDetails);

        // when
        Map<String, List<CampaignConditionDetails>> actual = underTest
                .groupConditionDetailsCacheKey(CAMPAIGN_ID_123);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = new CampaignConditionDetails(
                CAMPAIGN_ID_123, SHOP_ID, SHOP_ID_VALUES, GROUP_ID, AND);
        CampaignConditionDetails campaignConditionDetail2 = new CampaignConditionDetails(
                CAMPAIGN_ID_111, ITEM_NAME, ITEM_NAME_VALUES,GROUP_ID, AND);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail,
                campaignConditionDetail2);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails = of(
                "123", conditionDetails);
        ShopeeItem shopeeItem1 = new ShopeeItem("id1", SHOP_ID_VALUES, ITEM_NAME_VALUES,
                1, ITEM_PRICE, null, ITEM_COMMISSION, null, null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, "globalCategoryLv3Name",
                ORDERED_IN_SAME_SHOP);
        ShopeeItem shopeeItem2 = new ShopeeItem("id1", SHOP_ID_VALUES, ITEM_NAME_VALUES,
                1, ITEM_PRICE, new BigDecimal("2000"), ITEM_COMMISSION, null, null,
                MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null, "globalCategoryLv3Name",
                ORDERED_IN_SAME_SHOP);
        ShopeeItem shopeeItem3 = new ShopeeItem("id3", SHOP_ID_VALUES, ITEM_NAME_VALUES,
                1, ITEM_PRICE, ACTUAL_AMOUNT, ITEM_COMMISSION, GROSS_BRAND_COMMISSION,
                null, MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null, "globalCategoryLv3Name",
                ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                asList(shopeeItem1, shopeeItem2, shopeeItem3));
        when(underTest.getKeySecretName()).thenReturn(INDONESIA_SHOPEE_API_KEY_STORES);
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandItem(SHOP_ID_VALUES);
        doReturn(CampaignAdvType.REGULAR).when(underTest)
                .findCampaignType(CAMPAIGN_ID);

        ClickConversionRegistrationDetails subConversion =
                new ClickConversionRegistrationDetails(TIME1, CHECKOUT_ID,
                        CATEGORY_RESULT_ID, "customerType-direct", "category name",
                        "orderId_id3_modelId", ACTUAL_AMOUNT, CLICK_ID, 123L);
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>(
                Arrays.asList(subConversion));
        doReturn(brandItemConversions).when(underTest)
                .processConversionByBrandItemRules(TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID,
                        "customerType-direct", "category name", "orderId_id3_modelId",
                        ACTUAL_AMOUNT, CLICK_ID, SHOP_ID_VALUES, false);

        doReturn("ID").when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest
                .createConversionDetails(CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID,
                        shopeeOrder, campaignConditionDetails);

        // then
        assertNotNull(actual);
        assertEquals(5, actual.size());
        ConversionRegistrationDetails expected1 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType-indirect", "category name",
                "orderId_id1_modelId", null, CLICK_ID);
        ConversionRegistrationDetails expected2 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType-indirect", "category name",
                "orderId_id1_modelId-AT1", new BigDecimal("2000"), CLICK_ID);
        ConversionRegistrationDetails expected3 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType-direct", "category name",
                "orderId_id3_modelId", ACTUAL_AMOUNT, CLICK_ID);
        ConversionRegistrationDetails expected4 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, RESULT_ID, "customerType-direct", null,
                "orderId_id3_modelId-bonus", GROSS_BRAND_COMMISSION, CLICK_ID);
        ConversionRegistrationDetails expected5 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, RESULT_ID, "customerType-direct", null,
                "orderId_id3_modelId-bonus", GROSS_BRAND_COMMISSION, CLICK_ID);
        assertEquals(expected1, actual.get(0));
        assertEquals(expected2, actual.get(1));
        assertEquals(expected3, actual.get(2));
        assertEquals(expected4, actual.get(3));
        assertEquals(expected5, actual.get(4));

        verify(underTest, times(3)).insertDataProceeded(any(
                MerchantIntegrationHistoryInsertRequest.class));
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhengrossBrandCommissionNotNullAndGreaterThanZeroAndIsNotStoreCampaign() throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = new CampaignConditionDetails(
                CAMPAIGN_ID_123, SHOP_ID, SHOP_ID_VALUES, GROUP_ID, OR);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                of("123", conditionDetails);
        ShopeeItem shopeeItem1 = new ShopeeItem("id1", SHOP_ID_VALUES, null, 1,
                ITEM_PRICE, null, ITEM_COMMISSION, BigDecimal.valueOf(1), null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, "globalCategoryLv3Name", ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                asList(shopeeItem1));
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandItem(SHOP_ID_VALUES);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);

        ClickConversionRegistrationDetails subConversion =
                new ClickConversionRegistrationDetails(TIME1, CHECKOUT_ID,
                        CATEGORY_RESULT_ID, "customerType-indirect", "category name",
                        "orderId_id1_modelId", null, CLICK_ID, 123L);
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>(
                Arrays.asList(subConversion));
        doReturn(brandItemConversions).when(underTest)
                .processConversionByBrandItemRules(TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID,
                        "customerType-indirect", "category name", "orderId_id1_modelId",
                        null, CLICK_ID, SHOP_ID_VALUES, false);

        doReturn("ID").when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest
                .createConversionDetails(CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID,
                        shopeeOrder, campaignConditionDetails);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        ConversionRegistrationDetails expected1 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType-indirect", "category name",
                "orderId_id1_modelId", null, CLICK_ID);
        ConversionRegistrationDetails expected2 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, RESULT_ID, "customerType-indirect", null,
                "orderId_id1_modelId-bonus", new BigDecimal("1"), CLICK_ID);
        ConversionRegistrationDetails expected3 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, RESULT_ID, "customerType-indirect", null,
                "orderId_id1_modelId-bonus", new BigDecimal("1"), CLICK_ID);
        assertEquals(expected1, actual.get(0));
        assertEquals(expected2, actual.get(1));
        assertEquals(expected3, actual.get(2));
        verify(underTest, times(1)).insertDataProceeded(any(
                MerchantIntegrationHistoryInsertRequest.class));
    }

    @Test
    public void testCreateConversionDetailsShouldCheckDuplicateAndReturnCorrectDataWhenIsDuplicateConversionCheckEnabled() throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = mock(
                CampaignConditionDetails.class);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                ImmutableMap.of("123", conditionDetails);
        ShopeeItem shopeeItem1 = new ShopeeItem("id1", null, null, 1, ITEM_PRICE, null,
                ITEM_COMMISSION, BigDecimal.valueOf(1), null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, "globalCategoryLv3Name", ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                asList(shopeeItem1));
        String key = "123-20230717-checkoutId-orderId-id1-modelId";
        doReturn(true).when(underTest).isDuplicateConversion(key);
        doReturn(true).when(underTest).isDuplicateConversionCheckEnabled();

        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(true).when(underTest).isConversionValid(
                campaignConditionDetails, shopeeItem1);

        // when
        List<ConversionRegistrationDetails> actual = underTest
                .createConversionDetails(CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID,
                        shopeeOrder, campaignConditionDetails);

        // then
        assertEquals(0, actual.size());
        verify(underTest).isDuplicateConversion(key);
        verify(underTest, times(0)).insertDataProceeded(any(
                MerchantIntegrationHistoryInsertRequest.class));
    }

    @Test
    public void testCreateConversionDetailsShouldNotReturnDataDuplicateWhenIsDuplicateConversionCheckEnabledAndNotDuplicateConversion() throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = mock(
                CampaignConditionDetails.class);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                ImmutableMap.of("123", conditionDetails);
        ShopeeItem shopeeItem1 = new ShopeeItem("id1", null, null, 1, ITEM_PRICE, null,
                ITEM_COMMISSION, BigDecimal.valueOf(1), null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, "globalCategoryLv3Name", ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                asList(shopeeItem1));
        String key = "123-20230717-checkoutId-orderId-id1-modelId";
        doReturn(true).when(underTest)
                .isConversionValid(campaignConditionDetails, shopeeItem1);
        doReturn(true).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(false).when(underTest).isDuplicateConversion(key);
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandItem(null);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);

        ClickConversionRegistrationDetails subConversion =
                new ClickConversionRegistrationDetails(TIME1, CHECKOUT_ID,
                        CATEGORY_RESULT_ID, "customerType-indirect", "category name",
                        "orderId_id1_modelId", null, CLICK_ID, 123L);
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>(
                Arrays.asList(subConversion));
        doReturn(brandItemConversions).when(underTest)
                .processConversionByBrandItemRules(TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID,
                        "customerType-indirect", "category name", "orderId_id1_modelId",
                        null, CLICK_ID, null, false);

        doReturn("ID").when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest
                .createConversionDetails(CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID,
                        shopeeOrder, campaignConditionDetails);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        ConversionRegistrationDetails expected1 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType-indirect", "category name",
                "orderId_id1_modelId", null, CLICK_ID);
        ConversionRegistrationDetails expected2 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, RESULT_ID, "customerType-indirect", null,
                "orderId_id1_modelId-bonus", new BigDecimal("1"), CLICK_ID);
        ConversionRegistrationDetails expected3 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, RESULT_ID, "customerType-indirect", null,
                "orderId_id1_modelId-bonus", new BigDecimal("1"), CLICK_ID);
        assertEquals(expected1, actual.get(0));
        assertEquals(expected2, actual.get(1));
        assertEquals(expected3, actual.get(2));
        verify(underTest, times(1)).insertDataProceeded(any(
                MerchantIntegrationHistoryInsertRequest.class));
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenItemSellerCommissionNotNullAndGreaterThanZero() throws Exception {
        // given
        CampaignConditionDetails campaignConditionDetail = new CampaignConditionDetails(
                CAMPAIGN_ID_123, SHOP_ID, SHOP_ID_VALUES, GROUP_ID, OR);
        List<CampaignConditionDetails> conditionDetails = asList(campaignConditionDetail);
        ImmutableMap<String, List<CampaignConditionDetails>> campaignConditionDetails =
                of("123", conditionDetails);
        ShopeeItem shopeeItem1 = new ShopeeItem("id1", SHOP_ID_VALUES, null, 1,
                ITEM_PRICE, null, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, "globalCategoryLv3Name", ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                asList(shopeeItem1));
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandItem(SHOP_ID_VALUES);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);

        ClickConversionRegistrationDetails subConversion = new ClickConversionRegistrationDetails(
                TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType-indirect", "category name",
                "orderId_id1_modelId", null, CLICK_ID, 123L);
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>(
                Arrays.asList(subConversion));
        doReturn(brandItemConversions).when(underTest)
                .processConversionByBrandItemRules(TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID,
                        "customerType-indirect", "category name", "orderId_id1_modelId",
                        null, CLICK_ID, SHOP_ID_VALUES, false);

        doReturn("ID").when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest
                .createConversionDetails(CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID,
                        shopeeOrder, campaignConditionDetails);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        ConversionRegistrationDetails expected1 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType-indirect", "category name",
                "orderId_id1_modelId", null, CLICK_ID);
        ConversionRegistrationDetails expected2 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, RESULT_ID, "customerType-indirect", null, "orderId_id1_modelId-bonus",
                ITEM_SELLER_COMMISSION, CLICK_ID);
        ConversionRegistrationDetails expected3 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, RESULT_ID, "customerType-indirect", null, "orderId_id1_modelId-bonus",
                ITEM_SELLER_COMMISSION, CLICK_ID);
        assertEquals(expected1, actual.get(0));
        assertEquals(expected2, actual.get(1));
        assertEquals(expected3, actual.get(2));
        verify(underTest, times(1)).insertDataProceeded(any(
                MerchantIntegrationHistoryInsertRequest.class));
    }
}
