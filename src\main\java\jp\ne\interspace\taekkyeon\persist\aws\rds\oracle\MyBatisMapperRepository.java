/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle;

import javax.inject.Singleton;

import com.google.inject.Inject;
import com.google.inject.Injector;

import jp.ne.interspace.taekkyeon.module.Country;

import static com.google.inject.Key.get;

/**
 * Repository providing MyBatis mappers.
 *
 * <AUTHOR> OBS DEV Team
 */
@Singleton
public class MyBatisMapperRepository {

    @Inject
    private Injector injector;

    public <T> T getMapperOf(Class<T> mapperType, Country country) {
        return injector.getInstance(get(mapperType, country.getAnnotationClass()));
    }
}
