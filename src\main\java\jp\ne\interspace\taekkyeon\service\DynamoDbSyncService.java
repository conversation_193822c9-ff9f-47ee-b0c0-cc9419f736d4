/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.inject.Singleton;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.BatchWriteItemResult;
import com.amazonaws.services.dynamodbv2.model.PutRequest;
import com.amazonaws.services.dynamodbv2.model.WriteRequest;
import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.persist.aws.dynamodb.DynamoDbTable;

/**
 * Service layer for syncing to the DynamoDb.
 *
 * <AUTHOR>
 */
@Singleton @Slf4j
public class DynamoDbSyncService {

    private static final String UPDATE_MESSAGE = "Update DynamoDb table name : {}, contents : {}";
    private static final String ERROR_MESSAGE = "Failed to update DynamoDB data.";

    @Inject
    private DynamoDbTable dynamoDbTable;

    @Inject(optional = true)
    private SyncStatusUpdateService syncStatusUpdateService;

    /**
     * Waits until dynamoDb table available.
     */
    public void waitUntilAvailable() {
        dynamoDbTable.waitUntilAvailable();
    }

    /**
     * Syncs the {@code attributeValues} to DynamoDb.
     *
     * @param attributeValues
     *            the given update attribute values
     */
    public void syncStatus(Map<String, Map<String, AttributeValue>> attributeValues) {
        try {
            getLogger().info(UPDATE_MESSAGE, dynamoDbTable.getTableName(),
                    attributeValues.toString());
            Optional<BatchWriteItemResult> result = dynamoDbTable.batchUpdateBy(
                    attributeValues);
            if (syncStatusUpdateService != null) {
                result.ifPresent((res) -> res.getUnprocessedItems().values()
                        .stream()
                        .flatMap(List::stream)
                        .map(WriteRequest::getPutRequest)
                        .map(PutRequest::getItem)
                        .map((item) -> item.get(dynamoDbTable.getKeyName()))
                        .map(AttributeValue::getS)
                        .forEach((key) -> attributeValues.remove(key)));
                if (!attributeValues.isEmpty()) {
                    syncStatusUpdateService.updateSyncStatusOf(attributeValues);
                }
            }
        } catch (Exception e) {
            getLogger().error(ERROR_MESSAGE, e);
            throw e;
        }
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }
}
