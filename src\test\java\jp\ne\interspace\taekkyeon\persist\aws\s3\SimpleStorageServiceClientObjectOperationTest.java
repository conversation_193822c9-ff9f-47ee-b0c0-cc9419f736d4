/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.s3;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AccessControlList;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.CopyObjectResult;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.DeleteObjectsRequest;
import com.amazonaws.services.s3.model.GetObjectAclRequest;
import com.amazonaws.services.s3.model.GetObjectMetadataRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.RestoreObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.s3.model.SetObjectAclRequest;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Answers;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link SimpleStorageServiceClient}'s ojbect-related functionalities.
 *
 * <AUTHOR> OBS DEV Team
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SimpleStorageServiceClientObjectOperationTest {

    private static final String DEFAULT_TEST_BUCKET = "smile now";

    private static final String DEFAULT_TEST_KEY = "cry later";

    private static final String DEFAULT_TEST_STRING = "tres puntos formando un triangulo que significa 'Mi Vida Loca.'";

    private static final File DEFAULT_TEST_FILE = new File("/mierdita.bin");

    private static final InputStream DEFAULT_TEST_INPUT_STREAM = new ByteArrayInputStream(
            new byte[0]);

    private static final ObjectMetadata DEFAULT_TEST_OBJECT_METADATA = new ObjectMetadata();

    private static final AccessControlList DEFAULT_TEST_ACCESS_CONTROL_LIST = new AccessControlList();

    private static final CannedAccessControlList DEFAULT_TEST_CANNED_ACCESS_CONTROL_LIST = CannedAccessControlList.PublicRead;

    private static final int DEFAULT_TEST_EXPIRATION_IN_DAYS = 7;

    @InjectMocks @Spy
    private SimpleStorageServiceClient underTest;

    @Mock
    private ListObjectsV2Result result1 = new ListObjectsV2Result();

    @Mock
    private ListObjectsV2Result result2 = new ListObjectsV2Result();

    @Mock
    private ListObjectsV2Result result3 = new ListObjectsV2Result();

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private AmazonS3 s3;

    @Test(expected = IllegalArgumentException.class)
    public void testDoesObjectExistShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.doesObjectExist(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDoesObjectExistShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsEmpty() {
        // given
        String bucketName = "  ";

        // when
        underTest.doesObjectExist(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDoesObjectExistShouldThrowIllegalArgumentExceptionWhenGivenObjectNameIsNull() {
        // given
        String objectName = null;

        // when
        underTest.doesObjectExist(DEFAULT_TEST_BUCKET, objectName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDoesObjectExistShouldThrowIllegalArgumentExceptionWhenGivenObjectNameIsEmpty() {
        // given
        String objectName = " ";

        // when
        underTest.doesObjectExist(DEFAULT_TEST_BUCKET, objectName);
    }

    @Test
    public void testDoesObjectExistShouldReturnBooleanWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String objectName = DEFAULT_TEST_KEY;
        boolean expected = true;
        when(s3.doesObjectExist(bucketName, objectName))
                .thenReturn(expected);

        // when
        boolean actual = underTest.doesObjectExist(bucketName, objectName);

        // then
        assertEquals(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenRequestIsNull() {
        // given
        PutObjectRequest request = null;

        // when
        underTest.putObject(request);
    }

    @Test
    public void testPutObjectShouldReturnPutObjectResultWhenGivenPutObjectRequest() {
        // given
        PutObjectRequest request = new PutObjectRequest(DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY, "http://somewhere.com/shared/something");
        PutObjectResult expected = new PutObjectResult();
        when(s3.putObject(request)).thenReturn(expected);

        // when
        PutObjectResult actual = underTest.putObject(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.putObject(bucketName, DEFAULT_TEST_KEY, DEFAULT_TEST_STRING);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsEmpty() {
        // given
        String bucketName = " ";

        // when
        underTest.putObject(bucketName, DEFAULT_TEST_KEY, DEFAULT_TEST_STRING);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenKeyIsNull() {
        // given
        String key = null;

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, key, DEFAULT_TEST_STRING);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenKeyIsEmpty() {
        // given
        String key = " ";

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, key, DEFAULT_TEST_STRING);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenContentIsNull() {
        // given
        String content = null;

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY, content);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenContentIsEmpty() {
        // given
        String content = " ";

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY, content);
    }

    @Test
    public void testPutObjectShouldReturnPutObjectResultWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;
        String content = DEFAULT_TEST_STRING;
        PutObjectResult expected = new PutObjectResult();
        when(s3.putObject(bucketName, key, content)).thenReturn(expected);

        // when
        PutObjectResult actual = underTest.putObject(bucketName, key, content);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenNullBucketName() {
        // given
        String bucketName = null;

        // when
        underTest.putObject(bucketName, DEFAULT_TEST_KEY, DEFAULT_TEST_FILE);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenEmptyBucketName() {
        // given
        String bucketName = " ";

        // when
        underTest.putObject(bucketName, DEFAULT_TEST_KEY, DEFAULT_TEST_FILE);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenNullKey() {
        // given
        String key = null;

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, key, DEFAULT_TEST_FILE);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenEmptyKey() {
        // given
        String key = " ";

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, key, DEFAULT_TEST_FILE);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenNullFile() {
        // given
        File file = null;

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY, file);
    }

    @Test
    public void testPutObjectShouldReturnPutObjectResultWhenGivenParametersAreAllValid() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;
        File file = DEFAULT_TEST_FILE;
        PutObjectResult expected = new PutObjectResult();
        when(s3.putObject(bucketName, key, file)).thenReturn(expected);

        // when
        PutObjectResult actual = underTest.putObject(bucketName, key, file);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenBucketNameParameterIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.putObject(bucketName, DEFAULT_TEST_KEY,
                DEFAULT_TEST_INPUT_STREAM, DEFAULT_TEST_OBJECT_METADATA);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenBucketNameParameterIsEmpty() {
        // given
        String bucketName = " ";

        // when
        underTest.putObject(bucketName, DEFAULT_TEST_KEY, DEFAULT_TEST_INPUT_STREAM,
                DEFAULT_TEST_OBJECT_METADATA);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenKeyParameterIsNull() {
        // given
        String key = null;

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, key, DEFAULT_TEST_INPUT_STREAM,
                DEFAULT_TEST_OBJECT_METADATA);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenKeyParameterIsEmpty() {
        // given
        String key = " ";

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, key, DEFAULT_TEST_INPUT_STREAM,
                DEFAULT_TEST_OBJECT_METADATA);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenInputStreamIsNull() {
        // given
        InputStream inputStream = null;

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY, inputStream,
                DEFAULT_TEST_OBJECT_METADATA);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutObjectShouldThrowIllegalArgumentExceptionWhenGivenObjectMetadataIsNull() {
        // given
        ObjectMetadata objectMetadata = null;

        // when
        underTest.putObject(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY,
                DEFAULT_TEST_INPUT_STREAM, objectMetadata);
    }

    @Test
    public void testPutObjectShouldReturnPutObjectResultWhenValidParametersAreGiven() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;
        InputStream inputStream = DEFAULT_TEST_INPUT_STREAM;
        ObjectMetadata objectMetadata = DEFAULT_TEST_OBJECT_METADATA;
        PutObjectResult expected = new PutObjectResult();
        when(s3.putObject(bucketName, key, inputStream, objectMetadata))
                .thenReturn(expected);

        // when
        PutObjectResult actual = underTest.putObject(bucketName, key, inputStream,
                objectMetadata);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCopyObjectShouldThrowIllegalArgumentExceptionWhenGivenSourceBucketNameIsNull() {
        // given
        String sourceBucketName = null;

        // when
        underTest.copyObject(sourceBucketName, DEFAULT_TEST_KEY, DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCopyObjectShouldThrowIllegalArgumentExceptionWhenGivenSourceBucketNameIsEmpty() {
        // given
        String sourceBucketName = "  ";

        // when
        underTest.copyObject(sourceBucketName, DEFAULT_TEST_KEY, DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCopyObjectShouldThrowIllegalArgumentExceptionWhenGivenSourceKeyIsNull() {
        // given
        String sourceKey = null;

        // when
        underTest.copyObject(DEFAULT_TEST_BUCKET, sourceKey, DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCopyObjectShouldThrowIllegalArgumentExceptionWhenGivenSourceKeyIsEmpty() {
        // given
        String sourceKey = "  ";

        // when
        underTest.copyObject(DEFAULT_TEST_BUCKET, sourceKey, DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCopyObjectShouldThrowIllegalArgumentExceptionWhenGivenDestinationBucketNameIsNull() {
        // given
        String destinationBucketName = null;

        // when
        underTest.copyObject(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY, destinationBucketName,
                DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCopyObjectShouldThrowIllegalArgumentExceptionWhenGivenDestinationBucketNameIsEmpty() {
        // given
        String destinationBucketName = "  ";

        // when
        underTest.copyObject(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY, destinationBucketName,
                DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCopyObjectShouldThrowIllegalArgumentExceptionWhenGivenDestinationKeyIsNull() {
        // given
        String destinationKey = null;

        // when
        underTest.copyObject(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY, DEFAULT_TEST_BUCKET,
                destinationKey);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCopyObjectShouldThrowIllegalArgumentExceptionWhenGivenDestinationKeyIsEmpty() {
        // given
        String destinationKey = "  ";

        // when
        underTest.copyObject(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY, DEFAULT_TEST_BUCKET,
                destinationKey);
    }

    @Test
    public void testCopyObjectShouldReturnCopyObjectResultWhenGivenValidParameters() {
        // given
        String sourceBucketName = DEFAULT_TEST_BUCKET;
        String sourceKey = DEFAULT_TEST_KEY;
        String destinationBucketName = DEFAULT_TEST_BUCKET;
        String destinationKey = DEFAULT_TEST_KEY;
        CopyObjectResult expected = new CopyObjectResult();
        when(s3.copyObject(sourceBucketName, sourceKey, destinationBucketName,
                destinationKey)).thenReturn(expected);

        // when
        CopyObjectResult actual = underTest.copyObject(sourceBucketName, sourceKey,
                destinationBucketName, destinationKey);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCopyObjectShouldThrowIllegalArgumentExceptionWhenGivenCopyObjectRequestIsNull() {
        // given
        CopyObjectRequest request = null;

        // when
        underTest.copyObject(request);
    }

    @Test
    public void testCopyObjectShouldReturnCopyObjectResultWhenGivenValidRequest() {
        // given
        CopyObjectRequest request = new CopyObjectRequest(DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY, DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY);
        CopyObjectResult expected = new CopyObjectResult();
        when(s3.copyObject(request)).thenReturn(expected);

        // when
        CopyObjectResult actual = underTest.copyObject(request);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testFetchAllShouldReturnCorrectDataWhenCalled() {
        // given
        String bucketName = "bucket name";
        String prefix = "prefix";
        S3ObjectSummary expectedObject1 = mock(S3ObjectSummary.class);
        List<S3ObjectSummary> expectedList1 = Arrays.asList(expectedObject1);
        S3ObjectSummary expectedObject2 = mock(S3ObjectSummary.class);
        List<S3ObjectSummary> expectedList2 = Arrays.asList(expectedObject2);
        S3ObjectSummary expectedObject3 = mock(S3ObjectSummary.class);
        List<S3ObjectSummary> expectedList3 = Arrays.asList(expectedObject3);

        List<S3ObjectSummary> expected = new LinkedList<>();
        expected.addAll(expectedList1);
        expected.addAll(expectedList2);
        expected.addAll(expectedList3);

        String continuationToken1 = null;
        String continuationToken2 = "continuationToken2";
        String continuationToken3 = "continuationToken3";

        ListObjectsV2Request request1 = new ListObjectsV2Request();
        doReturn(request1).when(underTest)
                .createV2Request(bucketName, prefix, continuationToken1);
        ListObjectsV2Request request2 = new ListObjectsV2Request();
        doReturn(request2).when(underTest)
                .createV2Request(bucketName, prefix, continuationToken2);
        ListObjectsV2Request request3 = new ListObjectsV2Request();
        doReturn(request3).when(underTest)
                .createV2Request(bucketName, prefix, continuationToken3);

        when(s3.listObjectsV2(request1)).thenReturn(result1);
        when(result1.getObjectSummaries()).thenReturn(expectedList1);
        when(result1.getNextContinuationToken()).thenReturn(continuationToken2);

        when(s3.listObjectsV2(request2)).thenReturn(result2);
        when(result2.getObjectSummaries()).thenReturn(expectedList2);
        when(result2.getNextContinuationToken()).thenReturn(continuationToken3);

        when(s3.listObjectsV2(request3)).thenReturn(result3);
        when(result3.getObjectSummaries()).thenReturn(expectedList3);
        when(result3.getNextContinuationToken()).thenReturn(continuationToken1);

        // when
        List<S3ObjectSummary> actual = underTest.fetchAll(bucketName, prefix);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testFetchAllKeysShouldReturnCorrectDataWhenCalled() {
        // given
        String bucketName = "bucket name";
        String prefix = "prefix";
        String key1 = "key1";
        String key2 = "key3";
        String key3 = "key3";
        S3ObjectSummary expectedObject1 = new S3ObjectSummary();
        expectedObject1.setKey(key1);
        List<S3ObjectSummary> expectedList1 = Arrays.asList(expectedObject1);
        S3ObjectSummary expectedObject2 = new S3ObjectSummary();
        expectedObject2.setKey(key2);
        List<S3ObjectSummary> expectedList2 = Arrays.asList(expectedObject2);
        S3ObjectSummary expectedObject3 = new S3ObjectSummary();
        expectedObject3.setKey(key3);
        List<S3ObjectSummary> expectedList3 = Arrays.asList(expectedObject3);
        List<S3ObjectSummary> result = new LinkedList<>();
        result.addAll(expectedList1);
        result.addAll(expectedList2);
        result.addAll(expectedList3);
        doReturn(result).when(underTest).fetchAll(bucketName, prefix);

        List<String> expected = new LinkedList<>();
        expected.add(key1);
        expected.add(key2);
        expected.add(key3);

        // when
        List<String> actual = underTest.fetchAllKeys(bucketName, prefix);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateV2RequestShouldReturnCorrectDataWhenCalled() {
        // given
        String bucketName = "bucket name";
        String prefix = "prefix";
        String continuationToken = "continuationToken";

        // when
        ListObjectsV2Request actual = underTest
                .createV2Request(bucketName, prefix, continuationToken);

        // then
        assertNotNull(actual);
        assertEquals(bucketName, actual.getBucketName());
        assertEquals(prefix, actual.getPrefix());
        assertEquals(continuationToken, actual.getContinuationToken());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testListObjectsShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.listObjects(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testListObjectsShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsEmpty() {
        // given
        String bucketName = "  ";

        // when
        underTest.listObjects(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testListObjectsShouldThrowIllegalArgumentExceptionWhenGivenPrefixIsNull() {
        // given
        String prefix = null;

        // when
        underTest.listObjects(DEFAULT_TEST_BUCKET, prefix);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testListObjectsShouldThrowIllegalArgumentExceptionWhenGivenPrefixIsEmpty() {
        // given
        String prefix = " ";

        // when
        underTest.listObjects(DEFAULT_TEST_BUCKET, prefix);
    }

    @Test
    public void testListObjectsShouldReturnS3ObjectSummaryListWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String prefix = DEFAULT_TEST_KEY;
        List<S3ObjectSummary> expected = new ArrayList<>();
        when(s3.listObjects(bucketName, prefix).getObjectSummaries())
                .thenReturn(expected); // 2-level deep stubbing needed

        // when
        List<S3ObjectSummary> actual = underTest.listObjects(bucketName, prefix);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testListObjectsShouldThrowIllegalArgumentExceptionWhenGivenBucketIsEmpty() {
        // given
        String bucketName = " ";

        // when
        underTest.listObjects(bucketName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testListObjectsShouldThrowIllegalArgumentExceptionWhenGivenBucketIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.listObjects(bucketName);
    }

    @Test
    public void testListObjectsShouldReturnS3ObjectSummaryListWhenGivenValidParameter() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        List<S3ObjectSummary> expected = new ArrayList<>();
        when(s3.listObjects(bucketName).getObjectSummaries()).thenReturn(expected);

        // when
        List<S3ObjectSummary> actual = underTest.listObjects(bucketName);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectListingShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.getObjectListing(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectListingShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsEmpty() {
        // given
        String bucketName = "  ";

        // when
        underTest.getObjectListing(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectListingShouldThrowIllegalArgumentExceptionWhenGivenPrefixIsNull() {
        // given
        String prefix = null;

        // when
        underTest.getObjectListing(DEFAULT_TEST_BUCKET, prefix);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectListingShouldThrowIllegalArgumentExceptionWhenGivenPrefixIsEmpty() {
        // given
        String prefix = "   ";

        // when
        underTest.getObjectListing(DEFAULT_TEST_BUCKET, prefix);
    }

    @Test
    public void testGetObjectListingShouldReturnObjectListingWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String prefix = DEFAULT_TEST_KEY;
        ObjectListing expected = new ObjectListing();
        when(s3.listObjects(bucketName, prefix)).thenReturn(expected);

        // when
        ObjectListing actual = underTest.getObjectListing(bucketName, prefix);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.getObject(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsEmpty() {
        // given
        String bucketName = "  ";

        // when
        underTest.getObject(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectShouldThrowIllegalArgumentExceptionWhenGivenKeyIsNull() {
        // given
        String key = null;

        // when
        underTest.getObject(DEFAULT_TEST_BUCKET, key);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectShouldThrowIllegalArgumentExceptionWhenGivenKeyIsEmpty() {
        // given
        String key = "   ";

        // when
        underTest.getObject(DEFAULT_TEST_BUCKET, key);
    }

    @Test
    public void testGetObjectShouldReturnS3ObjectWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;
        S3Object expected = new S3Object();
        when(s3.getObject(bucketName, key)).thenReturn(expected);

        // when
        S3Object actual = underTest.getObject(bucketName, key);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectShouldThrowIllegalArgumentExceptionWhenGivenRequestIsNull() {
        // given
        GetObjectRequest request = null;

        // when
        underTest.getObject(request, DEFAULT_TEST_FILE);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectShouldThrowIllegalArgumentExceptionWhenGivenFileIsNull() {
        // given
        File destinationFile = null;
        GetObjectRequest request = new GetObjectRequest(DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY);

        // when
        underTest.getObject(request, destinationFile);
    }

    @Test
    public void testGetObjectShouldReturnObjectMetadataWhenGivenValidParameters() {
        // given
        GetObjectRequest request = new GetObjectRequest(DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY);
        File destinationFile = DEFAULT_TEST_FILE;
        ObjectMetadata expected = DEFAULT_TEST_OBJECT_METADATA;
        when(s3.getObject(request, destinationFile)).thenReturn(expected);

        // when
        ObjectMetadata actual = underTest.getObject(request, destinationFile);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        GetObjectRequest request = null;

        // when
        underTest.getObject(request);
    }

    @Test
    public void testGetObjectShouldReturnS3ObjectWhenGivenValidRequest() {
        // given
        GetObjectRequest request = new GetObjectRequest(DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY);
        S3Object expected = new S3Object();
        when(s3.getObject(request)).thenReturn(expected);

        // when
        S3Object actual = underTest.getObject(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectAsStringShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.getObjectAsString(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectAsStringShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsEmpty() {
        // given
        String bucketName = "  ";

        // when
        underTest.getObjectAsString(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectAsStringShouldThrowIllegalArgumentExceptionWhenGivenKeyIsNull() {
        // given
        String key = null;

        // when
        underTest.getObjectAsString(DEFAULT_TEST_BUCKET, key);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectAsStringShouldThrowIllegalArgumentExceptionWhenGivenKeyIsEmpty() {
        // given
        String key = "   ";

        // when
        underTest.getObjectAsString(DEFAULT_TEST_BUCKET, key);
    }

    @Test
    public void testGetObjectAsStringShouldReturnStringWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;
        String expected = DEFAULT_TEST_STRING;
        when(s3.getObjectAsString(bucketName, key)).thenReturn(expected);

        // when
        String actual = underTest.getObjectAsString(bucketName, key);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectMetadataShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.getObjectMetadata(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectMetadataShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsEmpty() {
        // given
        String bucketName = "  ";

        // when
        underTest.getObjectMetadata(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectMetadataShouldThrowIllegalArgumentExceptionWhenGivenKeyIsNull() {
        // given
        String key = null;

        // when
        underTest.getObjectMetadata(DEFAULT_TEST_BUCKET, key);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectMetadataShouldThrowIllegalArgumentExceptionWhenGivenKeyIsEmpty() {
        // given
        String key = "   ";

        // when
        underTest.getObjectMetadata(DEFAULT_TEST_BUCKET, key);
    }

    @Test
    public void testGetObjectMetaDataShouldReturnObjectMetadataWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;
        ObjectMetadata expected = DEFAULT_TEST_OBJECT_METADATA;
        when(s3.getObjectMetadata(bucketName, key)).thenReturn(expected);

        // when
        ObjectMetadata actual = underTest.getObjectMetadata(bucketName, key);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectMetadataShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        GetObjectMetadataRequest request = null;

        // when
        underTest.getObjectMetadata(request);
    }

    @Test
    public void testGetObjectMetaDataShouldReturnObjectMetadataWhenGivenValidRequest() {
        // given
        GetObjectMetadataRequest request = new GetObjectMetadataRequest(
                DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY);
        ObjectMetadata expected = DEFAULT_TEST_OBJECT_METADATA;
        when(s3.getObjectMetadata(request)).thenReturn(expected);

        // when
        ObjectMetadata actual = underTest.getObjectMetadata(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.getObjectAccessControlList(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsEmpty() {
        // given
        String bucketName = "  ";

        // when
        underTest.getObjectAccessControlList(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenKeyIsNull() {
        // given
        String key = null;

        // when
        underTest.getObjectAccessControlList(DEFAULT_TEST_BUCKET, key);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenKeyIsEmpty() {
        // given
        String key = "   ";

        // when
        underTest.getObjectAccessControlList(DEFAULT_TEST_BUCKET, key);
    }

    @Test
    public void testGetObjectAccessControlListShouldReturnAccessControlListWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;
        AccessControlList expected = DEFAULT_TEST_ACCESS_CONTROL_LIST;
        when(s3.getObjectAcl(bucketName, key)).thenReturn(expected);

        // when
        AccessControlList actual = underTest.getObjectAccessControlList(bucketName, key);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenGetObjectAclRequestIsNull() {
        // given
        GetObjectAclRequest request = null;

        // when
        underTest.getObjectAccessControlList(request);
    }

    @Test
    public void testGetObjectAccessControlListShouldReturnAccessControlListWhenGivenValidRequest() {
        // given
        GetObjectAclRequest request = new GetObjectAclRequest(DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY);
        AccessControlList expected = DEFAULT_TEST_ACCESS_CONTROL_LIST;
        when(s3.getObjectAcl(request)).thenReturn(expected);

        // when
        AccessControlList actual = underTest.getObjectAccessControlList(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsNull() {
        // given
        String bucketName = null;

        // when
        underTest.setObjectAccessControlList(bucketName, DEFAULT_TEST_KEY,
                DEFAULT_TEST_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenBucketNameIsEmpty() {
        // given
        String bucketName = "  ";

        // when
        underTest.setObjectAccessControlList(bucketName, DEFAULT_TEST_KEY,
                DEFAULT_TEST_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenKeyIsNull() {
        // given
        String key = null;

        // when
        underTest.setObjectAccessControlList(DEFAULT_TEST_BUCKET, key,
                DEFAULT_TEST_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenKeyIsEmpty() {
        // given
        String key = "   ";

        // when
        underTest.setObjectAccessControlList(DEFAULT_TEST_BUCKET, key,
                DEFAULT_TEST_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenAccessControlListIsNull() {
        // given
        AccessControlList accessControlList = null;

        // when
        underTest.setObjectAccessControlList(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY,
                accessControlList);
    }

    @Test
    public void testSetObjectAccessControlListShouldCallSetObjectAclOnceWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;
        AccessControlList accessControlList = DEFAULT_TEST_ACCESS_CONTROL_LIST;

        // when
        underTest.setObjectAccessControlList(bucketName, key, accessControlList);

        // then
        verify(s3).setObjectAcl(bucketName, key, accessControlList);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenNullBucketName() {
        // given
        String bucketName = null;

        // when
        underTest.setObjectAccessControlList(bucketName, DEFAULT_TEST_KEY,
                DEFAULT_TEST_CANNED_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenEmptyBucketName() {
        // given
        String bucketName = "  ";

        // when
        underTest.setObjectAccessControlList(bucketName, DEFAULT_TEST_KEY,
                DEFAULT_TEST_CANNED_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenNullKey() {
        // given
        String key = null;

        // when
        underTest.setObjectAccessControlList(DEFAULT_TEST_BUCKET, key,
                DEFAULT_TEST_CANNED_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenEmptyKey() {
        // given
        String key = "   ";

        // when
        underTest.setObjectAccessControlList(DEFAULT_TEST_BUCKET, key,
                DEFAULT_TEST_CANNED_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenNullGivenAccessControlList() {
        // given
        CannedAccessControlList cannedAccessControlList = null;

        // when
        underTest.setObjectAccessControlList(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY,
                cannedAccessControlList);
    }

    @Test
    public void testSetObjectAccessControlListShouldCallSetObjectAclOnceWhenGivenParametersAreValid() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;

        // when
        underTest.setObjectAccessControlList(bucketName, key,
                DEFAULT_TEST_CANNED_ACCESS_CONTROL_LIST);

        // then
        verify(s3).setObjectAcl(bucketName, key,
                DEFAULT_TEST_CANNED_ACCESS_CONTROL_LIST);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetObjectAccessControlListShouldThrowIllegalArgumentExceptionWhenGivenNullRequest() {
        // given
        SetObjectAclRequest request = null;

        // when
        underTest.setObjectAccessControlList(request);
    }

    @Test
    public void testSetObjectAccessControlListShouldCallSetObjectAclOnceWhenGivenValidRequest() {
        // given
        SetObjectAclRequest request = new SetObjectAclRequest(DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY, DEFAULT_TEST_ACCESS_CONTROL_LIST);

        // when
        underTest.setObjectAccessControlList(request);

        // then
        verify(s3).setObjectAcl(request);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteObjectShouldThrowIllegalArgumentExceptionWhenGivenNullBucketName() {
        // given
        String bucketName = null;

        // when
        underTest.deleteObject(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteObjectShouldThrowIllegalArgumentExceptionWhenGivenEmptyBucketName() {
        // given
        String bucketName = "    ";

        // when
        underTest.deleteObject(bucketName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteObjectShouldThrowIllegalArgumentExceptionWhenGivenNullKey() {
        // given
        String key = null;

        // when
        underTest.deleteObject(DEFAULT_TEST_BUCKET, key);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteObjectShouldThrowIllegalArgumentExceptionWhenGivenEmptyKey() {
        // given
        String key = "   ";

        // when
        underTest.deleteObject(DEFAULT_TEST_BUCKET, key);
    }

    @Test
    public void testDeleteObjectShouldCallDeleteObjectOnceWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;

        // when
        underTest.deleteObject(bucketName, key);

        // then
        verify(s3).deleteObject(bucketName, key);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteObjectShouldThrowIllegalArgumentExceptionWhenGivenNullRequest() {
        // given
        DeleteObjectRequest request = null;

        // when
        underTest.deleteObject(request);
    }

    @Test
    public void testDeleteObjectShouldCallDeleteObjectOnceWhenGivenValidRequest() {
        // given
        DeleteObjectRequest request = new DeleteObjectRequest(DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY);

        // when
        underTest.deleteObject(request);

        // then
        verify(s3).deleteObject(request);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteObjectsShouldThrowIllegalArgumentExceptionWhenGivenNullRequest() {
        // given
        DeleteObjectsRequest request = null;

        // when
        underTest.deleteObjects(request);
    }

    @Test
    public void testDeleteObjectsShouldCallDeleteObjectsOnceWhenGivenValidRequest() {
        // given
        DeleteObjectsRequest request = new DeleteObjectsRequest(DEFAULT_TEST_BUCKET);

        // when
        underTest.deleteObjects(request);

        // then
        verify(s3).deleteObjects(request);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRestoreObjectShouldThrowIllegalArgumentExceptionWhenGivenNullBucketName() {
        // given
        String bucketName = null;

        // when
        underTest.restoreObject(bucketName, DEFAULT_TEST_KEY,
                DEFAULT_TEST_EXPIRATION_IN_DAYS);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRestoreObjectShouldThrowIllegalArgumentExceptionWhenGivenEmptyBucketName() {
        // given
        String bucketName = "    ";

        // when
        underTest.restoreObject(bucketName, DEFAULT_TEST_KEY,
                DEFAULT_TEST_EXPIRATION_IN_DAYS);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRestoreObjectShouldThrowIllegalArgumentExceptionWhenGivenNullKey() {
        // given
        String key = null;

        // when
        underTest.restoreObject(DEFAULT_TEST_BUCKET, key,
                DEFAULT_TEST_EXPIRATION_IN_DAYS);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRestoreObjectShouldThrowIllegalArgumentExceptionWhenGivenEmptyKey() {
        // given
        String key = "   ";

        // when
        underTest.restoreObject(DEFAULT_TEST_BUCKET, key,
                DEFAULT_TEST_EXPIRATION_IN_DAYS);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRestoreObjectShouldThrowIllegalArgumentExceptionWhenGivenExpirationInDaysIsLessThanZero() {
        // given
        int expirationInDays = -1;

        // when
        underTest.restoreObject(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY, expirationInDays);
    }

    @Test
    public void testRestoreObjectShouldCallRestoreObjectOnceWhenGivenValidParameters() {
        // given
        String bucketName = DEFAULT_TEST_BUCKET;
        String key = DEFAULT_TEST_KEY;
        int expirationInDays = DEFAULT_TEST_EXPIRATION_IN_DAYS;
        ArgumentCaptor<RestoreObjectRequest> captor = ArgumentCaptor
                .forClass(RestoreObjectRequest.class);

        // when
        underTest.restoreObject(bucketName, key, expirationInDays);

        // then
        verify(s3).restoreObjectV2(captor.capture());

        RestoreObjectRequest actual = captor.getValue();
        assertEquals(bucketName, actual.getBucketName());
        assertEquals(key, actual.getKey());
        assertEquals(expirationInDays, actual.getExpirationInDays());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRestoreObjectShouldThrowIllegalArgumentExceptionWhenGivenNullRequest() {
        // given
        RestoreObjectRequest request = null;

        // when
        underTest.restoreObject(request);
    }

    @Test
    public void testRestoreObjectShouldCallRestoreObjectOnceWhenGivenValidRequest() {
        // given
        RestoreObjectRequest request = new RestoreObjectRequest(DEFAULT_TEST_BUCKET,
                DEFAULT_TEST_KEY);

        // when
        underTest.restoreObject(request);

        // then
        verify(s3).restoreObjectV2(request);
    }

}
