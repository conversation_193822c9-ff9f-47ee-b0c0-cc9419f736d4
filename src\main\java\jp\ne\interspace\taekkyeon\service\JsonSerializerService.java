/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.lang.reflect.Type;
import java.sql.Time;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.Locale;

import javax.inject.Singleton;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.JsonSyntaxException;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.JSON_DATE_TIME_FORMATTER;

/**
 * Service class for (de)serializing Java objects to / from json {@link String}s.
 *
 * <AUTHOR> Varga
 */
@Singleton
public class JsonSerializerService {

    private final Gson gson = buildGson();

    /**
     * Transforms the given {@code source} to a json {@link String}.
     *
     * @param source
     *            the object to transform
     * @return the resulting json {@link String}
     */
    public String toJson(Object source) {
        return gson.toJson(source);
    }

    /**
     * Transforms the given {@code json} {@link String} to the Java type specified by
     * {@code resultClass}.
     *
     * @param json
     *            the json {@link String} to transform
     * @param resultClass
     *            Java class of the resulting object
     * @param <T>
     *            generic variable for the type of the result
     * @return the transformed object
     */
    public <T> T fromJson(String json, Class<T> resultClass) {
        return gson.fromJson(json, resultClass);
    }

    private Gson buildGson() {
        GsonBuilder builder = new GsonBuilder();
        builder.disableHtmlEscaping();
        return builder
                .registerTypeAdapter(ZonedDateTime.class, new ZonedDateTimeAdapter())
                .registerTypeAdapter(Date.class, new ISO8601DateTimeTypeAdapter())
                .registerTypeAdapter(Time.class, new ISO8601TimeTypeAdapter())
                .registerTypeAdapter(LocalDate.class, new LocalDateAdapter())
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
                .registerTypeAdapter(YearMonth.class, new YearMonthAdapter())
                .create();
    }

    /**
     * Json serializer for {@link LocalDateTime}.
     */
    private static class ZonedDateTimeAdapter
            implements JsonSerializer<ZonedDateTime>, JsonDeserializer<ZonedDateTime> {

        @Override
        public synchronized JsonElement serialize(ZonedDateTime zonedDateTime, Type type,
                JsonSerializationContext jsonSerializationContext) {
            return new JsonPrimitive(zonedDateTime.format(JSON_DATE_TIME_FORMATTER));
        }

        @Override
        public synchronized ZonedDateTime deserialize(JsonElement jsonElement, Type type,
                JsonDeserializationContext jsonDeserializationContext) {
            return ZonedDateTime.parse(jsonElement.getAsString(),
                    JSON_DATE_TIME_FORMATTER);
        }
    }

    /**
     * Json serializer for legacy {@link Date}.
     */
    private static class ISO8601DateTimeTypeAdapter
            implements JsonSerializer<Date>, JsonDeserializer<Date> {

        private final DateFormat dateTimeFormat;

        private ISO8601DateTimeTypeAdapter() {
            dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ", Locale.US);
        }

        @Override
        public synchronized JsonElement serialize(Date date, Type type,
                JsonSerializationContext jsonSerializationContext) {
            synchronized (dateTimeFormat) {
                String dateFormatAsString = dateTimeFormat.format(date);
                return new JsonPrimitive(dateFormatAsString);
            }
        }

        @Override
        public synchronized Date deserialize(JsonElement jsonElement, Type type,
                JsonDeserializationContext jsonDeserializationContext) {
            try {
                synchronized (dateTimeFormat) {
                    Date date = dateTimeFormat.parse(jsonElement.getAsString());
                    return new Date((date.getTime() / 1000) * 1000);
                }
            } catch (ParseException e) {
                throw new JsonSyntaxException(jsonElement.getAsString(), e);
            }
        }
    }

    /**
     * Json serializer for legacy {@link Time}.
     */
    private static class ISO8601TimeTypeAdapter
            implements JsonSerializer<Time>, JsonDeserializer<Time> {

        private final DateFormat timeFormat;

        private ISO8601TimeTypeAdapter() {
            timeFormat = new SimpleDateFormat("HH:mm:ssZ", Locale.US);
        }

        @Override
        public synchronized JsonElement serialize(Time time, Type type,
                JsonSerializationContext jsonSerializationContext) {
            synchronized (timeFormat) {
                String timeFormatAsString = timeFormat.format(time);
                return new JsonPrimitive(timeFormatAsString);
            }
        }

        @Override
        public synchronized Time deserialize(JsonElement jsonElement, Type type,
                JsonDeserializationContext jsonDeserializationContext) {
            try {
                synchronized (timeFormat) {
                    Date date = timeFormat.parse(jsonElement.getAsString());
                    return new Time((date.getTime() / 1000) * 1000);
                }
            } catch (ParseException e) {
                throw new JsonSyntaxException(jsonElement.getAsString(), e);
            }
        }
    }

    /**
     * Json serializer for {@link LocalDate}.
     */
    public static class LocalDateAdapter
            implements JsonSerializer<LocalDate>, JsonDeserializer<LocalDate> {

        @Override
        public synchronized JsonElement serialize(LocalDate localDate, Type type,
                JsonSerializationContext jsonSerializationContext) {
            return new JsonPrimitive(localDate.toString());
        }

        @Override
        public synchronized LocalDate deserialize(JsonElement jsonElement, Type type,
                JsonDeserializationContext jsonDeserializationContext) {
            return LocalDate.parse(jsonElement.getAsString());
        }
    }

    /**
     * Json serializer for {@link LocalDateTime}.
     */
    public static class LocalDateTimeAdapter
            implements JsonSerializer<LocalDateTime>, JsonDeserializer<LocalDateTime> {

        @Override
        public synchronized JsonElement serialize(LocalDateTime localDateTime, Type type,
                JsonSerializationContext jsonSerializationContext) {
            return new JsonPrimitive(localDateTime.toString());
        }

        @Override
        public synchronized LocalDateTime deserialize(JsonElement jsonElement, Type type,
                JsonDeserializationContext jsonDeserializationContext) {
            return LocalDateTime.parse(jsonElement.getAsString());
        }
    }

    /**
     * Json serializer for {@link YearMonth}.
     */
    public static class YearMonthAdapter
            implements JsonSerializer<YearMonth>, JsonDeserializer<YearMonth> {

        @Override
        public synchronized JsonElement serialize(YearMonth yearMonth, Type type,
                JsonSerializationContext jsonSerializationContext) {
            return new JsonPrimitive(yearMonth.toString());
        }

        @Override
        public synchronized YearMonth deserialize(JsonElement jsonElement, Type type,
                JsonDeserializationContext jsonDeserializationContext) {
            return YearMonth.parse(jsonElement.getAsString());
        }
    }
}
