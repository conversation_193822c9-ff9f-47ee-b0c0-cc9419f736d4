/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.s3;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Date;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AbortMultipartUploadRequest;
import com.amazonaws.services.s3.model.CompleteMultipartUploadRequest;
import com.amazonaws.services.s3.model.CopyPartRequest;
import com.amazonaws.services.s3.model.CopyPartResult;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.InitiateMultipartUploadRequest;
import com.amazonaws.services.s3.model.ListMultipartUploadsRequest;
import com.amazonaws.services.s3.model.MultipartUploadListing;
import com.amazonaws.services.s3.model.SetBucketOwnershipControlsRequest;
import com.amazonaws.services.s3.model.SetPublicAccessBlockRequest;
import com.amazonaws.services.s3.model.UploadPartRequest;
import com.amazonaws.services.s3.model.UploadPartResult;
import com.amazonaws.services.s3.model.ownership.ObjectOwnership;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link SimpleStorageServiceClient}'s general functionalities.
 *
 * <AUTHOR> OBS DEV Team
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SimpleStorageServiceClientGeneralTest {

    private static final String DEFAULT_TEST_BUCKET = "smile.now";

    private static final String DEFAULT_TEST_KEY = "cry.later";

    private static final Date DEFAULT_TEST_EXIPARATION = new Date();

    private static final URL DEFAULT_TEST_URL = generateDefaultTestUrl();

    private static final HttpMethod DEFAULT_TEST_HTTP_METHOD = HttpMethod.GET;

    @InjectMocks
    private SimpleStorageServiceClient underTest;

    @Mock
    private AmazonS3 s3;

    @Test(expected = IllegalArgumentException.class)
    public void testGeneratePresignedUrlShouldThrowIllegalArgumentExceptionWhenGivenNullBucket() {
        // given
        String bucketName = null;

        // when
        underTest.generatePresignedUrl(bucketName, DEFAULT_TEST_KEY,
                DEFAULT_TEST_EXIPARATION, DEFAULT_TEST_HTTP_METHOD);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGeneratePresignedUrlShouldThrowIllegalArgumentExceptionWhenGivenEmptyBucket() {
        // given
        String bucketName = " ";

        // when
        underTest.generatePresignedUrl(bucketName, DEFAULT_TEST_KEY,
                DEFAULT_TEST_EXIPARATION, DEFAULT_TEST_HTTP_METHOD);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGeneratePresignedUrlShouldThrowIllegalArgumentExceptionWhenGivenNullKey() {
        // given
        String key = null;

        // when
        underTest.generatePresignedUrl(DEFAULT_TEST_BUCKET, key,
                DEFAULT_TEST_EXIPARATION, DEFAULT_TEST_HTTP_METHOD);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGeneratePresignedUrlShouldThrowIllegalArgumentExceptionWhenGivenEmptyKey() {
        // given
        String key = " ";

        // when
        underTest.generatePresignedUrl(DEFAULT_TEST_BUCKET, key,
                DEFAULT_TEST_EXIPARATION, DEFAULT_TEST_HTTP_METHOD);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGeneratePresignedUrlShouldThrowIllegalArgumentExceptionWhenGivenExpirationIsNull() {
        // given
        Date expiration = null;

        // when
        underTest.generatePresignedUrl(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY,
                expiration, DEFAULT_TEST_HTTP_METHOD);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGeneratePresignedUrlShouldThrowIllegalArgumentExceptionWhenGivenHttpMethodIsNull() {
        // given
        HttpMethod httpMethod = null;

        // when
        underTest.generatePresignedUrl(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY,
                DEFAULT_TEST_EXIPARATION, httpMethod);
    }

    @Test
    public void testGeneratePresignedUrlShouldReturnUrlWhenGivenValidParameters() {
        // given
        URL expected = DEFAULT_TEST_URL;
        when(s3.generatePresignedUrl(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY,
                DEFAULT_TEST_EXIPARATION, DEFAULT_TEST_HTTP_METHOD)).thenReturn(expected);

        // when
        URL actual = underTest.generatePresignedUrl(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY,
                DEFAULT_TEST_EXIPARATION, DEFAULT_TEST_HTTP_METHOD);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGeneratePresignedUrlShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        GeneratePresignedUrlRequest request = null;

        // when
        underTest.generatePresignedUrl(request);
    }

    @Test
    public void testGeneratePresignedUrlShouldReturnUrlWhenGivenValidRequest() {
        // given
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY);
        URL expected = DEFAULT_TEST_URL;
        when(s3.generatePresignedUrl(request)).thenReturn(expected);

        // when
        URL actual = underTest.generatePresignedUrl(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testListMultipartUploadsShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        ListMultipartUploadsRequest request = null;

        // when
        underTest.listMultipartUploads(request);
    }

    @Test
    public void testListMultipartUploadsShouldReturnMultipartUploadListingWhenListMultipartUploadsRequestIsGiven() {
        // given
        ListMultipartUploadsRequest request = new ListMultipartUploadsRequest(
                DEFAULT_TEST_BUCKET);
        MultipartUploadListing expected = new MultipartUploadListing();
        when(s3.listMultipartUploads(request)).thenReturn(expected);

        // when
        MultipartUploadListing actual = underTest.listMultipartUploads(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testInitiateMultipartUploadShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        InitiateMultipartUploadRequest request = null;

        // when
        underTest.initiateMultipartUpload(request);
    }

    @Test
    public void testInitiateMultipartUploadShouldCallInitiateMultipartUploadOnceWhenInitiateMultipartUploadRequestIsGiven() {
        // given
        InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(
                DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY);

        // when
        underTest.initiateMultipartUpload(request);

        // then
        verify(s3).initiateMultipartUpload(request);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUploadPartShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        UploadPartRequest request = null;

        // when
        underTest.uploadPart(request);
    }

    @Test
    public void testUploadPartShouldReturnUploadPartResultWhenUploadPartRequestIsGiven() {
        // given
        UploadPartRequest request = new UploadPartRequest();
        UploadPartResult expected = new UploadPartResult();
        when(s3.uploadPart(request)).thenReturn(expected);

        // when
        UploadPartResult actual = underTest.uploadPart(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCopyPartShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        CopyPartRequest request = null;

        // when
        underTest.copyPart(request);
    }

    @Test
    public void testCopyPartShouldReturnCopyPartResultWhenCopyPartRequestIsGiven() {
        // given
        CopyPartRequest request = new CopyPartRequest();
        CopyPartResult expected = new CopyPartResult();
        when(s3.copyPart(request)).thenReturn(expected);

        // when
        CopyPartResult actual = underTest.copyPart(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testAbortMultipartUploadShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        AbortMultipartUploadRequest request = null;

        // when
        underTest.abortMultipartUpload(request);
    }

    @Test
    public void testAbortMultipartUploadShouldCallAbortMultipartUploadOnceWhenAbortMultipartUploadRequestIsGiven() {
        // given
        String uploadId = "lil' some'n some'n";
        AbortMultipartUploadRequest request = new AbortMultipartUploadRequest(
                DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY, uploadId);

        // when
        underTest.abortMultipartUpload(request);

        // then
        verify(s3).abortMultipartUpload(request);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCompleteMultipartUploadShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        CompleteMultipartUploadRequest request = null;

        // when
        underTest.completeMultipartUpload(request);
    }

    @Test
    public void testCompleteMultipartUploadShouldCallCompleteMultipartUploadOnceWhenCompleteMultipartUploadRequestIsGiven() {
        // given
        CompleteMultipartUploadRequest request = new CompleteMultipartUploadRequest();

        // when
        underTest.completeMultipartUpload(request);

        // then
        verify(s3).completeMultipartUpload(request);
    }

    @Test
    public void testGetUrlShouldReturnCorrectDataWhenCalled()
            throws MalformedURLException {
        // given
        String expected = "https://s3.region.amazonaws.com/smile.now/cry.later";
        when(s3.getRegionName()).thenReturn("region");

        // when
        String actual = underTest.getUrl(DEFAULT_TEST_BUCKET, DEFAULT_TEST_KEY);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testUpdateBucketAsPublicShouldWorkCorrectlyWhenGivenCorrectParameter() {
        // given
        ArgumentCaptor<SetBucketOwnershipControlsRequest> ownershipRequestCaptor =
                ArgumentCaptor.forClass(SetBucketOwnershipControlsRequest.class);
        when(s3.setBucketOwnershipControls(ownershipRequestCaptor.capture()))
                .thenReturn(null);
        ArgumentCaptor<SetPublicAccessBlockRequest> accessRequestCaptor =
                ArgumentCaptor.forClass(SetPublicAccessBlockRequest.class);
        when(s3.setPublicAccessBlock(accessRequestCaptor.capture())).thenReturn(null);

        // when
        underTest.updateBucketAsPublic(DEFAULT_TEST_BUCKET);

        // then
        SetBucketOwnershipControlsRequest ownershipRequest = ownershipRequestCaptor
                .getValue();
        assertEquals(DEFAULT_TEST_BUCKET, ownershipRequest.getBucketName());
        assertEquals(ObjectOwnership.BucketOwnerPreferred.toString(),
                ownershipRequest.getOwnershipControls().getRules().get(0).getOwnership());
        SetPublicAccessBlockRequest blockRequest = accessRequestCaptor.getValue();
        assertEquals(DEFAULT_TEST_BUCKET, blockRequest.getBucketName());
        assertFalse(
                blockRequest.getPublicAccessBlockConfiguration().getBlockPublicAcls());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUpdateBucketAsPublicShouldThrowExceptionWhenGivenParameterIsNull() {
        // when
        underTest.updateBucketAsPublic(null);
    }

    private static URL generateDefaultTestUrl() {
        URL url = null;
        try {
            url = new URL("http://www.test.com");
        } catch (MalformedURLException ex) {
            // do nothing
        }
        return url;
    }

}
