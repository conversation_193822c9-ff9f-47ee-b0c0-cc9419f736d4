/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for handling staff account database operations.
 *
 * <AUTHOR> Van
 */
public interface StaffAccountMapper {

    /**
         SELECT
             email
         FROM
             staff_account
         WHERE
            staff_no = #{staffId}
     */
    @Multiline String SELECT_STAFF_EMAIL_BY_ID = "";

    /**
     * Returns a staff email by the ID of given staff account.
     *
     * @param staffId
     *            the ID of given staff account
     * @return a staff email by the ID of given staff account
     * @see #SELECT_STAFF_EMAIL_BY_ID
     */
    @Select(SELECT_STAFF_EMAIL_BY_ID)
    String findEmailBy(long staffId);
}
