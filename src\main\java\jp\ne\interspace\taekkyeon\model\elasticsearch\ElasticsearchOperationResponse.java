/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model.elasticsearch;

import com.google.gson.annotations.SerializedName;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding the response of elasticsearch operation.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @EqualsAndHashCode
public class ElasticsearchOperationResponse {

    @SerializedName("_index")
    private final String index;
    @SerializedName("_type")
    private final String type;
    @SerializedName("_id")
    private final String id;
    @SerializedName("_version")
    private final int version;
    private final String result;
    @SerializedName("_shards")
    private final ElasticsearchOperationShard shards;
    @SerializedName("_seq_no")
    private final long seqNo;
    @SerializedName("_primary_term")
    private final int primaryTerm;
}
