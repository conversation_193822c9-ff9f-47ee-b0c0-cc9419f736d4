/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding email sender status.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum EmailSenderStatus implements ValueEnum {

    DISABLED(0),
    ENABLED(1);

    private final int value;
}
