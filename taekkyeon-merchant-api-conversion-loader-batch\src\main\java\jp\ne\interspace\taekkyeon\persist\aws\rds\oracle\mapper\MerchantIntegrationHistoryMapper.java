/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryInsertRequest;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybatis mapper for merchant integration history.
 *
 * <AUTHOR> Van
 */
public interface MerchantIntegrationHistoryMapper {

    /**
         SELECT COUNT(*)
         FROM merchant_integration_history
            WHERE key = #{key}
         FETCH FIRST 1 ROW ONLY
     */
    @Multiline String SELECT_KEY_MERCHANT_HISTORY = "";

    /**
     * Returns the number of column have key by the given key.
     *
     * @param key
     *            key of the given key
     * @return the key by the given key
     */
    @Select(SELECT_KEY_MERCHANT_HISTORY)
    Integer findKeyExistingBy(@Param("key") String key);

    /**
        INSERT INTO
            merchant_integration_history (
             key,
             campaign_id,
             conversion_occurs_date,
             merchant,
             data
        ) VALUES (
            #{insertRequest.key, jdbcType=VARCHAR},
            #{insertRequest.campaignId, jdbcType=NUMERIC},
            #{insertRequest.conversionOccursDate, jdbcType=DATE},
            #{insertRequest.merchant, jdbcType=VARCHAR},
            #{insertRequest.data, jdbcType=CLOB}
        )
     */
    @Multiline String INSERT = "";

    /**
     * Inserts a merchant integration history by
     *             {@link MerchantIntegrationHistoryInsertRequest}.
     *
     * @param insertRequest
     *             a list of {@link MerchantIntegrationHistoryInsertRequest} the requests
     * @see #INSERT
     */
    @Insert(INSERT)
    void insert(@Param("insertRequest")
                    MerchantIntegrationHistoryInsertRequest insertRequest);
}
