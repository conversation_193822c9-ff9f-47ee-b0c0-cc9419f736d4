/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.time.LocalDateTime;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryDetailTest;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Integration test for {@link MerchantIntegrationHistoryMapper}.
 *
 * <AUTHOR> Van
 */
public interface TestMerchantIntegrationHistoryMapper {

    /**
        SELECT *
        FROM
            merchant_integration_history
        WHERE
            key = #{key}
     */
    @Multiline String SELECT_MERCHANT_INTEGRATION_HISTORY = "";
    @Select(SELECT_MERCHANT_INTEGRATION_HISTORY)
    @ConstructorArgs({ @Arg(column = "key", javaType = String.class),
            @Arg(column = "campaign_id", javaType = Long.class),
            @Arg(column = "conversion_occurs_date", javaType = LocalDateTime.class),
            @Arg(column = "data", javaType = String.class),
            @Arg(column = "merchant", javaType = String.class)
            })
    MerchantIntegrationHistoryDetailTest findMerchantIntegrationHistory(
            @Param("key") String key);
}
