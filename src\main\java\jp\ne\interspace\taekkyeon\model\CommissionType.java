/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding commission types.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum CommissionType implements ValueEnum {

    NET(0),
    GROSS_FIXED_AMOUNT(1),
    GROSS_AMOUNT_SOLD(2);

    private final int value;
}
