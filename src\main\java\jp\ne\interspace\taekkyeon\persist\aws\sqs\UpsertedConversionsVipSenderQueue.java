/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.sqs;

import javax.inject.Singleton;

import com.google.inject.Inject;

import lombok.Getter;

import jp.ne.interspace.taekkyeon.module.UpsertedConversionsVipSenderQueueNameResolver;

import static lombok.AccessLevel.PROTECTED;

/**
 * SQS queue for sending upserted conversions vip.
 *
 * <AUTHOR> Van
 */
@Singleton
public class UpsertedConversionsVipSenderQueue extends SimpleQueueServiceQueue {

    @Inject @UpsertedConversionsVipSenderQueueNameResolver @Getter(PROTECTED)
    private String partialQueueName;
}
