/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.sqs;

import com.google.inject.Inject;
import lombok.Getter;

import jp.ne.interspace.taekkyeon.module.CampaignClosurePublisherPaymentNotifierQueueNameResolver;

import static lombok.AccessLevel.PROTECTED;

/**
 * SQS queue for campaign closure publisher payment notifier.
 *
 * <AUTHOR>
 */
public class CampaignClosurePublisherPaymentNotifierQueue extends SimpleQueueServiceQueue {

    @Inject @Getter(PROTECTED) @CampaignClosurePublisherPaymentNotifierQueueNameResolver
    private String partialQueueName;
}
