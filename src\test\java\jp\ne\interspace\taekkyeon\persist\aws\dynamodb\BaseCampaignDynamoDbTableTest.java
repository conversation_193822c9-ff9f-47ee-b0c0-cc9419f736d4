/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.dynamodb;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.AttributeValueUpdate;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static jp.ne.interspace.taekkyeon.model.CampaignStatus.RUNNING;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

/**
 * Unit test for {@link BaseCampaignDynamoDbTable}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class BaseCampaignDynamoDbTableTest {

    @InjectMocks @Spy
    private BaseCampaignDynamoDbTable underTest;

    @Test
    public void testUpdateCampaignStatusShouldWorkCorrectlyWhenCalled() {
        // given
        String tableName = "tableName";

        long campaignId = 1;
        Map<String, AttributeValue> updateKey = Collections.emptyMap();
        doReturn(updateKey).when(underTest).createUpdateKey(campaignId);
        Map<String, AttributeValueUpdate> updateValues = Collections.emptyMap();
        doReturn(updateValues).when(underTest).createCampaignStatusUpdate(RUNNING);
        doReturn(tableName).when(underTest).getTableName();
        doReturn(Optional.empty()).when(underTest).updateItem(updateKey, updateValues);

        // when
        underTest.updateCampaignStatus(campaignId, RUNNING);

        // then
        verify(underTest).updateItem(updateKey, updateValues);
    }
}
