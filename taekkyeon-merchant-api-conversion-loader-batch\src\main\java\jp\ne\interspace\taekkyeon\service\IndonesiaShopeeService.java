/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.gson.JsonObject;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;

import jp.ne.interspace.taekkyeon.model.CampaignConditionDetails;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignMapper;

import static com.google.common.base.Splitter.on;
import static com.google.common.base.Strings.isNullOrEmpty;
import static java.math.BigDecimal.ROUND_HALF_UP;
import static java.math.BigDecimal.ZERO;
import static java.time.Instant.ofEpochSecond;
import static java.time.ZonedDateTime.ofInstant;
import static java.util.stream.Collectors.groupingBy;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.COMMA;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER_YYYYMMDD;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.UNDERSCORE;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_CAMPAIGN_ID;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_SECRET_NAME;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.INDONESIA_SHOPEE_API_KEY_KOLS_TIER2;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.INDONESIA_SHOPEE_API_KEY_STORES;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.INDONESIA_SHOPEE_DEFAULT_API_KEY;
import static lombok.AccessLevel.PACKAGE;

/**
 * Implements {@link AbstractShopeeService} for indonesia shopee.
 *
 * <AUTHOR> Shin
 */
public class IndonesiaShopeeService extends AbstractShopeeService {

    private static final String BONUS = "-bonus";
    private static final int PRODUCT_RESULT_ID = 3;
    private static final int CATEGORY_RESULT_ID = 30;
    private static final String CATEGORY_ID_BRAND_COMMISSION = "Brand Commission";
    private static final String DIRECT = "-direct";
    private static final String IN_DIRECT = "-indirect";
    private static final BigDecimal COMMISSION_IN_DIRECT_NON_KOL = new BigDecimal("5");
    private static final BigDecimal COMMISSION_IN_DIRECT_KOLS_TIERS_2 = new BigDecimal("13");
    private static final BigDecimal COMMISSION_IN_DIRECT_STORES = new BigDecimal("6.5");
    private static final BigDecimal HUNDRED_PERCENTAGE = new BigDecimal("100");
    private static final String OPERATOR_AND = "AND";
    private static final String OPERATOR_OR = "OR";

    private static final ImmutableMap<String, BigDecimal> CAMPAIGN_TYPE_AND_SUFFIX_CUSTOM_MAPPING =
            new ImmutableMap.Builder<String, BigDecimal>()
            .put(INDONESIA_SHOPEE_DEFAULT_API_KEY, COMMISSION_IN_DIRECT_NON_KOL)
            .put(INDONESIA_SHOPEE_API_KEY_STORES, COMMISSION_IN_DIRECT_STORES)
            .put(INDONESIA_SHOPEE_API_KEY_KOLS_TIER2, COMMISSION_IN_DIRECT_KOLS_TIERS_2)
            .build();

    @Inject @Named(BIND_KEY_CAMPAIGN_ID) @Getter(PACKAGE)
    private long campaignId;

    @Inject @Named(BIND_KEY_SECRET_NAME) @Getter(PACKAGE)
    private String keySecretName;

    @Inject
    private CampaignMapper campaignMapper;

    private LoadingCache<Long, List<CampaignConditionDetails>>
            campaignConditionDetailsCache = CacheBuilder.newBuilder().maximumSize(1000)
            .build(new CacheLoader<Long, List<CampaignConditionDetails>>() {

                @Override
                public List<CampaignConditionDetails> load(Long campaignId) {
                    return campaignMapper.findCampaignConditionBy(campaignId);
                }
            });

    @Override
    public List<ConversionRegistrationDetails> parse(String responseBody)
            throws Exception {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        if (!isNullOrEmpty(responseBody)) {
            JsonObject obj = getJsonParser().parse(responseBody).getAsJsonObject();
            ShopeeConversionReport conversionReport = parseConversionReport(obj);
            ZoneId zoneId = getZoneId();
            for (ShopeeNode shopeeNode : conversionReport.getNodes()) {
                String[] utmContents = shopeeNode.getUtmContent().split(HYPHEN);
                String clickId = getClickIdFrom(utmContents);
                String targetMd5CampaignId = hashMd5By(getCampaignId());
                if (isTargetCampaignId(utmContents, targetMd5CampaignId)) {
                    String customerType = shopeeNode.getBuyerType().toLowerCase();
                    Instant instant = ofEpochSecond(shopeeNode.getPurchaseTime());
                    ZonedDateTime conversionTime = ofInstant(instant, zoneId);
                    String identifier = shopeeNode.getCheckoutId();
                    Map<String, List<CampaignConditionDetails>> conditionDetailsCacheKey =
                            groupConditionDetailsCacheKey(getCampaignId());
                    for (ShopeeOrder shopeeOrder : shopeeNode.getOrders()) {
                        details.addAll(createConversionDetails(clickId, customerType,
                                conversionTime, identifier, shopeeOrder,
                                conditionDetailsCacheKey));
                    }
                }
            }
        }
        return details;
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> createConversionDetails(String clickId,
            String customerType, ZonedDateTime conversionTime, String identifier,
            ShopeeOrder shopeeOrder,
            Map<String, List<CampaignConditionDetails>> conditionDetailsCacheKey)
            throws UnsupportedEncodingException {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        List<String> products = new LinkedList<>();
        for (ShopeeItem shopeeItem : shopeeOrder.getItems()) {
            if (!isCampaignConditionsValid(conditionDetailsCacheKey, shopeeItem)) {
                continue;
            }

            String temporaryProductId = shopeeItem.getItemId();
            String conversionOccursDate = conversionTime.format(
                    DATE_TIME_FORMATTER_YYYYMMDD);
            String modelId = shopeeItem.getModelId();

            String key = Joiner.on(HYPHEN)
                    .join(getCampaignId(), conversionOccursDate, identifier,
                            shopeeOrder.getOrderId(), shopeeItem.getItemId(), modelId);
            if (!isConversionUnique(key)) {
                continue;
            }

            products.add(temporaryProductId);
            String sku = Joiner.on(UNDERSCORE)
                    .join(shopeeOrder.getOrderId(), temporaryProductId,
                            shopeeItem.getModelId());
            String productId = getItemId(products, sku);
            products.add(sku);

            String categoryId = shopeeItem.getGlobalCategoryLv1Name()
                    .replaceAll(COMMA, EMPTY);

            BigDecimal itemCommission = shopeeItem.getItemCommission();
            BigDecimal totalPrice = shopeeItem.getActualAmount();
            String newCustomerType = customerType + getNewCustomerTypeSuffix(
                    itemCommission, totalPrice);

            boolean isBrandItem = isBrandItem(shopeeItem.getShopId());

            List<ConversionRegistrationDetails> brandItemConversions =
                    processConversionByBrandItemRules(conversionTime, identifier,
                    CATEGORY_RESULT_ID, newCustomerType, categoryId, productId,
                    totalPrice, clickId, shopeeItem.getShopId(), isBrandItem);

            List<ConversionRegistrationDetails> bonusConversions = createExtraBonuses(
                    conversionTime, identifier, PRODUCT_RESULT_ID,
                    newCustomerType, null, productId,
                    clickId, shopeeItem, shopeeOrder, brandItemConversions);
            brandItemConversions.addAll(bonusConversions);
            details.addAll(brandItemConversions);
            insertDataProceeded(
                    createConversionProceeded(conversionTime, brandItemConversions, key));
        }
        return details;
    }

    private boolean isCampaignConditionsValid(
            Map<String, List<CampaignConditionDetails>> conditionDetailsCacheKey,
            ShopeeItem shopeeItem) {
        return conditionDetailsCacheKey.isEmpty() || isConversionValid(
                conditionDetailsCacheKey, shopeeItem);
    }

    @VisibleForTesting
    boolean isStoreCampaign() {
        return getKeySecretName().equals(INDONESIA_SHOPEE_API_KEY_STORES);
    }

    private BigDecimal getConditionNewCustomType() {
        return CAMPAIGN_TYPE_AND_SUFFIX_CUSTOM_MAPPING.get(getKeySecretName());
    }

    private String getNewCustomerTypeSuffix(BigDecimal itemCommission,
            BigDecimal itemValue) {
        if ((itemValue != null) && (itemValue.compareTo(ZERO) != 0) && (
                (itemCommission.divide(itemValue, 2, ROUND_HALF_UP)
                        .multiply(HUNDRED_PERCENTAGE))
                        .compareTo(getConditionNewCustomType()) >= 0)) {
            return DIRECT;
        }
        return IN_DIRECT;
    }

    @VisibleForTesting
    boolean isConversionValid(
            Map<String, List<CampaignConditionDetails>> conditionDetailsCacheKey,
            ShopeeItem shopeeItem) {
        if (conditionDetailsCacheKey == null || conditionDetailsCacheKey.isEmpty()) {
            return true;
        }
        Map<String, Object> shopeeItemMapping = convertDtoToMap(shopeeItem);
        boolean finalResult = false;
        for (Map.Entry<String, List<CampaignConditionDetails>> entry : conditionDetailsCacheKey
                .entrySet()) {
            List<CampaignConditionDetails> campaignConditionDetails = entry.getValue();

            finalResult = finalResult || validateSingleConditionStatement(
                    campaignConditionDetails, shopeeItemMapping);
        }

        return finalResult;
    }

    private boolean validateSingleConditionStatement(
            List<CampaignConditionDetails> campaignConditionDetails,
            Map<String, Object> shopeeItemMapping) {
        Boolean finalResult = null;
        for (CampaignConditionDetails condition : campaignConditionDetails) {
            String itemValue = (String) shopeeItemMapping
                    .get(condition.getConditionName());

            if (!isNullOrEmpty(itemValue)) {
                List<String> conditionValues = on(COMMA)
                        .splitToList(condition.getConditionValues());

                if (condition.getRelativeClauseOperator().equalsIgnoreCase(OPERATOR_OR)) {
                    if (checkContains(itemValue, conditionValues)) {
                        return true;
                    } else {
                        finalResult = finalResult != null ? finalResult || false : false;
                    }
                } else if (condition.getRelativeClauseOperator()
                        .equalsIgnoreCase(OPERATOR_AND)) {
                    if (!checkContains(itemValue, conditionValues)) {
                        return false;
                    } else {
                        finalResult = finalResult != null ? finalResult && true : true;
                    }
                } else {
                    finalResult = true;
                }
            } else {
                return false;
            }
        }
        return finalResult;
    }

    private boolean checkContains(String searchInString, List<String> findStrings) {
        for (String findString : findStrings) {
            if (searchInString.contains(findString)) {
                return true;
            }
        }
        return false;
    }

    private Map<String, Object> convertDtoToMap(Object dtoObject) {
        Map<String, Object> result = new HashMap<>();

        Field[] fields = dtoObject.getClass().getDeclaredFields();

        try {
            for (Field field : fields) {
                field.setAccessible(true);
                result.put(field.getName(), field.get(dtoObject));
            }
        } catch (IllegalAccessException e) {
            System.out.println("Can not convert DTO to Map");
            e.printStackTrace();
        }

        return result;
    }

    @VisibleForTesting
    Map<String, List<CampaignConditionDetails>> groupConditionDetailsCacheKey(
            long campaignId) {
        List<CampaignConditionDetails> conditionDetails = campaignConditionDetailsCache
                .getUnchecked(campaignId);
        return conditionDetails.stream()
                .filter(campaignConditionDetails ->
                        campaignConditionDetails.getGroupShopId() != null)
                .collect(groupingBy(CampaignConditionDetails::getGroupShopId));
    }
}
