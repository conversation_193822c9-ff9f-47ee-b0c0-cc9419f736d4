/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

import jp.ne.interspace.taekkyeon.model.ValueEnum;

/**
 * Enum for holding all the various get parameter flags.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum TrackingType implements ValueEnum {

    COOKIE(0),
    SOCKET(1),
    COOKIE_REDIRECT(2),
    SOCKET_REDIRECT(3);

    private final int value;
}
