/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for {@link StaffAccountMapper}.
 *
 * <AUTHOR> Van
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class StaffAccountMapperTest {

    @Inject
    private StaffAccountMapper underTest;

    @Test
    public void testFindEmailByShouldReturnCorrectDataWhenFoundData() {
        // given
        long staffId = 11;
        String expectedStaffEmail = "<EMAIL>";

        // when
        String actual = underTest.findEmailBy(staffId);

        // then
        assertNotNull(actual);
        assertEquals(expectedStaffEmail, actual);
    }
}
