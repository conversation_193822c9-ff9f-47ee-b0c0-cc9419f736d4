/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.elasticsearch;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.inject.Singleton;

import com.google.common.collect.ImmutableTable;
import com.google.common.collect.Table.Cell;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.extern.slf4j.Slf4j;

import pl.allegro.tech.embeddedelasticsearch.EmbeddedElastic;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;

import static org.apache.ibatis.io.Resources.getResourceAsReader;

/**
 * Initializes an embedded {@code Elasticsearch} instance.
 *
 * <AUTHOR>
 */
@Slf4j @Singleton
public class EmbeddedElasticsearch {

    public static final String BIND_KEY_ELASTICSEARCH_TEST_DATA_PATH = "elasticsearch.test.data";

    @Inject(optional = true) @Named(BIND_KEY_ELASTICSEARCH_TEST_DATA_PATH)
    private ImmutableTable<String, String, String> testDataPaths;

    @Inject
    private EmbeddedElastic embeddedElastic;

    /**
     * Starts an embedded {@code Elasticsearch} instance.
     */
    public void setUp() {
        log.debug("Setting up an embedded elasticsearch instance...");
        try {
            embeddedElastic.start();
            initializeTestData();
        } catch (Exception ex) {
            embeddedElastic.stop();
            String errorMessage =
                    "Failed to initialize an embedded elasticsearch instance";
            log.error(errorMessage, ex);
            throw new TaekkyeonException(errorMessage);
        }
    }

    /**
     * Stops an embedded {@code Elasticsearch} instance.
     */
    public void tearDown() {
        log.debug("Tearing down the embedded elasticsearch instance...");
        try {
            embeddedElastic.stop();
        } catch (RuntimeException e) {
            // do nothing.
        }
    }

    private void initializeTestData() throws IOException {
        if (testDataPaths != null) {
            for (Cell<String, String, String> testDataPath : testDataPaths.cellSet()) {
                try (BufferedReader reader = new BufferedReader(
                        getResourceAsReader(testDataPath.getValue()))) {
                    Map<CharSequence, CharSequence> testDataWithId = new HashMap<>();
                    String json = reader.readLine();
                    int id = 0;
                    while (json != null) {
                        if (!json.isEmpty()) {
                            testDataWithId.put(String.valueOf(id), json);
                            id++;
                        }
                        json = reader.readLine();
                    }
                    embeddedElastic.index(testDataPath.getRowKey(),
                            testDataPath.getColumnKey(), testDataWithId);
                }
            }
        }
    }
}
