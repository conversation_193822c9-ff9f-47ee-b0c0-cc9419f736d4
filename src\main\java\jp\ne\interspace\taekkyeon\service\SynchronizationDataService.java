/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import lombok.Getter;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.model.GlobalConversionStatusSynchronizationData;
import jp.ne.interspace.taekkyeon.model.SynchronizationData;
import jp.ne.interspace.taekkyeon.module.SyncEndTimeResolver;
import jp.ne.interspace.taekkyeon.module.SyncStartTimeResolver;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.SynchronizationDataMapper;

import static java.time.ZoneOffset.UTC;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.INVALID_DATE_TIME;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.ORACLE;
import static lombok.AccessLevel.PACKAGE;

/**
 * Service layer for handling synchronization data.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class SynchronizationDataService {

    private static final String COLLECTION_NAME_CONVERSIONS = "conversions";
    private static final long DEFAULT_CONVERSION_ID = 0;
    private static final long DEFAULT_SITE_ID = 0;

    @Inject @SyncStartTimeResolver @VisibleForTesting @Getter(PACKAGE)
    private LocalDateTime syncStartTime;

    @Inject @SyncEndTimeResolver @Getter
    private LocalDateTime syncEndTime;

    @Inject
    private SynchronizationDataMapper syncMapper;

    @Inject
    private CountryService countryService;

    /**
     * Returns {@code SyncStartTime} from the {@code tableName} of the
     * {@code databaseName}, if given {@code SyncStartTime} is invalid.
     *
     * @param tableName
     *            the table name of the sync data
     * @param databaseName
     *            the target database name criterion
     * @return {@code SyncStartTime} from target database if given {@code SyncStartTime}
     *         is invalid
     */
    public LocalDateTime findSyncStartTime(String tableName, String databaseName) {
        return findSyncStartTime(tableName, databaseName, null);
    }

    /**
     * Returns {@code SyncStartTime} from the {@code tableName} of the
     * {@code databaseName}, if given {@code SyncStartTime} is invalid.
     *
     * @param tableName
     *            the table name of the sync data
     * @param databaseName
     *            the target database criterion
     * @param countryCode
     *            short code by given country
     * @return {@code SyncStartTime} from target database if given {@code SyncStartTime}
     *         is invalid
     */
    public LocalDateTime findSyncStartTime(String tableName, String databaseName,
            String countryCode) {
        if (getSyncStartTime().isEqual(INVALID_DATE_TIME)) {
            SynchronizationData syncData = syncMapper.findSynchronizationData(tableName,
                    databaseName, countryCode);
            if (syncData != null) {
                return syncData.getSyncStartTime();
            }
            throw new TaekkyeonException("Could not find sync start time.");
        }
        return convertLocalDateTime(getSyncStartTime(), countryCode);
    }

    /**
     * Returns {@code SyncStartTime} from {@code tableName} of Oracle, if given
     * {@code SyncStartTime} is invalid.
     *
     * @param tableName
     *            the table name of the sync data
     * @return {@code SyncStartTime} from {@code tableName}, if given
     *         {@code SyncStartTime} is invalid
     */
    public LocalDateTime findSyncStartTime(String tableName) {
        return findSyncStartTime(tableName, ORACLE.toString());
    }

    /**
     * Returns {@link SynchronizationData} from the collection name
     * and the collection name of conversions if update required, otherwise return default
     * {@link SynchronizationData}.
     *
     * @return {@link SynchronizationData} from the collection name
     *          and the collection name of conversions if update required,
     *          otherwise return default {@link SynchronizationData}
     */
    public SynchronizationData findSyncedConversion() {
        if (isSyncStartTimeUpdateRequired()) {
            return syncMapper.findSynchronizationData(COLLECTION_NAME_CONVERSIONS,
                    ORACLE.toString(), null);
        }
        return new SynchronizationData(getSyncStartTime(), DEFAULT_CONVERSION_ID,
                DEFAULT_SITE_ID);
    }

    /**
     * Returns {@link SynchronizationData} by given search criteria.
     *
     * @param tableName
     *          the table name of the sync data
     * @param databaseName
     *          the target database name criterion
     * @param countryCode
     *          short code by given country
     * @return {@link SynchronizationData} by given search criteria
     */
    public SynchronizationData findData(String tableName, String databaseName,
            String countryCode) {
        if (isSyncStartTimeUpdateRequired()) {
            return syncMapper.findSynchronizationData(tableName, databaseName,
                    countryCode);
        }
        return new SynchronizationData(getSyncStartTime(), DEFAULT_CONVERSION_ID,
                DEFAULT_SITE_ID);
    }

    /**
     * Updates {@code SyncStartTime} to {@code tableName} of Oracle, if update required.
     *
     * @param tableName
     *            the table name of the sync data
     */
    public void updateSyncStartTime(String tableName) {
        updateSyncStartTime(tableName, ORACLE.toString());
    }

    /**
     * Updates {@code SyncStartTime} to the {@code tableName} of the {@code databaseName},
     * if update required.
     *
     * @param tableName
     *            the table name of the sync data
     * @param databaseName
     *            the target database criterion
     */
    public void updateSyncStartTime(String tableName, String databaseName) {
        updateSyncStartTime(tableName, databaseName, null);
    }

    /**
     * Updates {@code SyncStartTime} to the {@code tableName} of the {@code databaseName},
     * if update required.
     *
     * @param tableName
     *            the table name of the sync data
     * @param databaseName
     *            the target database criterion
     * @param countryCode
     *            short code by given country
     */
    public void updateSyncStartTime(String tableName, String databaseName,
            String countryCode) {
        if (isSyncStartTimeUpdateRequired()) {
            syncMapper.updateSyncStartTime(getSyncEndTime(), tableName, databaseName,
                    countryCode);
        }
    }

    /**
     * Updates {@code SyncStartTime} to the {@code tableName} of the {@code databaseName}
     * by sync end time from request.
     *
     * @param tableName
     *            the table name of the sync data
     * @param databaseName
     *            the target database name criterion
     * @param syncEndTime
     *            the time of sync end
     * @param countryCode
     *            the code of the country
     */
    public void updateSyncStartTimeBySyncEndTime(String tableName, String databaseName,
            LocalDateTime syncEndTime, String countryCode) {
        syncMapper.updateSyncStartTime(syncEndTime, tableName, databaseName, countryCode);
    }

    /**
     * Updates a conversion in the database if update required.
     *
     * @param conversionId
     *          the ID of given the conversion
     * @param latestUpdateTime
     *          the latest update time of the conversion
     */
    public void updateSyncConversion(long conversionId, LocalDateTime latestUpdateTime) {
        if (isSyncStartTimeUpdateRequired()) {
            syncMapper.updateSynchronizationData(conversionId, latestUpdateTime,
                    COLLECTION_NAME_CONVERSIONS, ORACLE.toString(), null);
        }
    }

    /**
     * Updates a conversion in the database, if update required.
     *
     * @param conversionId
     *          the ID of given the conversion
     * @param latestUpdateTime
     *          the latest update time of the conversion
     * @param tableName
     *          the table name of the sync data
     * @param databaseName
     *          the target database name criterion
     * @param countryCode
     *          short code by given country
     */
    public void updateSyncConversion(long conversionId, LocalDateTime latestUpdateTime,
            String tableName, String databaseName, String countryCode) {
        if (isSyncStartTimeUpdateRequired()) {
            syncMapper.updateSynchronizationData(conversionId, latestUpdateTime,
                    tableName, databaseName, countryCode);
        }
    }

    /**
     * Returns {@link GlobalConversionStatusSynchronizationData} from
     * the {@code tableName}.
     *
     * @param tableName
     *          the table name of the sync data
     * @param countryCode
     *          the country code criterion
     * @return {@link GlobalConversionStatusSynchronizationData} from
     *          the collection name
     */
    public GlobalConversionStatusSynchronizationData
            findGlobalConversionStatusSynchronizationData(String tableName,
                    String countryCode) {
        return syncMapper.findGlobalConversionStatusSynchronizationData(tableName,
                countryCode);
    }

    /**
     * Upserts a {@link GlobalConversionStatusSynchronizationData}.
     *
     * @param globalSyncData
     *          {@link GlobalConversionStatusSynchronizationData}
     * @param countryCode
     *          short code by the given country
     * @return the number of upserted row
     */
    public int upsertGlobalConversionStatus(
            GlobalConversionStatusSynchronizationData globalSyncData,
            String countryCode) {
        return syncMapper.upsertGlobalConversionStatus(globalSyncData, countryCode);
    }

    /**
     * Returns {@link GlobalConversionStatusSynchronizationData} from
     * the {@code tableName} of the {@code databaseName}.
     *
     * @param tableName
     *          the table name of the sync data
     * @param databaseName
     *          the database name criterion
     * @param countryCode
     *          the country code criterion
     * @return {@link GlobalConversionStatusSynchronizationData} from
     *          the collection name
     */
    public int deleteSynchronizationData(String tableName, String databaseName,
            String countryCode) {
        return syncMapper.delete(tableName, databaseName, countryCode);
    }

    /**
     * Converts the UTC of {@code localDateTime} to local date time.
     *
     * @param localDateTime
     *            the given UTC of local date Time
     * @return converted local date time
     */
    public LocalDateTime convertLocalDateTimeBy(LocalDateTime localDateTime) {
        return convertLocalDateTime(localDateTime, EMPTY);
    }

    /**
     * Converts the UTC of {@code localDateTime} to local date time using
     * {@code countryCode}.
     *
     * @param localDateTime
     *            the given UTC of local date Time
     * @param countryCode
     *            the given country code
     * @return converted local date time using {@code countryCode}
     */
    public LocalDateTime convertLocalDateTime(LocalDateTime localDateTime,
            String countryCode) {
        return localDateTime.atZone(UTC).withZoneSameInstant(ZoneId.of(
                countryService.findZoneIdBy(getCountryCodeFrom(countryCode))))
                .toLocalDateTime();
    }

    /**
     * Converts the local date time of {@code localDateTime} to UTC using
     * {@code countryCode}.
     *
     * @param localDateTime
     *            the given local date Time
     * @param countryCode
     *            the given country code
     * @return the coverted {@code localDateTime} of UTC using {@code countryCode}
     */
    public LocalDateTime convertUtc(LocalDateTime localDateTime,
            String countryCode) {
        return localDateTime.atZone(ZoneId.of(countryService.findZoneIdBy(
                        getCountryCodeFrom(countryCode))))
                .withZoneSameInstant(UTC).toLocalDateTime();
    }

    @VisibleForTesting
    String getCountryCodeFrom(String countryCode) {
        if (countryCode == null || countryCode.length() < 2) {
            return getCurrentCountryCode();
        }
        return countryCode;
    }

    @VisibleForTesting
    String getCurrentCountryCode() {
        return getCurrentCountry().getCode();
    }

    private boolean isSyncStartTimeUpdateRequired() {
        return getSyncStartTime().isEqual(INVALID_DATE_TIME);
    }
}
