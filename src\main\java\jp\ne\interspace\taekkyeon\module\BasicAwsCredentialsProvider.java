/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;

/**
 * Basic AWS Credentials Provider.
 *
 * <AUTHOR>
 */
public class BasicAwsCredentialsProvider implements AWSCredentialsProvider {

    private final AWSCredentials credentials;

    /**
     * Creates a {@link BasicAwsCredentialsProvider} instance with the given parameters.
     *
     * @param accessKey
     *            the given accessKey
     * @param secretKey
     *            the given secretKey
     */
    public BasicAwsCredentialsProvider(String accessKey, String secretKey) {
        credentials = new BasicAWSCredentials(accessKey, secretKey);
    }

    @Override
    public AWSCredentials getCredentials() {
        return credentials;
    }

    @Override
    public void refresh() {
    }
}
