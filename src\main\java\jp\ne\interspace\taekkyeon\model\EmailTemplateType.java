/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding all email template types.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum EmailTemplateType implements ValueEnum {

    PUBLISHER_ONBOARDING_WELCOME(0),
    PUBLISHER_ONBOARDING_DAY1(1),
    PUBLISHER_ONBOARDING_DAY2(2),
    PUBLISHER_ONBOARDING_DAY3(3),
    PUBLISHER_ONBOARDING_DAY4(4),
    PUBLISHER_ONBOARDING_DAY5(5),
    PUBLISHER_ONBOARDING_DAY6(6),
    PUBLISHER_ONBOARDING_DAY7(7),
    INFLUENCER_ONBOARDING_WELCOME(10),
    INFLUENCER_ONBOARDING_DAY1(11),
    INFLUENCER_ONBOARDING_DAY2(12),
    INFLUENCER_ONBOARDING_DAY3(13),
    INFLUENCER_ONBOARDING_DAY4(14),
    INFLUENCER_ONBOARDING_DAY5(15),
    INFLUENCER_ONBOARDING_DAY6(16),
    INFLUENCER_ONBOARDING_DAY7(17);

    private final int value;
}
