/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.reader;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import lombok.Getter;

import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.Header;

import jp.ne.interspace.taekkyeon.model.SqsRecord;
import jp.ne.interspace.taekkyeon.model.SqsRecordPayload;
import jp.ne.interspace.taekkyeon.module.MaxExecutionSecondsResolver;
import jp.ne.interspace.taekkyeon.module.VisibilityTimeoutSecondsResolver;
import jp.ne.interspace.taekkyeon.module.WaitTimeSecondsResolver;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.SimpleQueueServiceQueue;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.MAX_NUMBER_OF_SQS_MESSAGES;
import static lombok.AccessLevel.PACKAGE;

/**
 * Base {@link RecordReader} for reading data from an SQS queue.
 *
 * <AUTHOR> Varga
 */
public abstract class SqsRecordReader implements RecordReader {

    @Inject @MaxExecutionSecondsResolver @VisibleForTesting @Getter(value = PACKAGE)
    private int maxExecutionSeconds;

    @Inject @VisibilityTimeoutSecondsResolver @VisibleForTesting @Getter(value = PACKAGE)
    private int visibilityTimeoutSeconds;

    @Inject @WaitTimeSecondsResolver @VisibleForTesting @Getter(value = PACKAGE)
    private int waitTimeSeconds;

    @VisibleForTesting @Getter(PACKAGE)
    private String queueUrl;

    @VisibleForTesting @Getter(PACKAGE)
    private LocalDateTime endTime;

    @VisibleForTesting @Getter(PACKAGE)
    private long currentRecordNumber;

    @VisibleForTesting @Getter(PACKAGE)
    private List<Message> cachedMessages;

    @Override
    public void open() throws Exception {
        endTime = getCurrentTime().plusSeconds(getMaxExecutionSeconds());
        currentRecordNumber = 0;
        queueUrl = getSourceQueue().getQueueUrl();
        cachedMessages = new LinkedList<>();
    }

    @Override
    public SqsRecord readRecord() throws Exception {
        return nextRecord();
    }

    @Override
    public void close() throws Exception {
        // do nothing
    }

    /**
     * Returns the source SQS queue.
     *
     * @return the source SQS queue
     */
    protected abstract SimpleQueueServiceQueue getSourceQueue();

    @VisibleForTesting
    LocalDateTime getCurrentTime() {
        return LocalDateTime.now();
    }

    @VisibleForTesting
    Message receiveMessage() {
        if (getCachedMessages().isEmpty()) {
            ReceiveMessageRequest request = new ReceiveMessageRequest(getQueueUrl())
                    .withMaxNumberOfMessages(MAX_NUMBER_OF_SQS_MESSAGES)
                    .withVisibilityTimeout(getVisibilityTimeoutSeconds())
                    .withWaitTimeSeconds(getWaitTimeSeconds());
            ReceiveMessageResult result = getSourceQueue().receiveMessage(request);
            getCachedMessages().addAll(result.getMessages());
        }

        if (getCachedMessages().isEmpty()) {
            return null;
        } else {
            return getCachedMessages().remove(0);
        }
    }

    private SqsRecord nextRecord() {
        LocalDateTime currentTime = getCurrentTime();
        if (currentTime.isBefore(getEndTime())) {
            Message message = receiveMessage();
            if (message == null) {
                return null;
            } else {
                currentRecordNumber++;
                return new SqsRecord(
                        new Header(getCurrentRecordNumber(), null,
                                Date.from(currentTime.toInstant(ZoneOffset.UTC))),
                        new SqsRecordPayload(message.getReceiptHandle(),
                                message.getBody()));
            }
        }
        return null;
    }
}
