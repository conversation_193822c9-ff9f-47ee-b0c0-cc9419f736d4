/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding campaign condition details.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class CampaignConditionDetails {

    private final long campaignId;
    private final String conditionName;
    private final String conditionValues;
    private final String groupShopId;
    private final String relativeClauseOperator;
}
