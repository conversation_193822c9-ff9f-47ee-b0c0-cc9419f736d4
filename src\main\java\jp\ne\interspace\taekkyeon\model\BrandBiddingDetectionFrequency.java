/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for brand bidding frequency.
 *
 * <AUTHOR> Mayur
 */
@Getter @AllArgsConstructor
public enum BrandBiddingDetectionFrequency implements ValueEnum {

    EVERY_HOUR(1),
    EVERY_TWO_HOUR(2),
    EVERY_THREE_HOUR(3),
    EVERY_FOUR_HOUR(4),
    EVERY_SIX_HOUR(6),
    EVERY_EIGHT_HOUR(8),
    EVERY_TWELVE_HOUR(12),
    EVERY_TWENTY_FOUR_HOUR(24);

    private final int value;
}
