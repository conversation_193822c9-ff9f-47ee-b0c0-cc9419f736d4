/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.validator;

import javax.inject.Singleton;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;

import static java.lang.String.format;

/**
 * Class for validating log items.
 *
 * <AUTHOR>
 */
@Singleton
public class LogValidator {

    private static final String GREATER_THAN_BYTE_MESSAGE_FORMAT = "The parameter [%s] (value:[%s]) is greater than %s bytes";

    /**
     * If the byte count of the {@code fieldValue} is greater than the given
     * {@code maxByteCount}, throws {@link TaekkyeonException}.
     *
     * @param fieldValue
     *            The field value of the log to be validate
     * @param maxByteCount
     *            The max byte count to validate
     * @param fieldName
     *            The field name of the log to be output as error log
     */
    public void validateMaxByteCountOf(String fieldValue, int maxByteCount,
            String fieldName) {
        if (fieldValue.getBytes().length > maxByteCount) {
            throw new TaekkyeonException(
                    format(GREATER_THAN_BYTE_MESSAGE_FORMAT, fieldName, fieldValue,
                            maxByteCount));
        }
    }
}
