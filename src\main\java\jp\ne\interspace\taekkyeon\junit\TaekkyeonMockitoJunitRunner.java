/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.junit;

import java.lang.reflect.InvocationTargetException;

import org.junit.runners.model.InitializationError;
import org.mockito.MockitoAnnotations;

/**
 * Mockito-Guice initializer for JUnit tests.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonMockitoJunitRunner extends TaekkyeonJunitRunner {

    /**
     * Constructor of Mockito-Guice initializer.
     *
     * @param testClass
     *            JUnit test class to be run
     * @throws InvocationTargetException
     *             when invoking the super constructor fails
     * @throws InitializationError
     *             when test setup or Guice setup fails
     */
    public TaekkyeonMockitoJunitRunner(Class<?> testClass)
            throws Exception {
        super(testClass);
    }

    @Override
    public Object createTest() throws Exception {
        Object testClazz = super.createTest();
        MockitoAnnotations.initMocks(testClazz);
        return testClazz;
    }

}
