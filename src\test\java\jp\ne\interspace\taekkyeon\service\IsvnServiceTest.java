/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.util.TaekkyeonHttpClient;

import static com.google.common.collect.ImmutableMap.of;
import static jp.ne.interspace.taekkyeon.service.IsvnService.CLIENT_ID;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link IsvnServiceTest}.
 *
 * <AUTHOR> Nguyen
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class IsvnServiceTest {

    private static final String CLIENT_ID_FIELD = "client-id";
    private static final String CLIENT_TRACE_NO_FIELD = "client-trace-no";
    private static final String CLIENT_REQUEST_TIME_FIELD = "client-request-time";
    private static final String CLIENT_SIGNATURE_FIELD = "client-signature";

    @InjectMocks @Spy
    private IsvnService underTest;

    @Mock
    private TaekkyeonHttpClient httpClient;

    @Mock
    private Logger logger;

    @Test
    public void testSendCallbackEventShouldSentCorrectValuesWhenCalled()
            throws Exception {
        // given
        String isvnUrl = "isvnUrl";
        String requestBody = "requestBody";
        String responseStatus = "responseStatus";

        doReturn(isvnUrl).when(underTest).getIsvnUrl();
        Map<String, String> header = of("Content-Type", "application/text");
        when(httpClient.post(isvnUrl, header, requestBody)).thenReturn(responseStatus);
        doReturn(header).when(underTest).buildHeaders();

        // when
        underTest.sendCallbackEvent(requestBody);

        // then
        verify(httpClient).post(isvnUrl, header, requestBody);
        verify(underTest,never()).waitFiveMinutes();
    }

    @Test
    public void testSendCallbackEventShouldCallMethodWaitFiveMinutesThreeTimesWhenApiResponseIsNull()
            throws Exception {
        // given
        String isvnUrl = "isvnUrl";
        String requestBody = "requestBody";
        Map<String, String> header = of("Content-Type", "application/text");

        doReturn(logger).when(underTest).getLogger();
        doReturn(header).when(underTest).buildHeaders();
        when(httpClient.post(isvnUrl, header, requestBody)).thenReturn(null);
        doReturn(isvnUrl).when(underTest).getIsvnUrl();
        doNothing().when(underTest).waitFiveMinutes();

        // when
        underTest.sendCallbackEvent(requestBody);

        // then
        verify(underTest, times(3)).waitFiveMinutes();
        verify(underTest, never()).getLogger();
    }

    @Test
    public void testSendCallbackEventShouldLogErrorMessageAndCallMethodWaitFiveMinutesThreeTimesWhenOccurredException()
            throws Exception {
        // given
        String isvnUrl = "isvnUrl";
        String requestBody = "requestBody";
        Map<String, String> header = of("Content-Type", "application/text");
        RuntimeException exception = new RuntimeException();

        doReturn(logger).when(underTest).getLogger();
        doReturn(header).when(underTest).buildHeaders();
        doThrow(exception).when(httpClient).post(isvnUrl, header, requestBody);
        doReturn(isvnUrl).when(underTest).getIsvnUrl();
        doNothing().when(underTest).waitFiveMinutes();

        // when
        underTest.sendCallbackEvent(requestBody);

        // then
        verify(logger, times(3)).error(
                "Error occurs while sending API to ISVN: requestBody", exception);
        verify(underTest, times(3)).waitFiveMinutes();
    }

    @Test
    public void testBuildHeadersShouldReturnCorrectValueWhenCalled()
            throws Exception {
        // given
        String traceNo = "traceNo";
        String requestTime = "requestTime";
        String signature = "signature";
        Map<String, String> expected =
                of(CLIENT_ID_FIELD, CLIENT_ID, CLIENT_TRACE_NO_FIELD,
                        traceNo, CLIENT_REQUEST_TIME_FIELD, requestTime,
                        CLIENT_SIGNATURE_FIELD, signature);

        doReturn(traceNo).when(underTest).getUuid();
        doReturn(requestTime).when(underTest).getEpochTime();
        doReturn(signature).when(underTest).generateSignature(traceNo, requestTime);

        // when
        Map<String, String> actual = underTest.buildHeaders();

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testBuildHeadersShouldShouldLogErrorMessageWhenOccurredException()
            throws Exception {
        // given
        String traceNo = "traceNo";
        String requestTime = "requestTime";
        RuntimeException exception = new RuntimeException();

        doReturn(logger).when(underTest).getLogger();
        doReturn(traceNo).when(underTest).getUuid();
        doReturn(requestTime).when(underTest).getEpochTime();
        doThrow(exception).when(underTest).generateSignature(traceNo, requestTime);

        // when
        underTest.buildHeaders();

        // then
        verify(logger).error("Error occurs while build header", exception);
    }

    @Test
    public void testGenerateSignatureShouldReturnCorrectValueWhenCalled()
            throws Exception {
        // given
        String traceNo = "traceNo";
        String requestTime = "requestTime";
        String expected = "7912bae2f31094e7ff82741bfc21c58e918b753b075acff2309399beaeae6eba";

        // when
        String actual = underTest.generateSignature(traceNo, requestTime);

        // then
        assertEquals(expected, actual);
    }
}
