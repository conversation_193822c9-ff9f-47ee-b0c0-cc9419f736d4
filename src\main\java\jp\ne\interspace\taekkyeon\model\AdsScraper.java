/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.Location.DEFAULT_LOCATION;

/**
 * DTO holding the data of ads scraper.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @ToString
public class AdsScraper {

    public static final AdsScraper DEFAULT_ADS_SCRAPER = new AdsScraper(null, 0, true,
            EMPTY, EMPTY, EMPTY, DEFAULT_LOCATION, EMPTY);

    private final String countryCode;
    private final long campaignId;
    private final boolean isMobile;
    private final String keyword;
    private final String language;
    private final String userAgent;
    private final Location location;
    private final String googleUrl;
}
