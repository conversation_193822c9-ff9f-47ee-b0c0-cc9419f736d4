/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding campaign budget types.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum CampaignBudgetType implements ValueEnum {

    CONVERSION_COUNT(1, "Conversion count"),
    CONVERSION_TOTAL_COMMISSIONS(2, "Conversion total commissions"),
    CLICK_COUNT(3, "Click count"),
    CLICK_TOTAL_COMMISSIONS(4, "Click total commissions");

    private final int value;
    private final String displayName;
}
