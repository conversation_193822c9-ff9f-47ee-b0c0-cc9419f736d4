/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDateTime;
import java.time.YearMonth;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding the data of global conversion status synchronization data.
 *
 * <AUTHOR> Pachpind
 */
@Getter @EqualsAndHashCode(callSuper = true)
public class GlobalConversionStatusSynchronizationData extends SynchronizationData {

    private final YearMonth closedMonth;

    /**
     * Constructor to be used by the relevant MyBatis mapper and for default item
     * generation. Cannot be auto-generated because of the {@code super()} call.
     */
    public GlobalConversionStatusSynchronizationData(LocalDateTime syncStartTime,
            long conversionId, long siteId, YearMonth closedMonth) {
        super(syncStartTime, conversionId, siteId);
        this.closedMonth = closedMonth;
    }
}
