/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.util.Properties;

import javax.sql.DataSource;

import com.google.common.collect.ImmutableMap;
import com.google.inject.Provider;

import lombok.RequiredArgsConstructor;

import jp.ne.interspace.taekkyeon.model.Pair;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.JDBC_PASSWORD_SECRET_KEY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.JDBC_URL_SECRET_KEY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.JDBC_USER_NAME_SECRET_KEY;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.MARIADB;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.ORACLE;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.REDSHIFT;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.getSecretValues;

/**
 * {@link DataSource} provider of ACCESSTRADE RDBMS.
 *
 * <AUTHOR> Shin
 */
@RequiredArgsConstructor
public class TaekkyeonMyBatisPropertiesProvider implements Provider<Properties> {

    public static final String BIND_KEY_MYBATIS_ENVIRONMENT_ID = "mybatis.environment.id";
    public static final String DEFAULT_MYBATIS_ENVIRONMENT_NAME = "Taekkyeon Batch";

    private static final String BIND_KEY_JDBC_DRIVER = "JDBC.driver";
    private static final String BIND_KEY_JDBC_URL = "JDBC.url";
    private static final String BIND_KEY_JDBC_USER_NAME = "JDBC.username";
    private static final String BIND_KEY_JDBC_PASSWORD = "JDBC.password";
    private static final String BIND_KEY_JDBC_AUTO_COMMIT = "JDBC.autoCommit";
    private static final String WRONG_DATABASE_TYPE_MESSAGE = "Wrong database type";

    private static final String REDSHIFT_JDBC_URL_SECRET_KEY = "REDSHIFT_JDBC_URL";
    private static final String REDSHIFT_USER_NAME_SECRET_KEY = "REDSHIFT_USER_NAME";
    private static final String REDSHIFT_PASSWORD_SECRET_KEY = "REDSHIFT_PASSWORD";
    private static final String MARIADB_JDBC_URL_SECRET_KEY = "EXTERNAL_MARIADB_JDBC_URL";
    private static final String MARIADB_USER_NAME_SECRET_KEY = "EXTERNAL_MARIADB_USER_NAME";
    private static final String MARIADB_PASSWORD_SECRET_KEY = "EXTERNAL_MARIADB_PASSWORD";

    private static final ImmutableMap<DatabaseType, String> JDBC_DRIVERS = new ImmutableMap.Builder<DatabaseType, String>()
            .put(ORACLE, "oracle.jdbc.driver.OracleDriver")
            .put(REDSHIFT, "com.amazon.redshift.jdbc.Driver")
            .put(MARIADB, "org.mariadb.jdbc.Driver")
            .build();

    private static final ImmutableMap<DatabaseType, String> AUTO_COMMITS = new ImmutableMap.Builder<DatabaseType, String>()
            .put(ORACLE, "true")
            .put(REDSHIFT, "false")
            .put(MARIADB, "true")
            .build();

    private final DatabaseType databaseType;

    @Override
    public Properties get() {
        return getMyBatisProperties();
    }

    private Properties getMyBatisProperties() {
        Country country = getCurrentCountry();
        Environment environment = getCurrentEnvironment();

        Pair<String, String> usernameAndPassword = getUsernameAndPassword(country,
                environment);

        Properties properties = new Properties();
        properties.setProperty(BIND_KEY_MYBATIS_ENVIRONMENT_ID,
                DEFAULT_MYBATIS_ENVIRONMENT_NAME);
        properties.setProperty(BIND_KEY_JDBC_DRIVER, JDBC_DRIVERS.get(databaseType));
        properties.setProperty(BIND_KEY_JDBC_URL,
                getJdbcUrl(country, environment));
        properties.setProperty(BIND_KEY_JDBC_USER_NAME, usernameAndPassword.getLeft());
        properties.setProperty(BIND_KEY_JDBC_PASSWORD, usernameAndPassword.getRight());
        properties.setProperty(BIND_KEY_JDBC_AUTO_COMMIT, AUTO_COMMITS.get(databaseType));

        return properties;
    }

    private String getJdbcUrl(Country country, Environment environment) {
        switch (databaseType) {
            case ORACLE:
                return getSecretValues(country, environment, JDBC_URL_SECRET_KEY);
            case REDSHIFT:
                return getSecretValues(country, environment, REDSHIFT_JDBC_URL_SECRET_KEY);
            case MARIADB:
                return getSecretValues(country, environment, MARIADB_JDBC_URL_SECRET_KEY);
            default:
                throw new RuntimeException(WRONG_DATABASE_TYPE_MESSAGE);
        }
    }

    private Pair<String, String> getUsernameAndPassword(Country country,
            Environment environment) {
        String username;
        String password;
        switch (databaseType) {
            case ORACLE:
                username = getSecretValues(country, environment, JDBC_USER_NAME_SECRET_KEY);
                password = getSecretValues(country, environment, JDBC_PASSWORD_SECRET_KEY);
                break;
            case REDSHIFT:
                username = getSecretValues(country, environment, REDSHIFT_USER_NAME_SECRET_KEY);
                password = getSecretValues(country, environment, REDSHIFT_PASSWORD_SECRET_KEY);
                break;
            case MARIADB:
                username = getSecretValues(country, environment, MARIADB_USER_NAME_SECRET_KEY);
                password = getSecretValues(country, environment, MARIADB_PASSWORD_SECRET_KEY);
                break;
            default:
                throw new RuntimeException(WRONG_DATABASE_TYPE_MESSAGE);
        }
        return Pair.of(username, password);
    }
}
