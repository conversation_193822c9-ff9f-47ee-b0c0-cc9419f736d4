/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding all the different referral account statuses.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum ReferralAccountStatus implements ValueEnum {

    INACTIVE(0),
    ACTIVE(1);

    private final int value;
}
