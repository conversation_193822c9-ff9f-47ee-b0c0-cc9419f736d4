/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding site lead generation data.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum SiteLeadGeneration implements ValueEnum {

    EMPTY(0),
    PRODUCT_AND_SERVICE_REVIEW(1),
    COUPON(2),
    OTHER_DISCOUNT_INFORMATION(3),
    CASHBACK(4),
    REWARD_POINT(5),
    PRICE_COMPARISON(6),
    PROMOTION_BANNER(7),
    AFFILIATE_NETWORK(8),
    OTHER_ADVERTISING_NETWORK(9);

    private final int value;
}

