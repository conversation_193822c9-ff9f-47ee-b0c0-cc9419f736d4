/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.ConversionUpdateRequestTest;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for {@link ConversionUpdateRequestMapper}.
 *
 * <AUTHOR> <PERSON>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class ConversionUpdateRequestMapperTest {

    @Inject
    private ConversionUpdateRequestMapper underTest;

    @Inject
    private TestConversionUpdateRequestMapper mapper;

    @Test
    public void testInsertRequestShouldWorkCorrectlyWhenCalled() {
        // given
        String staffEmail = "<EMAIL>";
        String fileName = "fileName1";
        int dataCount = 1;
        long campaignId = 1;

        // when
        underTest.insertRequest(fileName, campaignId, staffEmail,
                dataCount);

        // then
        ConversionUpdateRequestTest actual = mapper.findConversionUpdateRequestBy(
                fileName);
        assertNotNull(actual);
        assertEquals("fileName1", actual.getFileName());
        assertEquals(0, actual.getErrorCount());
        assertEquals(1, actual.getCampaignId());
        assertEquals("<EMAIL>", actual.getEmailStaff());
        assertEquals(1, actual.getDataCount());
    }
}
