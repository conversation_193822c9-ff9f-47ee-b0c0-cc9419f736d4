/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Collection;
import java.util.Properties;
import java.util.Set;

import com.google.inject.Exposed;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.PrivateModule;
import com.google.inject.Provides;
import com.google.inject.name.Names;

import lombok.extern.slf4j.Slf4j;

import org.apache.ibatis.io.ResolverUtil;
import org.apache.ibatis.io.ResolverUtil.Test;
import org.apache.ibatis.jdbc.ScriptRunner;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionManager;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.mybatis.guice.MyBatisModule;
import org.mybatis.guice.datasource.builtin.PooledDataSourceProvider;
import org.mybatis.guice.datasource.helper.JdbcHelper;
import org.mybatis.guice.session.SqlSessionFactoryProvider;
import org.mybatis.guice.session.SqlSessionManagerProvider;

import static jp.ne.interspace.taekkyeon.module.DatabaseType.REDSHIFT;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonMyBatisModule.BIND_KEY_MYBATIS_ENVIRONMENT_ID;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonMyBatisModule.DEFAULT_MYBATIS_ENVIRONMENT_NAME;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonMyBatisModule.MYBATIS_SQL_MAPPER_PACKAGES;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonMyBatisModule.MYBATIS_TYPE_HANDLER_PACKAGE;
import static org.apache.ibatis.io.Resources.getResourceAsReader;

/**
 * Taekkyeon module for JUnit HSQLDB data source.
 *
 * <AUTHOR> Shin
 */
@Slf4j
public class TaekkyeonHsqldbRedshiftModule extends PrivateModule {

    private static final String BIND_KEY_HSQLDB_USERNAME = "hsqldb.username";
    private static final String BIND_KEY_HSQLDB_PASSWORD = "hsqldb.password";
    private static final String BIND_KEY_HSQLDB_AUTOCOMMIT = "hsqldb.autoCommit";

    private static final String HSQLDB_USERNAME = "sa";
    private static final String HSQLDB_PASSWORD = "";
    private static final String HSQLDB_AUTOCOMMIT = "false";

    private static final String HSQLDB_SCHEMA_SQL_FILE_PATH =
            "database/hsqldb/redshift-schema.sql";

    private static final String HSQLDB_DATA_SQL_FILE_PATH =
            "database/hsqldb/redshift-test-data.sql";

    @Override
    protected void configure() {
        install(new MyBatisModule() {

            @Override
            protected void initialize() {
                Names.bindProperties(binder(), getHsqldbProperties());

                bindDataSourceProviderType(PooledDataSourceProvider.class);
                bindTransactionFactoryType(JdbcTransactionFactory.class);

                addMapperClassesIn(MYBATIS_SQL_MAPPER_PACKAGES.get(REDSHIFT));
                addTypeHandlerClasses(MYBATIS_TYPE_HANDLER_PACKAGE);

                install(JdbcHelper.HSQLDB_IN_MEMORY_NAMED);

                bindSqlSessionFactory();
                bindSqlSessionManager();
            }

            private void bindSqlSessionFactory() {
                bind(SqlSessionFactory.class).annotatedWith(RedshiftResolver.class)
                        .toProvider(SqlSessionFactoryProvider.class);
                expose(SqlSessionFactory.class).annotatedWith(RedshiftResolver.class);
            }

            private void bindSqlSessionManager() {
                bind(SqlSessionManager.class).annotatedWith(RedshiftResolver.class)
                        .toProvider(SqlSessionManagerProvider.class);
                expose(SqlSessionManager.class).annotatedWith(RedshiftResolver.class);
            }

            private Properties getHsqldbProperties() {
                Properties properties = new Properties();
                properties.setProperty(BIND_KEY_MYBATIS_ENVIRONMENT_ID,
                        DEFAULT_MYBATIS_ENVIRONMENT_NAME);
                properties.setProperty(BIND_KEY_HSQLDB_USERNAME, HSQLDB_USERNAME);
                properties.setProperty(BIND_KEY_HSQLDB_PASSWORD, HSQLDB_PASSWORD);
                properties.setProperty(BIND_KEY_HSQLDB_AUTOCOMMIT, HSQLDB_AUTOCOMMIT);
                return properties;
            }

            private void addMapperClassesIn(String packageName) {
                bind(getClassesIn(packageName));
            }

            private Set<Class<?>> getClassesIn(String packageName) {
                Test isObject = new ResolverUtil.IsA(Object.class);
                return new ResolverUtil<Object>().find(isObject, packageName)
                        .getClasses();
            }

            private void bind(Collection<Class<?>> mappers) {
                for (Class<?> mapper : mappers) {
                    addMapperClass(mapper);
                    expose(mapper);
                }
            }

            @Provides @Exposed
            private EmbeddedHsqldbInitializer initializeEmbeddedHsqldb(Injector injector) {
                log.debug("Initializing the embedded HSQLDB instances for integration tests...");
                try {
                    initializeHsqlTestDatabaseScript(injector);
                } catch (Exception ex) {
                    log.error("Could not load the test database scripts.", ex);
                }
                return new EmbeddedHsqldbInitializer();
            }

            private SqlSessionFactory initializeHsqlTestDatabaseScript(Injector injector)
                    throws IOException, SQLException {
                Key<SqlSessionFactory> key = Key.get(SqlSessionFactory.class,
                        RedshiftResolver.class);
                SqlSessionFactory sqlSessionFactory = injector.getInstance(key);
                if (sqlSessionFactory != null) {
                    org.apache.ibatis.mapping.Environment environment = sqlSessionFactory
                            .getConfiguration().getEnvironment();
                    ScriptRunner runner = new ScriptRunner(
                            environment.getDataSource().getConnection());
                    runner.setLogWriter(null);
                    runner.setAutoCommit(true);
                    runner.setStopOnError(true);
                    runner.runScript(getResourceAsReader(HSQLDB_SCHEMA_SQL_FILE_PATH));
                    runner.runScript(getResourceAsReader(HSQLDB_DATA_SQL_FILE_PATH));
                    runner.closeConnection();
                }
                return sqlSessionFactory;
            }
        });
    }
}
