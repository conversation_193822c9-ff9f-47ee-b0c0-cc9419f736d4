/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.Date;
import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.module.Environment;
import jp.ne.interspace.taekkyeon.persist.aws.redshift.mapper.core.RedshiftDataMapper;
import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceClient;

import static java.time.ZoneOffset.UTC;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonRedshiftSyncModule.BIND_KEY_BUCKET_NAME;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonRedshiftSyncModule.BIND_KEY_REDSHIFT_CREDENTIALS;
import static lombok.AccessLevel.PACKAGE;

/**
 * Abstract service layer for handling data for redshift.
 *
 * <AUTHOR> NT
 */
@Slf4j
public abstract class RedshiftService {

    private static final String FAILED_TO_PROCESS_CSV_FILE = "Failed to process CSV file %s on S3 or deactivate data";
    private static final String S3_FILE_PATH_FORMAT = "s3://%s/%s";
    private static final String SUCCESS = "success";
    private static final String ERROR = "error";

    @Inject @Named(BIND_KEY_BUCKET_NAME) @Getter(PACKAGE)
    private String bucketName;

    @Inject @Named(BIND_KEY_REDSHIFT_CREDENTIALS) @Getter(PACKAGE)
    private String redshiftCredentials;

    @Inject @VisibleForTesting @Getter(value = PACKAGE)
    private SimpleStorageServiceClient s3Client;

    public abstract RedshiftDataMapper getRedShiftDataMapper();

    public abstract String getFileName();

    /**
     * Drops all temporary tables.
     *
     * @param country
     *          the current country
     * @param environment
     *          the current environment
     */
    public void dropAllTemporaryTables(Country country, Environment environment) {
        List<String> temporaryTableNames = getRedShiftDataMapper()
                .findDistinctTemporaryTables(country, environment);
        for (String tableName : temporaryTableNames) {
            getRedShiftDataMapper().dropTemporaryTableBy(tableName);
        }
    }

    /**
     * Upserts data to redshift safely
     * 1. Create csv file by contents
     * 2. Upload file to s3
     * 3. Copy data to temporary table redshift
     * 3. Insert new data and update old data from temporary table into main table
     * 3. Move file into success folder if process successfully,
     *    otherwise move file into error folder .
     *
     * @param country
     *          the current country
     * @param environment
     *          the current environment
     * @param csvContents
     *          the given contents
     * @param fileName
     *          the given file name
     * @param temporaryTableKey
     *          the temporary table key file name
     */
    public void safelyUpsertData(Country country, Environment environment,
            String csvContents, String fileName, String temporaryTableKey) {
        try {
            getS3Client().putObject(getBucketName(), fileName, csvContents);
            upsertData(country, environment, fileName, temporaryTableKey);
            move(fileName, SUCCESS);
        } catch (Exception e) {
            getLogger().error(String.format(FAILED_TO_PROCESS_CSV_FILE, fileName), e);
            move(fileName, ERROR);
            throw e;
        }
    }

    @VisibleForTesting
    void upsertData(Country country, Environment environment, String fileName,
            String temporaryTableKey) {
        getRedShiftDataMapper().createTemporarySyncTable(country, environment,
                temporaryTableKey);
        getRedShiftDataMapper().copy(country, environment,
                String.format(S3_FILE_PATH_FORMAT, getBucketName(), fileName),
                getRedshiftCredentials(), temporaryTableKey);
        getRedShiftDataMapper().updateDataByTemporaryTable(country, environment,
                temporaryTableKey);
        getRedShiftDataMapper().insertDataByTemporaryTable(country, environment,
                temporaryTableKey);
        getRedShiftDataMapper().dropTemporaryTable(country, environment,
                temporaryTableKey);
    }

    @VisibleForTesting
    void move(String fileName, String targetFolder) {
        if (getS3Client().doesObjectExist(getBucketName(), fileName)) {
            getS3Client().copyObject(getBucketName(), fileName, getBucketName(),
                    targetFolder + "/" + fileName);
            getS3Client().deleteObject(getBucketName(), fileName);
        }
    }

    /**
     * Creates name of redshift table.
     *
     * @param date
     *          the given create time
     * @return the full file name
     */
    public String createFileNameBy(Date date) {
        return Joiner.on(EMPTY).join(getFileName(),
                date.toInstant().atOffset(UTC).toLocalDateTime(), ".csv");
    }

    /**
     * Gets currrent country.
     *
     * @return the {@link Country} current country
     */
    public Country getCurrentCountry() {
        return Country.getCurrentCountry();
    }

    /**
     * Gets currrent environment.
     *
     * @return the {@link Environment} current environment
     */
    public Environment getCurrentEnvironment() {
        return Environment.getCurrentEnvironment();
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }
}
