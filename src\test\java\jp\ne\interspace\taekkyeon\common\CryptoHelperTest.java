/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

/**
 * Unit tests for {@link CryptoHelper}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class CryptoHelperTest {

    @Inject
    private CryptoHelper underTest;

    @Test
    public void testBase64EncryptShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        String string = "accesstrade";
        String expected = "YWNjZXNzdHJhZGU=";

        // when
        String actual = underTest.base64Encrypt(string);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testBase64EncryptShouldReturnGivenStringWhenGivenEmpty()
            throws Exception {
        // given
        String string = "";

        // when
        String actual = underTest.base64Encrypt(string);

        // then
        assertNotNull(actual);
        assertEquals(string, actual);
    }

    @Test
    public void testBase64EncryptShouldReturnGivenStringWhenGivenNull() throws Exception {
        // given
        String string = null;

        // when
        String actual = underTest.base64Encrypt(string);

        // then
        assertNull(actual);
    }

    @Test
    public void testBase64DecryptShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        String string = "YWNjZXNzdHJhZGU=";
        String expected = "accesstrade";

        // when
        String actual = underTest.base64Decrypt(string);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testBase64DecryptShouldReturnGivenStringWhenGivenEmpty()
            throws Exception {
        // given
        String string = "";

        // when
        String actual = underTest.base64Decrypt(string);

        // then
        assertNotNull(actual);
        assertEquals(string, actual);
    }

    @Test
    public void testBase64DecryptShouldReturnGivenStringWhenGivenNull() throws Exception {
        // given
        String string = null;

        // when
        String actual = underTest.base64Decrypt(string);

        // then
        assertNull(actual);
    }

    @Test
    public void testAes256EncryptShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        String string = "accesstrade";
        String expected = "kQzccgZ9vTazCrEY5gogjA==";

        // when
        String actual = underTest.aes256Encrypt(string);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testAes256EncryptShouldReturnGivenStringWhenGivenEmpty()
            throws Exception {
        // given
        String string = "";

        // when
        String actual = underTest.aes256Encrypt(string);

        // then
        assertNotNull(actual);
        assertEquals(string, actual);
    }

    @Test
    public void testAes256EncryptShouldReturnGivenStringWhenGivenNull() throws Exception {
        // given
        String string = null;

        // when
        String actual = underTest.aes256Encrypt(string);

        // then
        assertNull(actual);
    }

    @Test
    public void testAes256DecryptShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        String string = "kQzccgZ9vTazCrEY5gogjA==";
        String expected = "accesstrade";

        // when
        String actual = underTest.aes256Decrypt(string);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testAes256DecryptShouldReturnGivenStringWhenGivenEmpty()
            throws Exception {
        // given
        String string = "";

        // when
        String actual = underTest.aes256Decrypt(string);

        // then
        assertNotNull(actual);
        assertEquals(string, actual);
    }

    @Test
    public void testAes256DecryptShouldReturnGivenStringWhenGivenNull() throws Exception {
        // given
        String string = null;

        // when
        String actual = underTest.aes256Decrypt(string);

        // then
        assertNull(actual);
    }
}
