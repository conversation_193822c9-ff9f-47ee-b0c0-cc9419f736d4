/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding shopee order.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @ToString
public class ShopeeOrder {

    private final String orderId;
    private final String shopType;
    private final List<ShopeeItem> items;
}
