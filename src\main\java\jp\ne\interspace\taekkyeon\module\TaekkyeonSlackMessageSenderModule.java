/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import javax.inject.Singleton;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.name.Named;

import static com.google.inject.name.Names.named;
import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.getPropertyBy;

/**
 * Taekkyeon module for Slack message sender.
 *
 * <AUTHOR>
 */
public class TaekkyeonSlackMessageSenderModule extends AbstractModule {

    public static final String BIND_KEY_SLACK_URL = "monitoring.slack.url";
    public static final String BIND_KEY_SHOULD_SEND_NOTIFICATION_TO_SLACK = "should.send.notification.to.slack";
    public static final String BIND_KEY_AUTHORIZATION_TOKEN = "monitoring.authorization.token";

    private static final String VM_ARGUMENT_SLACK_URL = "monitoringSlackUrl";
    private static final String VM_ARGUMENT_SHOULD_SEND_NOTIFICATION_TO_SLACK = "shouldSendNotificationToSlack";

    private static final String AUTHORIZATION_TOKEN = getProperty(
            "authorizationToken", "Basic c3lzdGVtX21vbml0b3Jpbmc6M2Y4cWhXSHI2dW1ZSlhQ");

    @Override
    protected void configure() {
        install(new TaekkyeonHttpClientModule());

        bindConstant().annotatedWith(named(BIND_KEY_AUTHORIZATION_TOKEN))
                .to(AUTHORIZATION_TOKEN);
    }

    @Provides @Singleton @Named(BIND_KEY_SLACK_URL)
    private String provideSlackUrl() {
        return getPropertyBy(VM_ARGUMENT_SLACK_URL);
    }

    @Provides @Singleton @Named(BIND_KEY_SHOULD_SEND_NOTIFICATION_TO_SLACK)
    private boolean provideShouldSendNotificationToSlack() {
        return Boolean.valueOf(getProperty(VM_ARGUMENT_SHOULD_SEND_NOTIFICATION_TO_SLACK, "true"));
    }
}
