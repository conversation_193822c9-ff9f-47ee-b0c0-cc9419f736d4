/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.util.Set;

import javax.inject.Singleton;

import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.name.Named;

import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.getPropertyBy;

/**
 * Common Guice module for click log optimizer batch.
 *
 * <AUTHOR> Tran
 */
public class ClickLogOptimizerPropertiesModule extends AbstractModule {

    public static final String BIND_KEY_TARGET_COUNTRY_CODE = "target.country.code";
    public static final String BIND_KEY_TARGET_SITE_IDS = "target.site.ids";
    public static final String BIND_KEY_EXCLUDED_SITE_IDS = "excluded.site.ids";
    public static final String BIND_KEY_TARGET_CAMPAIGN_IDS = "target.campaign.ids";
    public static final String BIND_KEY_EXCLUDED_CAMPAIGN_IDS = "excluded.campaign.ids";

    private static final String TARGET_COUNTRY_CODE_VM_ARGUMENT = "targetCountryCode";
    private static final String TARGET_SITE_IDS_VM_ARGUMENT = "targetSiteIds";
    private static final String EXCLUDED_SITE_IDS_VM_ARGUMENT = "excludedSiteIds";
    private static final String TARGET_CAMPAIGN_IDS_VM_ARGUMENT = "targetCampaignIds";
    private static final String EXCLUDED_CAMPAIGN_IDS_VM_ARGUMENT = "excludedCampaignIds";

    @Override
    protected void configure() {
        // do nothing
    }

    @Provides @Singleton @Named(BIND_KEY_TARGET_COUNTRY_CODE)
    private String getTargetCountryCode() {
        return getPropertyBy(TARGET_COUNTRY_CODE_VM_ARGUMENT);
    }

    @Provides @Singleton @Named(BIND_KEY_TARGET_SITE_IDS)
    private Set<Long> getTargetSiteIds() {
        return getTargetIdsFrom(getProperty(TARGET_SITE_IDS_VM_ARGUMENT));
    }

    @Provides @Singleton @Named(BIND_KEY_EXCLUDED_SITE_IDS)
    private Set<Long> getExcludedSiteIds() {
        return getTargetIdsFrom(getProperty(EXCLUDED_SITE_IDS_VM_ARGUMENT));
    }

    @Provides @Singleton @Named(BIND_KEY_TARGET_CAMPAIGN_IDS)
    private Set<Long> getTargetCampaignIds() {
        return getTargetIdsFrom(getProperty(TARGET_CAMPAIGN_IDS_VM_ARGUMENT));
    }

    @Provides @Singleton @Named(BIND_KEY_EXCLUDED_CAMPAIGN_IDS)
    private Set<Long> getExcludedCampaignIds() {
        return getTargetIdsFrom(getProperty(EXCLUDED_CAMPAIGN_IDS_VM_ARGUMENT));
    }

    private Set<Long> getTargetIdsFrom(String ids) {
        Set<Long> convertedIds = Sets.newHashSet();
        if (!Strings.isNullOrEmpty(ids)) {
            String[] splittedIds = ids.split(",");
            for (String id : splittedIds) {
                convertedIds.add(Long.valueOf(id));
            }
        }
        return convertedIds;
    }
}
