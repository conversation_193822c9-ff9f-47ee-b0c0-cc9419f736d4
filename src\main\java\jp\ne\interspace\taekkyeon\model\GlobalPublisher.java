/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for checking publisher is global publisher or not.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum GlobalPublisher implements ValueEnum {

    NO(0), YES(1);

    private final int value;
}

