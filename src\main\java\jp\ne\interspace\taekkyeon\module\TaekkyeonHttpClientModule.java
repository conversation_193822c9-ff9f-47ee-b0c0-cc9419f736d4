/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import com.google.inject.AbstractModule;

import jp.ne.interspace.taekkyeon.util.TaekkyeonHttpClient;

import static com.google.inject.Scopes.SINGLETON;
import static com.google.inject.name.Names.named;
import static java.lang.Integer.parseInt;
import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonHttpClientProvider.BIND_KEY_HTTP_CONNECTION_TIMEOUT;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonHttpClientProvider.BIND_KEY_HTTP_MAX_TOTAL_CONNECTIONS;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonHttpClientProvider.BIND_KEY_HTTP_SOCKET_TIMEOUT;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonHttpClientProvider.BIND_KEY_REDIRECTS_ENABLED;

/**
 * Taekkyeon module for http client.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonHttpClientModule extends AbstractModule {

    private static final int MAX_TOTAL_CONNECTION = 100;
    private static final String CONNECTION_TIMEOUT_IN_MILLIS = "30000";
    private static final String SOCKET_TIMEOUT_IN_MILLIS = "60000";

    private static final String REDIRECT_ENABLED_VM_ARGUMENT = "redirectsEnabled";
    private static final String SOCKET_TIMEOUT_VM_ARGUMENT = "socketTimeout";
    private static final String CONNECTION_TIMEOUT_VM_ARGUMENT = "connectionTimeout";

    @Override
    protected void configure() {

        bindTaekkyeonHttpClientProperties();

        bind(TaekkyeonHttpClient.class).toProvider(TaekkyeonHttpClientProvider.class)
                .in(SINGLETON);
    }

    private void bindTaekkyeonHttpClientProperties() {
        bindConstant().annotatedWith(named(BIND_KEY_HTTP_MAX_TOTAL_CONNECTIONS))
                .to(MAX_TOTAL_CONNECTION);
        bindConstant().annotatedWith(named(BIND_KEY_HTTP_CONNECTION_TIMEOUT))
                .to(provideConnectionTimeout());
        bindConstant().annotatedWith(named(BIND_KEY_HTTP_SOCKET_TIMEOUT))
                .to(provideSocketTimeout());
        bindConstant().annotatedWith(named(BIND_KEY_REDIRECTS_ENABLED))
                .to(provideRedirectsEnabled());

    }

    private boolean provideRedirectsEnabled() {
        return Boolean.getBoolean(REDIRECT_ENABLED_VM_ARGUMENT);
    }

    private int provideSocketTimeout() {
        return parseInt(
                getProperty(SOCKET_TIMEOUT_VM_ARGUMENT, SOCKET_TIMEOUT_IN_MILLIS));
    }

    private int provideConnectionTimeout() {
        return parseInt(getProperty(CONNECTION_TIMEOUT_VM_ARGUMENT,
                CONNECTION_TIMEOUT_IN_MILLIS));
    }
}
