/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding campaign closure payment generation status.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum PaymentGenerationStatus implements ValueEnum {

    NOT_GENERATED(0),
    IS_PROCESSING(2),
    GENERATED(1);

    private final int value;
}
