/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Product CSV column indexes.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public class ProductCsvColumnIndexes {

    private final int idIndex;
    private final int nameIndex;
    private final int imageUrlIndex;
    private final int landingUrlIndex;
    private final int descriptionIndex;
    private final int priceIndex;
    private final int discountedPriceIndex;
    private final int categoryIdIndex;
}
