/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */

/**
 * The MIT License
 *
 *   Copyright (c) 2017, <PERSON><PERSON><PERSON> (<EMAIL>)
 *
 *   Permission is hereby granted, free of charge, to any person obtaining a copy
 *   of this software and associated documentation files (the "Software"), to deal
 *   in the Software without restriction, including without limitation the rights
 *   to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *   copies of the Software, and to permit persons to whom the Software is
 *   furnished to do so, subject to the following conditions:
 *
 *   The above copyright notice and this permission notice shall be included in
 *   all copies or substantial portions of the Software.
 *
 *   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *   OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 *   THE SOFTWARE.
 */

package org.easybatch.core.job;

import lombok.extern.log4j.Log4j;

import org.apache.ibatis.session.SqlSessionManager;
import org.apache.ibatis.session.TransactionIsolationLevel;
import org.apache.log4j.AppenderSkeleton;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;

import org.easybatch.core.listener.BatchListener;
import org.easybatch.core.listener.CompositeBatchListener;
import org.easybatch.core.listener.CompositeJobListener;
import org.easybatch.core.listener.CompositePipelineListener;
import org.easybatch.core.listener.CompositeRecordReaderListener;
import org.easybatch.core.listener.CompositeRecordWriterListener;
import org.easybatch.core.listener.JobListener;
import org.easybatch.core.listener.PipelineListener;
import org.easybatch.core.listener.RecordReaderListener;
import org.easybatch.core.listener.RecordWriterListener;
import org.easybatch.core.processor.CompositeRecordProcessor;
import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.Batch;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DEBUG_MODE;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.IS_JENKINS_LOG_ENABLED;
import static org.easybatch.core.job.JobStatus.COMPLETED;
import static org.easybatch.core.job.JobStatus.FAILED;
import static org.easybatch.core.job.JobStatus.STARTED;
import static org.easybatch.core.job.JobStatus.STARTING;
import static org.easybatch.core.job.JobStatus.STOPPING;
import static org.easybatch.core.util.Utils.formatErrorThreshold;

/**
 * Implementation of read-process-write job pattern.
 * <p>
 * Added a {@code Log4j} logger to hook JUL logs.
 * </p>
 * <AUTHOR> Ben Hassine (<EMAIL>)
 * <AUTHOR> OBS DEV Team
 */
@Log4j
class BatchJob implements Job {

    private static final String DEFAULT_JOB_NAME = "job";

    private String name;

    private RecordReader recordReader;
    private RecordWriter recordWriter;
    @SuppressWarnings("rawtypes")
    private RecordProcessor recordProcessor;
    private RecordTracker recordTracker;

    private JobListener jobListener;
    private BatchListener batchListener;
    private RecordReaderListener recordReaderListener;
    private RecordWriterListener recordWriterListener;
    private PipelineListener pipelineListener;

    private JobParameters parameters;
    private JobMetrics metrics;
    private JobReport report;
    private JobMonitor monitor;
    private SqlSessionManager sqlSessionManager;

    /**
     * Creates a {@link BatchJob} instance with the given {@link JobParameters}.
     *
     * @param parameters {@link JobParameters} parameters to show on the report
     * @param sqlSessionManager {@link SqlSessionManager} session manager to manage transactions.
     */
    BatchJob(JobParameters parameters, SqlSessionManager sqlSessionManager) {
        this.parameters = parameters;
        this.name = DEFAULT_JOB_NAME;
        metrics = new JobMetrics();
        report = new JobReport();
        report.setParameters(parameters);
        report.setMetrics(metrics);
        report.setJobName(name);
        monitor = new JobMonitor(report);
        recordReader = new NoOpRecordReader();
        recordProcessor = new CompositeRecordProcessor();
        recordWriter = new NoOpRecordWriter();
        recordReaderListener = new CompositeRecordReaderListener();
        pipelineListener = new CompositePipelineListener();
        recordWriterListener = new CompositeRecordWriterListener();
        batchListener = new CompositeBatchListener();
        jobListener = new CompositeJobListener();
        recordTracker = new RecordTracker();
        this.sqlSessionManager = sqlSessionManager;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public JobReport call() {
        start();
        try {
            if (sqlSessionManager != null) {
                sqlSessionManager.openSession();
                sqlSessionManager
                        .startManagedSession(TransactionIsolationLevel.READ_COMMITTED);
            }
            openReader();
            openWriter();
            setStatus(STARTED);
            while (moreRecords()) {
                Batch batch = readAndProcessBatch();
                writeBatch(batch);
            }
            setStatus(STOPPING);
        } catch (Throwable throwable) {
            fail(throwable);
            return report;
        } finally {
            closeReader();
            closeWriter();
            if (sqlSessionManager != null) {
                if (report.getLastError() != null) {
                    sqlSessionManager.rollback();
                } else {
                    sqlSessionManager.commit();
                }
                sqlSessionManager.close();
            }
        }

        if (report.getLastError() != null) {
            log.info("Error occurred in close reader or close writer, "
                    + "transaction has been rolled back");
            fail(new Exception(report.getLastError()));
        } else {
            complete();
        }

        return report;
    }

    /*
     * private methods
     */

    private void start() {
        setLogLevel();
        setStatus(STARTING);
        jobListener.beforeJobStart(parameters);
        recordTracker = new RecordTracker();
        metrics.setStartTime(System.currentTimeMillis());
        log.info("Batch size: " + parameters.getBatchSize());
        log.info("Error threshold: " + formatErrorThreshold(parameters.getErrorThreshold()));
        log.info("Jmx monitoring: " + parameters.isJmxMonitoring());
        registerJobMonitor();
    }

    private void registerJobMonitor() {
        if (parameters.isJmxMonitoring()) {
            monitor.registerJmxMBeanFor(this);
        }
    }

    private void openReader() throws RecordReaderOpeningException {
        try {
            log.debug("Opening record reader");
            recordReader.open();
        } catch (Exception e) {
            log.error("Unable to open record reader", e);
            throw new RecordReaderOpeningException("Unable to open record reader", e);
        }
    }

    private void openWriter() throws RecordWriterOpeningException {
        try {
            log.debug("Opening record writer");
            recordWriter.open();
        } catch (Exception e) {
            log.error("Unable to open record writer", e);
            throw new RecordWriterOpeningException("Unable to open record writer", e);
        }
    }

    private void setStatus(JobStatus status) {
        log.debug("Job '" + name + "' " + status.name().toLowerCase());
        report.setStatus(status);
    }

    private boolean moreRecords() {
        return recordTracker.moreRecords();
    }

    @SuppressWarnings("rawtypes")
    private Batch readAndProcessBatch()
            throws RecordReadingException, ErrorThresholdExceededException {
        Batch batch = new Batch();
        batchListener.beforeBatchReading();
        for (int i = 0; i < parameters.getBatchSize(); i++) {
            Record record = readRecord();
            if (record == null) {
                recordTracker.noMoreRecords();
                break;
            } else {
                metrics.incrementReadCount();
            }
            processRecord(record, batch);
        }
        batchListener.afterBatchProcessing(batch);
        return batch;
    }

    @SuppressWarnings("rawtypes")
    private Record readRecord() throws RecordReadingException {
        Record record;
        try {
            recordReaderListener.beforeRecordReading();
            record = recordReader.readRecord();
            recordReaderListener.afterRecordReading(record);
            return record;
        } catch (Exception e) {
            recordReaderListener.onRecordReadingException(e);
            log.error("Unable to read next record", e);
            throw new RecordReadingException("Unable to read next record", e);
        }
    }

    @SuppressWarnings(value = { "unchecked", "rawtypes" })
    private void processRecord(Record record, Batch batch)
            throws ErrorThresholdExceededException {
        Record processedRecord;
        try {
            if (DEBUG_MODE) {
                log.debug("Processing " + record);
            }
            notifyJobUpdate();
            pipelineListener.beforeRecordProcessing(record);
            processedRecord = recordProcessor.processRecord(record);
            pipelineListener.afterRecordProcessing(record, processedRecord);
            if (processedRecord == null) {
                log.debug(record + " has been filtered");
                metrics.incrementFilteredCount();
            } else {
                batch.addRecord(processedRecord);
            }
        } catch (Exception e) {
            log.error("Unable to process " + record, e);
            pipelineListener.onRecordProcessingException(record, e);
            metrics.incrementErrorCount();
            report.setLastError(e);
            if (metrics.getErrorCount() > parameters.getErrorThreshold()) {
                log.error("Error threshold exceeded. Aborting execution", e);
                throw new ErrorThresholdExceededException(
                        "Error threshold exceeded. Aborting execution", e);
            }
        }
    }

    private void writeBatch(Batch batch) throws BatchWritingException {
        if (DEBUG_MODE) {
            log.debug("Writing " + batch);
        }
        try {
            if (!batch.isEmpty()) {
                recordWriterListener.beforeRecordWriting(batch);
                recordWriter.writeRecords(batch);
                recordWriterListener.afterRecordWriting(batch);
                batchListener.afterBatchWriting(batch);
                metrics.incrementWriteCount(batch.size());
            }
        } catch (Exception e) {
            recordWriterListener.onRecordWritingException(batch, e);
            batchListener.onBatchWritingException(batch, e);
            log.error("Unable to write records", e);
            throw new BatchWritingException("Unable to write records", e);
        }
    }

    private void teardown(JobStatus status) {
        report.setStatus(status);
        metrics.setEndTime(System.currentTimeMillis());
        log.info("Job '" + name + "' finished with status: " + report.getStatus());
        notifyJobUpdate();
        jobListener.afterJobEnd(report);
    }

    private void complete() {
        teardown(COMPLETED);
    }

    private void fail(Throwable throwable) {
        log.error("Something went wrong", throwable);
        report.setLastError(throwable);
        teardown(FAILED);
    }

    private void closeReader() {
        try {
            log.debug("Closing record reader");
            recordReader.close();
        } catch (Exception e) {
            log.warn("Unable to close record reader", e);
            report.setLastError(e);
        }
    }

    private void closeWriter() {
        try {
            log.debug("Closing record writer");
            recordWriter.close();
        } catch (Exception e) {
            log.warn("Unable to close record writer", e);
            report.setLastError(e);
        }
    }

    private void notifyJobUpdate() {
        if (parameters.isJmxMonitoring()) {
            monitor.notifyJobReportUpdate();
        }
    }

    /*
     * Setters for job components
     */

    public void setRecordReader(RecordReader recordReader) {
        this.recordReader = recordReader;
    }

    public void setRecordWriter(RecordWriter recordWriter) {
        this.recordWriter = recordWriter;
    }

    @SuppressWarnings("rawtypes")
    public void addRecordProcessor(RecordProcessor recordProcessor) {
        ((CompositeRecordProcessor) this.recordProcessor)
                .addRecordProcessor(recordProcessor);
    }

    public void addBatchListener(BatchListener batchListener) {
        ((CompositeBatchListener) this.batchListener).addBatchListener(batchListener);
    }

    public void addJobListener(JobListener jobListener) {
        ((CompositeJobListener) this.jobListener).addJobListener(jobListener);
    }

    public void addRecordReaderListener(RecordReaderListener recordReaderListener) {
        ((CompositeRecordReaderListener) this.recordReaderListener)
                .addRecordReaderListener(recordReaderListener);
    }

    public void addRecordWriterListener(RecordWriterListener recordWriterListener) {
        ((CompositeRecordWriterListener) this.recordWriterListener)
                .addRecordWriterListener(recordWriterListener);
    }

    public void addPipelineListener(PipelineListener pipelineListener) {
        ((CompositePipelineListener) this.pipelineListener)
                .addPipelineListener(pipelineListener);
    }

    public void setName(String name) {
        this.name = name;
        this.report.setJobName(name);
    }

    private void setLogLevel() {
        if (IS_JENKINS_LOG_ENABLED) {
            ((AppenderSkeleton) Logger.getRootLogger().getAppender("stdout"))
                    .setThreshold(Level.DEBUG);
        }
    }
}
