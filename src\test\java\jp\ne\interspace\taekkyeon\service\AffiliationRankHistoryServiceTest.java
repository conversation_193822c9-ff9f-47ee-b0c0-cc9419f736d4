/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.AffiliationRankHistoryMapper;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link AffiliationRankHistoryService}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class AffiliationRankHistoryServiceTest {

    @InjectMocks
    private AffiliationRankHistoryService underTest;

    @Mock
    private AffiliationRankHistoryMapper affiliationMapper;

    @Test
    public void testGetRankByShouldReturnCorrectRankWhenCalled() {
        // given
        long campaignId = 3;
        long siteId = 2;
        LocalDateTime conversionDate = LocalDateTime.of(2017, 10, 1, 0, 0, 0);
        Integer expected = 5;
        when(affiliationMapper.findRankBy(campaignId, siteId, conversionDate))
                .thenReturn(expected);

        // when
        Integer actual = underTest.getRankBy(campaignId, siteId, conversionDate);

        // then
        assertEquals(expected, actual);
    }
}
