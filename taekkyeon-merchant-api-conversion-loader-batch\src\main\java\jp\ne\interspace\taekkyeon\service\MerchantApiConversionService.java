/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.List;

import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionsWithClickIdDetails;

/**
 * Service layer for getting merchant API conversions.
 *
 * <AUTHOR>
 */
public abstract class MerchantApiConversionService {

    public abstract String getResponseBody() throws Exception;

    public abstract List<ConversionRegistrationDetails> parse(String responseBody)
            throws Exception;

    public abstract boolean hasNext();

    public abstract List<ConversionsWithClickIdDetails>
            createConversionsWithClickIdDetailsBy(
                List<ConversionRegistrationDetails> details);

    public abstract List<ConversionRegistrationDetails> getConversionData()
            throws Exception;

    public abstract boolean isConversionUnique(String key);

}
