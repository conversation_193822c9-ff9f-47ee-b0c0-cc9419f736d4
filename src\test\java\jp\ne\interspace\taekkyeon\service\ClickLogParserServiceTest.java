/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 * Unit test for {@link ClickLogParserService}.
 *
 * <AUTHOR>
 */
public class ClickLogParserServiceTest {

    private ClickLogParserService underTest = new ClickLogParserService();

    @Test
    public void testGetReferralDomainByShouldReturnUnknownWhenReferralUrlIsNull() {
        // given
        String referralUrl = null;
        String expected = "unknown";

        // when
        String actual = underTest.getReferralDomainBy(referralUrl);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetReferralDomainByShouldReturnUnknownWhenReferralUrlIsEmpty() {
        // given
        String referralUrl = "";
        String expected = "unknown";

        // when
        String actual = underTest.getReferralDomainBy(referralUrl);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetReferralDomainByShouldReturnUnknownWhenReferralUrlIsNotValid() {
        // given
        String referralUrl = "NotValid";
        String expected = "unknown";

        // when
        String actual = underTest.getReferralDomainBy(referralUrl);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetReferralDomainByShouldReturnUnknownWhenReferralUrlIsForFilePath() {
        // given
        String referralUrl = "file:///android_asset/homepage.html";
        String expected = "unknown";

        // when
        String actual = underTest.getReferralDomainBy(referralUrl);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetReferralDomainByShouldReturnCorrectHostWhenReferralUrlIsForWebSite() {
        // given
        String referralUrl = "https://www.google.com/test/path";
        String expected = "www.google.com";

        // when
        String actual = underTest.getReferralDomainBy(referralUrl);

        // then
        assertEquals(expected, actual);
    }
}
