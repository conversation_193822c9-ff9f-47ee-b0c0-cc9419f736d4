/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;

/**
 * Unit tests for {@link CreativeHelper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class CreativeHelperTest {

    @InjectMocks @Spy
    private CreativeHelper underTest;

    @Test
    public void testCreateRkShouldReturnCorrectRkWhenCalled() {
        // given
        long creativeId = 1111L;
        long siteId = 2222L;
        String expected = "0000uv0001pq";

        // when
        String actual = underTest.createRk(creativeId, siteId);

        // then
        assertEquals(expected, actual);
    }
}
