/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.CampaignSettingDuplicationCutDetails;
import jp.ne.interspace.taekkyeon.model.DuplicationCutTarget;

/**
 * Mybatis mapper for handling campaign settings.
 *
 * <AUTHOR>
 */
public interface CampaignSettingsMapper {

    String SELECT_DUPLICATION_CUT_DETAILS =
            "SELECT "
            + "    verify_cut_flag as enabled, "
            + "    verify_cut_target as target "
            + "FROM "
            + "    merchant_campaign_setting "
            + "WHERE "
            + "    campaign_no = #{campaignId} ";

    /**
     * Returns the duplication cut details by the given {@code campaignId}.
     *
     * @param campaignId
     *          the given campaign ID
     * @return the duplication cut details by the given {@code campaignId}
     * @see #SELECT_DUPLICATION_CUT_DETAILS
     */
    @Select(SELECT_DUPLICATION_CUT_DETAILS)
    @ConstructorArgs({ @Arg(column = "enabled", javaType = boolean.class),
            @Arg(column = "target", javaType = DuplicationCutTarget.class) })
    CampaignSettingDuplicationCutDetails findDuplicationCutDetailsBy(long campaignId);
}
