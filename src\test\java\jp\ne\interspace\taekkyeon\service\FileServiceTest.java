/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.IOException;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link FileService}.
 *
 * <AUTHOR> Van Nguyen
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class FileServiceTest {

    private static final String REPORT_TYPE = "campaign";
    private static final String COUNTRY_CODE = "ID";
    private static final String CREATION_DATE_STRING = "2020-02-17";
    private static final int RANDOM_STRING_LENGTH = 16;
    private static final String RANDOM_STRING = "randomString";
    private static final List<String> DATA = Arrays.asList("data1");
    private static final String FILE_NAME = "campaign-report-id-2020-02-17-randomString.csv";
    private static final Path PATH = mock(Path.class);

    @InjectMocks @Spy
    private FileService underTest;

    @Mock
    private StringHelper stringHelper;

    @Test
    public void testCreateFileNameShouldReturnCorrectNameWhenCalled() {
        // given
        when(stringHelper.generateRandomAlphanumericString(RANDOM_STRING_LENGTH))
                .thenReturn(RANDOM_STRING);

        // when
        String actual = underTest.createFileName(REPORT_TYPE, COUNTRY_CODE,
                CREATION_DATE_STRING);

        // then
        assertEquals(FILE_NAME, actual);
    }

    @Test
    public void testWriteInFileShouldCreateFileWhenFileNotExist() throws IOException {
        // given
        doReturn(PATH).when(underTest).getFilePath(FILE_NAME);
        doReturn(false).when(underTest).isFileExist(PATH);
        doNothing().when(underTest).createFile(PATH);
        doNothing().when(underTest).write(PATH, DATA);

        // when
        underTest.writeInFile(FILE_NAME, DATA);

        // then
        verify(underTest).getFilePath(FILE_NAME);
        verify(underTest).isFileExist(PATH);
        verify(underTest).createFile(PATH);
        verify(underTest).write(PATH, DATA);
    }

    @Test
    public void testWriteInFileShouldNotCreateFileWhenFileDoesExist() throws IOException {
        // given
        doReturn(PATH).when(underTest).getFilePath(FILE_NAME);
        doReturn(true).when(underTest).isFileExist(PATH);
        doNothing().when(underTest).createFile(PATH);
        doNothing().when(underTest).write(PATH, DATA);

        // when
        underTest.writeInFile(FILE_NAME, DATA);

        // then
        verify(underTest).getFilePath(FILE_NAME);
        verify(underTest).isFileExist(PATH);
        verify(underTest, never()).createFile(PATH);
        verify(underTest).write(PATH, DATA);
    }

}
