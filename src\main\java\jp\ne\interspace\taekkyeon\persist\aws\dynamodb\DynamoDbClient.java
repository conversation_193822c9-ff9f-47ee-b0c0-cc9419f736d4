/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.dynamodb;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import javax.inject.Singleton;

import com.amazonaws.AmazonWebServiceRequest;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClient;
import com.amazonaws.services.dynamodbv2.document.DynamoDB;
import com.amazonaws.services.dynamodbv2.document.Table;
import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.AttributeValueUpdate;
import com.amazonaws.services.dynamodbv2.model.BatchGetItemRequest;
import com.amazonaws.services.dynamodbv2.model.BatchGetItemResult;
import com.amazonaws.services.dynamodbv2.model.BatchWriteItemRequest;
import com.amazonaws.services.dynamodbv2.model.BatchWriteItemResult;
import com.amazonaws.services.dynamodbv2.model.Condition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.CreateTableResult;
import com.amazonaws.services.dynamodbv2.model.DeleteItemRequest;
import com.amazonaws.services.dynamodbv2.model.DeleteItemResult;
import com.amazonaws.services.dynamodbv2.model.DeleteTableRequest;
import com.amazonaws.services.dynamodbv2.model.DeleteTableResult;
import com.amazonaws.services.dynamodbv2.model.DescribeTableRequest;
import com.amazonaws.services.dynamodbv2.model.DescribeTableResult;
import com.amazonaws.services.dynamodbv2.model.GetItemRequest;
import com.amazonaws.services.dynamodbv2.model.GetItemResult;
import com.amazonaws.services.dynamodbv2.model.KeysAndAttributes;
import com.amazonaws.services.dynamodbv2.model.ListTablesRequest;
import com.amazonaws.services.dynamodbv2.model.ListTablesResult;
import com.amazonaws.services.dynamodbv2.model.PutItemRequest;
import com.amazonaws.services.dynamodbv2.model.PutItemResult;
import com.amazonaws.services.dynamodbv2.model.QueryRequest;
import com.amazonaws.services.dynamodbv2.model.QueryResult;
import com.amazonaws.services.dynamodbv2.model.ReturnValue;
import com.amazonaws.services.dynamodbv2.model.ScanRequest;
import com.amazonaws.services.dynamodbv2.model.ScanResult;
import com.amazonaws.services.dynamodbv2.model.UpdateItemRequest;
import com.amazonaws.services.dynamodbv2.model.UpdateItemResult;
import com.amazonaws.services.dynamodbv2.model.UpdateTableRequest;
import com.amazonaws.services.dynamodbv2.model.UpdateTableResult;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.util.concurrent.RateLimiter;

import static com.google.common.base.Preconditions.checkArgument;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.AWS_CREDENTIALS_CHAIN;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.DEFAULT_AWS_REGION;

/**
 * Low-level client for Amazon DynamoDB.
 *
 * <AUTHOR> OBS DEV Team
 * @see AmazonDynamoDB
 */
@Singleton
public class DynamoDbClient {

    @VisibleForTesting
    static final int MAX_RECOVERY_ATTEMPTS = 3;

    private AmazonDynamoDB dynamoDb;

    private DynamoDB dynamoDbDocument;

    /**
     * Constructs a new client to invoke service methods on DynamoDB using the specified
     * AWS account credentials provider.
     * <p>
     * All service calls made using this new client object are blocking, and will not
     * return until the service call completes.
     * </p>
     *
     * @see AmazonDynamoDB
     */
    public DynamoDbClient() {
        dynamoDb = createAmazonDynamoDb();
        dynamoDbDocument = new DynamoDB(dynamoDb);
    }

    private AmazonDynamoDB createAmazonDynamoDb() {
        return AmazonDynamoDBClient.builder().withCredentials(AWS_CREDENTIALS_CHAIN)
                .withRegion(DEFAULT_AWS_REGION).build();
    }

    private void checkRequest(AmazonWebServiceRequest request) {
        checkArgument(request != null, "Invalid DynamoDB API request");
    }

    @SafeVarargs
    private final void checkParameters(
            Map<String, ? extends Serializable>... parameters) {
        String message = "Invalid Map parameter(s) for DynamoDB API";
        checkArgument(parameters != null, message);
        for (Map<String, ? extends Serializable> parameter : parameters) {
            checkArgument(parameter != null && !parameter.isEmpty(), message);
        }
    }

    private void checkParameters(List<String> parameters) {
        checkArgument(parameters != null && !parameters.isEmpty(),
                "Invalid List parameter(s) for DynamoDB API");
    }

    private void checkParameters(String... parameters) {
        String message = "Invalid String parameter(s) for DynamoDB API";
        checkArgument(parameters != null, message);
        for (String parameter : parameters) {
            checkArgument(Strings.nullToEmpty(parameter).trim().length() > 0, message);
        }
    }

    /**
     * Waits for the given DynamoDB table to become avaliable to use by polling the table
     * every {@code 5} seconds. If polling crashes, reattempts will be made with the
     * interval specified with the given {@link RateLimiter}.
     *
     * @param tableName
     *            the name of the DynamoDB table to poll
     * @param interval
     *            a {@link RateLimiter} that specify the inverval, as in:
     *            <p>
     *            {@code RateLimiter.create(2.0); // rate is "2 permits per second"}
     *            </p>
     * @see RateLimiter
     */
    public void waitUntilAvailableFor(String tableName, RateLimiter interval) {
        checkParameters(tableName);
        checkArgument(interval != null, "RateLimiter cannot be null");
        Table table = dynamoDbDocument.getTable(tableName);
        int attempts = 0;
        while (attempts < MAX_RECOVERY_ATTEMPTS) {
            try {
                interval.acquire();
                table.waitForDelete();
                return;
            } catch (IllegalArgumentException ie) {
                // table exists and is not being deleted
                try {
                    table.waitForActive();
                    return;
                } catch (Exception ex) {
                    // let-through to finally
                }
            } catch (Exception ex) {
                // let-through to finally
            } finally {
                attempts++;
            }
        }
    }

    /**
     * Adds a new table to your account.
     *
     * @param request
     *            represents the input of a CreateTable operation
     * @return {@link CreateTableResult} as a result of the CreateTable operation
     * @see AmazonDynamoDB#createTable(CreateTableRequest)
     */
    public CreateTableResult createTable(CreateTableRequest request) {
        checkRequest(request);
        return dynamoDb.createTable(request);
    }

    /**
     * Returns an array of table names associated with the current account and endpoint.
     *
     * @return {@link ListTableResult} as a result of the ListTables operation
     * @see AmazonDynamoDB#listTables()
     */
    public ListTablesResult listTables() {
        return dynamoDb.listTables();
    }

    /**
     * Returns an array of table names associated with the current account and endpoint.
     *
     * @param request
     *            represents the input of a ListTables operation
     * @return {@link ListTableResult} as a result of the ListTables operation
     * @see AmazonDynamoDB#listTables(ListTablesRequest)
     */
    public ListTablesResult listTables(ListTablesRequest request) {
        checkRequest(request);
        return dynamoDb.listTables(request);
    }

    /**
     * Returns information about the table, including the current status of the table,
     * when it was created, the primary key schema, and any indexes on the table.
     *
     * @param tableName
     *            the name of the table to describe
     * @return {@link DescribeTableResult} as a result of the DescribeTable operation
     * @see AmazonDynamoDB#describeTable(String)
     */
    public DescribeTableResult describeTable(String tableName) {
        checkParameters(tableName);
        return dynamoDb.describeTable(tableName);
    }

    /**
     * Returns information about the table, including the current status of the table,
     * when it was created, the primary key schema, and any indexes on the table.
     *
     * @param request
     *            represents the input of a DescribeTable operation
     * @return {@link DescribeTableResult} as a result of the DescribeTable operation
     * @see AmazonDynamoDB#describeTable(DescribeTableRequest)
     */
    public DescribeTableResult describeTable(DescribeTableRequest request) {
        checkRequest(request);
        return dynamoDb.describeTable(request);
    }

    /**
     * Modifies the provisioned throughput settings, global secondary indexes, or DynamoDB
     * Streams settings for a given table.
     *
     * @param request
     *            represents the input of an UpdateTable operation
     * @return {@link UpdateTableResult} as a result of the UpdateTable operation
     * @see AmazonDynamoDB#updateTable(UpdateTableRequest)
     */
    public UpdateTableResult updateTable(UpdateTableRequest request) {
        checkRequest(request);
        return dynamoDb.updateTable(request);
    }

    /**
     * Deletes a table and all of its items.
     *
     * @param tableName
     *            the name of the table to delete
     * @return {@link DeleteTableResult} as a result of the DeleteTable operation
     * @see AmazonDynamoDB#deleteTable(String)
     */
    public DeleteTableResult deleteTable(String tableName) {
        checkParameters(tableName);
        return dynamoDb.deleteTable(tableName);
    }

    /**
     * Deletes a table and all of its items.
     *
     * @param request
     *            represents the input of a DeleteTable operation
     * @return {@link DeleteTableResult} as a result of the DeleteTable operation
     * @see AmazonDynamoDB#deleteTable(DeleteTableRequest)
     */
    public DeleteTableResult deleteTable(DeleteTableRequest request) {
        checkRequest(request);
        return dynamoDb.deleteTable(request);
    }

    /**
     * Returns one or more items and item attributes by accessing every item in a table or
     * a secondary index.
     *
     * @param tableName
     *            the name of the table containing the requested items; or if you provide
     *            IndexName, the name of the table to which that index belongs
     * @param filterExpression
     *            see {@link ScanRequest#setFilterExpression} and also <a href=
     *            "http://docs.aws.amazon.com/amazondynamodb/latest/developerguide/QueryAndScan.html#FilteringResults"
     *            >Filter Expressions</a>
     * @param projectionExpression
     *            see {@link ScanRequest#setProjectionExpression} and also <a href=
     *            "http://docs.aws.amazon.com/amazondynamodb/latest/developerguide/Expressions.AccessingItemAttributes.html"
     *            >Accessing Item Attributes</a>
     * @return {@link ScanResult} as a result of the Scan operation
     * @see AmazonDynamoDB#scan(ScanRequest)
     */
    public ScanResult scan(String tableName, String filterExpression,
            String projectionExpression) {
        checkParameters(tableName, filterExpression, projectionExpression);
        return scan(new ScanRequest(tableName).withFilterExpression(filterExpression)
                .withProjectionExpression(projectionExpression));
    }

    /**
     * Returns one or more items and item attributes by accessing every item in a table or
     * a secondary index.
     *
     * @deprecated this is an older version of DynamoDB Scan
     * @param tableName
     *            the name of the table containing the requested items; or if you provide
     *            IndexName, the name of the table to which that index belongs
     * @param scanFilter
     *            a condition that evaluates the scan results and returns only the desired
     *            values; see {@link ScanRequest#withScanFilter}
     * @param attributeNamesToGet
     *            the names of one or more attributes to retrieve; if no attribute names
     *            are provided, then all attributes will be returned; if any of the
     *            requested attributes are not found, they will not appear in the result;
     *            see {@link ScanRequest#withAttributesToGet}
     * @return {@link ScanResult} as a result of the Scan operation
     * @see AmazonDynamoDB#scan(ScanRequest)
     */
    @Deprecated
    public ScanResult scan(String tableName, Map<String, Condition> scanFilter,
            List<String> attributeNamesToGet) {
        checkParameters(tableName);
        checkParameters(scanFilter);
        checkParameters(attributeNamesToGet);
        return scan(new ScanRequest(tableName).withScanFilter(scanFilter)
                .withAttributesToGet(attributeNamesToGet));
    }

    /**
     * Returns one or more items and item attributes by accessing every item in a table or
     * a secondary index.
     *
     * @param request
     *            represents the input of a scan operation
     * @return {@link ScanResult} as a result of the Scan operation
     * @see AmazonDynamoDB#scan(ScanRequest)
     */
    public ScanResult scan(ScanRequest request) {
        checkRequest(request);
        return dynamoDb.scan(request);
    }

    /**
     * Puts or deletes multiple items in one or more tables.
     *
     * @param request
     *            represents the input of a BatchWriteItem operation
     * @return {@link BatchWriteItemResult} as a result of the BatchWriteItem operation
     *         returned by the service
     * @see AmazonDynamoDB#batchWriteItem(BatchWriteItemRequest)
     */
    public BatchWriteItemResult batchWriteItem(BatchWriteItemRequest request) {
        checkRequest(request);
        return dynamoDb.batchWriteItem(request);
    }

    /**
     * The BatchGetItem operation returns the attributes of one or more items from one or
     * more tables.
     *
     * @param items
     *            a map of one or more table names and, for each table, a map that
     *            describes one or more items to retrieve from that table; Each table name
     *            can be used only once per BatchGetItem request
     * @return {@link BatchGetItemResult} as a result of the BatchGetItem operation
     * @see AmazonDynamoDB#batchGetItem(BatchGetItemRequest)
     */
    public BatchGetItemResult batchGetItem(Map<String, KeysAndAttributes> items) {
        checkParameters(items);
        return dynamoDb.batchGetItem(items);
    }

    /**
     * The BatchGetItem operation returns the attributes of one or more items from one or
     * more tables.
     *
     * @param request
     *            represents the input of a BatchGetItem operation
     * @return {@link BatchGetItemResult} as a result of the BatchGetItem operation
     * @see AmazonDynamoDB#batchGetItem(BatchGetItemRequest)
     */
    public BatchGetItemResult batchGetItem(BatchGetItemRequest request) {
        checkRequest(request);
        return dynamoDb.batchGetItem(request);
    }

    /**
     * Creates a new item, or replaces an old item with a new item.
     *
     * @param tableName
     *            the name of the table to contain the item
     * @param items
     *            a map of attribute name/value pairs, one for each attribute; see
     *            {@link PutItemRequest#withItem} and also <a href=
     *            "http://docs.aws.amazon.com/amazondynamodb/latest/developerguide/DataModel.html#DataModelPrimaryKey"
     *            >Primary Key</a>
     * @param conditionExpression
     *            a condition that must be satisfied in order for a conditional PutItem
     *            operation to succeed; see {@link PutItemRequest#setConditionExpression}
     * @param doReturnOldValues
     *            if set {@code true}, when PutItem overwrote an attribute name-value
     *            pair, then the content of the old item is returned; see
     *            {@link PutItemRequest#setReturnValues}
     * @return {@link PutItemResult} as a result of the PutItem operation
     * @see AmazonDynamoDB#putItem(PutItemRequest)
     */
    public PutItemResult putItem(String tableName, Map<String, AttributeValue> items,
            String conditionExpression, boolean doReturnOldValues) {
        checkParameters(tableName);
        checkParameters(items);
        checkParameters(conditionExpression);
        PutItemRequest request = new PutItemRequest(tableName, items)
                .withConditionExpression(conditionExpression);
        if (doReturnOldValues) {
            request.setReturnValues(ReturnValue.ALL_OLD);
        }
        return putItem(request);
    }

    /**
     * Creates a new item, or replaces an old item with a new item.
     *
     * @param tableName
     *            the name of the table to contain the item
     * @param items
     *            a map of attribute name/value pairs, one for each attribute; see
     *            {@link PutItemRequest#withItem} and also <a href=
     *            "http://docs.aws.amazon.com/amazondynamodb/latest/developerguide/DataModel.html#DataModelPrimaryKey"
     *            >Primary Key</a>
     * @return {@link PutItemResult} as a result of the PutItem operation
     * @see AmazonDynamoDB#putItem(PutItemRequest)
     */
    public PutItemResult putItem(String tableName, Map<String, AttributeValue> items) {
        checkParameters(tableName);
        checkParameters(items);
        return putItem(new PutItemRequest(tableName, items));
    }

    /**
     * Creates a new item, or replaces an old item with a new item.
     * <p>
     * You can perform a conditional put operation (add a new item if one with the
     * specified primary key doesn't exist), or replace an existing item if it has certain
     * attribute values.
     * </p>
     *
     * @param request
     *            represents the input of a PutItem operation
     * @return {@link PutItemResult} as a result of the PutItem operation
     * @see AmazonDynamoDB#putItem(PutItemRequest)
     */
    public PutItemResult putItem(PutItemRequest request) {
        checkRequest(request);
        return dynamoDb.putItem(request);
    }

    /**
     * Returns a set of attributes for the item with the given primary key.
     *
     * @param tableName
     *            the name of the table to contain the item
     * @param key
     *            a map of attribute names to AttributeValue objects, representing the
     *            primary key of the item to retrieve
     * @return {@link GetItemResult} as a result of the GetItem operation
     * @see AmazonDynamoDB#getItem(GetItemRequest)
     */
    public GetItemResult getItem(String tableName, Map<String, AttributeValue> key) {
        checkParameters(tableName);
        checkParameters(key);
        return getItem(new GetItemRequest(tableName, key));
    }

    /**
     * Returns a set of attributes for the item with the given primary key.
     *
     * @param request
     *            result of the GetItem operation returned by the service
     * @return {@link GetItemResult} as a result of the GetItem operation
     * @see AmazonDynamoDB#getItem(GetItemRequest)
     */
    public GetItemResult getItem(GetItemRequest request) {
        checkRequest(request);
        return dynamoDb.getItem(request);
    }

    /**
     * Edits an existing item's attributes, or adds a new item to the table if it does not
     * already exist.
     *
     * @deprecated this is an older version of DynamoDB UpdateItem
     * @param tableName
     *            the name of the table containing the item to update
     * @param key
     *            the primary key of the item to be updated; each element consists of an
     *            attribute name and a value for that attribute; see
     *            {@link UpdateItemRequest#setKey}
     * @param updates
     *            the names of attributes to be modified, the action to perform on each,
     *            and the new value for each; see
     *            {@link UpdateItemRequest#setAttributeUpdates}
     * @param returnValues
     *            use ReturnValues if you want to get the item attributes as they appeared
     *            either before or after they were update; see
     *            {@link UpdateItemRequest#setReturnValues}
     * @return {@link UpdateItemResult} as a result of the UpdateItem operation
     * @see AmazonDynamoDB#updateItem(UpdateItemRequest)
     */
    @Deprecated
    public UpdateItemResult updateItem(String tableName, Map<String, AttributeValue> key,
            Map<String, AttributeValueUpdate> updates, ReturnValue returnValues) {
        checkParameters(tableName);
        checkParameters(key, updates);
        checkArgument(returnValues != null,
                "Invalid ReturnValue parameter for DynamoDB API");
        return updateItem(new UpdateItemRequest(tableName, key, updates, returnValues));
    }

    /**
     * Edits an existing item's attributes, or adds a new item to the table if it does not
     * already exist.
     *
     * @param request
     *            represents the input of an UpdateItem operation
     * @return {@link UpdateItemResult} as a result of the UpdateItem operation
     * @see AmazonDynamoDB#updateItem(UpdateItemRequest)
     */
    public UpdateItemResult updateItem(UpdateItemRequest request) {
        checkRequest(request);
        return dynamoDb.updateItem(request);
    }

    /**
     * Deletes a single item in a table by primary key.
     *
     * @param tableName
     *            the name of the table from which to delete the item
     * @param key
     *            a map of attribute names to AttributeValue objects, representing the
     *            primary key of the item to delete
     * @return {@link DeleteItemResult} as a result of the DeleteItem operation
     * @see AmazonDynamoDB#deleteItem(DeleteItemRequest)
     */
    public DeleteItemResult deleteItem(String tableName,
            Map<String, AttributeValue> key) {
        checkParameters(tableName);
        checkParameters(key);
        return deleteItem(new DeleteItemRequest(tableName, key));
    }

    /**
     * Deletes a single item in a table by primary key.
     *
     * @param request
     *            represents the input of a DeleteItem operation
     * @return {@link DeleteItemResult} as a result of the DeleteItem operation
     * @see AmazonDynamoDB#deleteItem(DeleteItemRequest)
     */
    public DeleteItemResult deleteItem(DeleteItemRequest request) {
        checkRequest(request);
        return dynamoDb.deleteItem(request);
    }

    /**
     * Queries item by query request.
     *
     * @param request
     *          the given query request
     * @return {@link QueryResult} as a result of query operation
     */
    public QueryResult queryItem(QueryRequest request) {
        checkRequest(request);
        return dynamoDb.query(request);
    }
}
