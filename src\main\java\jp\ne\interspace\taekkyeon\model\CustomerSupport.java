/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the details of a customer support.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class CustomerSupport {
    public static final CustomerSupport DEFAULT_CUSTOMER_SUPPORT = new CustomerSupport("", "");

    private final String emailSender;
    private final String accessTradeUrl;
}
