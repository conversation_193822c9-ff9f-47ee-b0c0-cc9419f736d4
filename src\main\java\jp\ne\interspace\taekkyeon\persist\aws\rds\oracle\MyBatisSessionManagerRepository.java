/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle;

import javax.inject.Singleton;

import com.google.inject.Inject;
import com.google.inject.Injector;

import org.apache.ibatis.session.SqlSessionManager;

import jp.ne.interspace.taekkyeon.module.Country;

import static com.google.inject.Key.get;

/**
 * Repository providing SQL session managers.
 *
 * <AUTHOR> <PERSON>
 */
@Singleton
public class MyBatisSessionManagerRepository {

    @Inject
    private Injector injector;

    /**
     * Returns {@link SqlSessionManager} corresponding to the given {@link Country}.
     *
     * @param country
     *            {@link Country} where the datasource resides
     * @return a singleton instance of {@link SqlSessionManager}
     */
    public SqlSessionManager getSessionManagerOf(Country country) {
        return injector
                .getInstance(get(SqlSessionManager.class, country.getAnnotationClass()));
    }
}
