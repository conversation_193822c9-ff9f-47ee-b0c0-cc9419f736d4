/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.junit;

import java.lang.annotation.Annotation;
import java.util.LinkedList;
import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.google.inject.Module;

import lombok.NonNull;

import org.junit.runners.BlockJUnit4ClassRunner;
import org.junit.runners.model.InitializationError;

/**
 * Taekkyeon module injector for JUnit test classes.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonJunitRunner extends BlockJUnit4ClassRunner {

    @VisibleForTesting
    protected Injector injector;

    public TaekkyeonJunitRunner(Class<?> clazz) throws InitializationError {
        super(clazz);
        injector = createInjector(extractTaekkyeonModules(clazz));
    }

    @Override
    public Object createTest() throws Exception {
        Object testClazz = super.createTest();
        injector.injectMembers(testClazz);
        return testClazz;
    }

    @VisibleForTesting
    protected List<Annotation> getAnnotations(Class<?> testClass)
            throws InitializationError {
        List<Annotation> annotations = new LinkedList<>();
        Class<?>[] classes = extractTaekkyeonModules(testClass);
        for (Class<?> clazz : classes) {
            for (Annotation annotation : clazz.getAnnotations()) {
                annotations.add(annotation);
            }
        }
        return annotations;
    }

    private Class<?>[] extractTaekkyeonModules(@NonNull Class<?> testClazz)
            throws InitializationError {

        TaekkyeonModules annotation = testClazz.getAnnotation(TaekkyeonModules.class);
        if (annotation == null) {
            throw new InitializationError(
                    "no module annotation found for " + testClazz.getName());
        }

        return annotation.value();
    }

    private Injector createInjector(@NonNull Class<?>[] clazzez)
            throws InitializationError {

        // immediately return the injector if exists
        if (injector != null) {
            return injector;
        }

        Module[] modules = new Module[clazzez.length];
        for (int i = 0; i < clazzez.length; i++) {
            try {
                modules[i] = (Module) clazzez[i].newInstance();
            } catch (InstantiationException | IllegalAccessException e) {
                throw new InitializationError(e);
            }
        }

        return Guice.createInjector(modules);
    }

    protected Injector getInjector() {
        return injector;
    }
}
