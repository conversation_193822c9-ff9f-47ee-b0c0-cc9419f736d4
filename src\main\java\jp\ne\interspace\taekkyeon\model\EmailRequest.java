/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the data of email request.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class EmailRequest {

    private final long userId;
    private final EmailType emailType;
    private final String details;
}
