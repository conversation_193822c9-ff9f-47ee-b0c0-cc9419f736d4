/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon;

import com.google.inject.Inject;

import org.easybatch.core.record.Batch;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.TransactionTestMapper;

/**
 * Writer for transaction integration test.
 *
 * <AUTHOR> VAN NGUYEN
 */
public class TransactionTestRollbackWriter implements RecordWriter {

    @Inject
    private TransactionTestMapper transactionTestMapper;

    @Override
    public void open() throws Exception {
        // do nothing.
    }

    @Override
    public void writeRecords(Batch batch) throws Exception {
        transactionTestMapper.insertFirstValidData();
        transactionTestMapper.insertInvalidData();
        transactionTestMapper.insertSecondValidData();
    }

    @Override
    public void close() throws Exception {
        // do nothing.
    }
}
