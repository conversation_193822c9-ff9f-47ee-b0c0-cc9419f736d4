/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.gson.JsonObject;

import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;

import static java.math.BigDecimal.ROUND_HALF_UP;
import static java.math.BigDecimal.ZERO;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER_YYYYMMDD;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.PIPE;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SPACE;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.UNDERSCORE;

/**
 * Implements {@link AbstractShopeeService} for thailand shopee.
 *
 * <AUTHOR> Van
 */
public class ThailandShopeeService extends AbstractShopeeService {

    private static final int PRODUCT_RESULT_ID = 3;
    private static final int CATEGORY_RESULT_ID = 30;
    private static final ImmutableMap<Double, String> CATEGORY_BY_TOTAL_COMMISSION =
            new ImmutableMap.Builder<Double, String>()
                    .put(0.0, "Existing0").put(2.3, "cate23")
                    .put(3.0, "cate30").put(4.5, "cate45").put(5.5, "cate55")
                    .put(6.5, "cate65").put(7.0, "cate70").put(8.0, "cate80")
                    .put(9.5, "cate95").put(10.0, "cate100").put(10.5, "cate105")
                    .put(12.0, "cate120").put(14.0, "cate140").put(14.5, "cate145")
                    .put(15.0, "cate150").put(16.0, "cate160").build();
    private static final String REQUEST_FIELD_SEPARATOR = "&";
    private static final String CATEGORY_ID_BRAND_COMMISSION = "Brand Commission";
    private static final String XTRA_COMM = "-XTRAComm";
    private static final String BONUS_CATEGORY = "Bonus";

    @Override
    public List<ConversionRegistrationDetails> parse(String responseBody)
            throws Exception {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        if (!Strings.isNullOrEmpty(responseBody)) {
            JsonObject obj = getJsonParser().parse(responseBody).getAsJsonObject();
            ShopeeConversionReport conversionReport = parseConversionReport(obj);
            ZoneId zoneId = getZoneId();
            for (ShopeeNode shopeeNode : conversionReport.getNodes()) {
                String[] utmContents = shopeeNode.getUtmContent().split(HYPHEN);
                String clickId = getClickIdFrom(utmContents);
                String customerType = shopeeNode.getBuyerType().toLowerCase();
                Instant instant = Instant.ofEpochSecond(shopeeNode.getPurchaseTime());
                ZonedDateTime conversionTime = ZonedDateTime.ofInstant(instant, zoneId);
                String targetMd5CampaignId = hashMd5By(getCampaignId());
                if (isTargetCampaignId(utmContents, targetMd5CampaignId)) {
                    for (ShopeeOrder shopeeOrder : shopeeNode.getOrders()) {
                        details.addAll(createConversionDetails(clickId, customerType,
                                conversionTime, shopeeOrder, shopeeNode));
                    }
                }
            }
        }
        return details;
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> createConversionDetails(String clickId,
            String customerType, ZonedDateTime conversionTime, ShopeeOrder shopeeOrder,
            ShopeeNode shopeeNode) throws UnsupportedEncodingException {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        List<String> items = new LinkedList<>();
        for (ShopeeItem shopeeItem : shopeeOrder.getItems()) {
            String identifier = shopeeNode.getCheckoutId();
            String itemId = shopeeItem.getItemId();
            String skuId = Joiner.on(UNDERSCORE)
                    .join(shopeeOrder.getOrderId(), itemId, shopeeItem.getModelId());
            String sku = getItemId(items, skuId);
            items.add(skuId);
            String category = getCategoryByGlobalCategoryLv1Name(
                    shopeeItem.getGlobalCategoryLv1Name());
            boolean isGlobalCategoryLv1SpecialName = Pattern.matches(
                    "(?i).*\\b(cinema|utilities|telco)\\b.*", category.toLowerCase());
            if (isGlobalCategoryLv1SpecialName) {
                continue;
            }

            String conversionOccursDate = conversionTime.format(
                    DATE_TIME_FORMATTER_YYYYMMDD);
            String modelId = shopeeItem.getModelId();
            String key = Joiner.on(HYPHEN)
                    .join(getCampaignId(), conversionOccursDate, identifier,
                            shopeeOrder.getOrderId(), shopeeItem.getItemId(), modelId);
            if (!isConversionUnique(key)) {
                continue;
            }

            BigDecimal commissionRatio = calculateCommissionRatio(
                    shopeeItem.getItemCommission(), shopeeItem.getActualAmount());
            category = getCategory(commissionRatio);
            boolean isBrandItem = isBrandItem(shopeeItem.getShopId());
            List<ConversionRegistrationDetails> brandItemConversions =
                    processConversionByBrandItemRules(conversionTime, identifier,
                    CATEGORY_RESULT_ID, customerType, category, sku,
                    shopeeItem.getActualAmount(), clickId, shopeeItem.getShopId(),
                    isBrandItem);

            List<ConversionRegistrationDetails> bonusConversions = createExtraBonuses(
                    conversionTime, identifier, PRODUCT_RESULT_ID,
                    customerType, BONUS_CATEGORY, sku,
                    clickId, shopeeItem, shopeeOrder, brandItemConversions);
            brandItemConversions.addAll(bonusConversions);

            details.addAll(brandItemConversions);

            insertDataProceeded(createConversionProceeded(conversionTime, details, key));
        }
        return details;
    }

    @VisibleForTesting
    String getCategory(BigDecimal totalCommission) {
        String category = EMPTY;
        double totalCategory = Double.parseDouble(String.format("%.1f", totalCommission));
        if (totalCategory > 0 && totalCategory <= 1.5) {
            category = "Existing13";
        } else if (totalCategory > 1.5 && totalCategory <= 1.9) {
            category = "Existing18";
        } else if (totalCategory > 1.9 && totalCategory <= 2.2) {
            category = "Existing21";
        } else {
            category = CATEGORY_BY_TOTAL_COMMISSION.entrySet().stream()
                    .filter(entry -> entry.getKey() == totalCategory)
                    .map(Map.Entry::getValue).findFirst().orElse("cate0");
        }
        return category;
    }

    @VisibleForTesting
    BigDecimal calculateCommissionRatio(BigDecimal itemCommission, BigDecimal actualAmount) {
        if (itemCommission != null && itemCommission.compareTo(ZERO) != 0
                && actualAmount != null) {
            return itemCommission.divide(actualAmount, 4, ROUND_HALF_UP)
                    .multiply(new BigDecimal("100"));
        }
        return ZERO;
    }

    @VisibleForTesting
    String getCategoryByGlobalCategoryLv1Name(String globalCategoryLv1Name) {
        return globalCategoryLv1Name.replace(SPACE, EMPTY)
                .replace(REQUEST_FIELD_SEPARATOR, PIPE);
    }
}
