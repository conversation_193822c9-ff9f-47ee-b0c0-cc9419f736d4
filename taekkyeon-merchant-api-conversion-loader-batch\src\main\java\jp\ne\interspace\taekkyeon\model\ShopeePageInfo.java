/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding shopee page info.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @ToString
public class ShopeePageInfo {

    private final int limit;
    private final boolean hasNextPage;
    private final String scrollId;
}
