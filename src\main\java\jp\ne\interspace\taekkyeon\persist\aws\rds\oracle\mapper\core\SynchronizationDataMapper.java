/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core;

import java.time.LocalDateTime;
import java.time.YearMonth;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import jp.ne.interspace.taekkyeon.model.GlobalConversionStatusSynchronizationData;
import jp.ne.interspace.taekkyeon.model.SynchronizationData;
import jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.YearMonthTypeHandler;

/**
 * Mybat<PERSON> mapper for handling synchronization data.
 *
 * <AUTHOR>
 */
public interface SynchronizationDataMapper {

    String SCRIPT_TAG_START = "<script>";
    String SCRIPT_TAG_END = "</script>";

    String SELECT_SYNCHRONIZATION_DATA =
            "SELECT "
            + "    sync_start_time AS syncStartTime, "
            + "    conversion_id AS conversionId, "
            + "    site_id AS siteId ";

    String SELECT_CLOSED_MONTH =
            "    , closed_month AS closedMonth ";

    String FROM =
            "FROM "
            + "    synchronization_data ";

    String WHERE = "WHERE ";

    String WHERE_TABLE_NAME =
            "    table_name = #{tableName} ";

    String AND_DATABASE_NAME =
            "AND "
            + "    database_name = #{databaseName} ";

    String AND_DATABASE_NAME_ORACLE =
            "AND "
            + "    database_name = 'ORACLE' ";

    String AND_COUNTY_CODE =
            "<if test = \"countryCode != null and countryCode != ''\">"
            + "    AND "
            + "        country_code = #{countryCode}"
            + "</if>";

    String UPDATE =
            "UPDATE "
            + "    synchronization_data ";

    String SET_SYNC_START_TIME =
            "SET "
                    + "    sync_start_time = #{syncStartTime} ";

    String SET_CONVERSION_ID =
            "    , conversion_id =#{conversionId} ";

    String DELETE =
            "DELETE FROM "
            + "    synchronization_data ";

    String UPSERT_GLOBAL_CONVERSIONS_STATUS =
            "MERGE INTO "
            + "    synchronization_data sync_data "
            + "USING ("
            + "    SELECT"
            + "        'global_conversion_status' table_name,"
            + "        #{countryCode} country_code,"
            + "        #{globalSyncData.syncStartTime} sync_start_time,"
            + "        #{globalSyncData.conversionId} conversion_id,"
            + "        #{globalSyncData.siteId} site_id,"
            + "        #{globalSyncData.closedMonth, jdbcType=DATE,"
            + "                typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.YearMonthTypeHandler}"
            + "                closed_month,"
            + "        'ORACLE' database_name "
            + "    FROM "
            + "        dual "
            + ") temp "
            + "ON ("
            + "    sync_data.table_name = temp.table_name "
            + "AND"
            + "    sync_data.database_name = temp.database_name "
            + "AND"
            + "    sync_data.country_code = temp.country_code"
            + ") "
            + "WHEN MATCHED THEN "
            + "    UPDATE SET"
            + "        sync_data.sync_start_time = temp.sync_start_time,"
            + "        sync_data.conversion_id = temp.conversion_id, "
            + "        sync_data.site_id = temp.site_id, "
            + "        sync_data.closed_month = temp.closed_month "
            + "WHEN NOT MATCHED THEN "
            + "    INSERT ("
            + "        table_name,"
            + "        country_code,"
            + "        sync_start_time,"
            + "        conversion_id,"
            + "        site_id,"
            + "        closed_month,"
            + "        database_name)"
            + "    VALUES ("
            + "        temp.table_name,"
            + "        temp.country_code,"
            + "        temp.sync_start_time,"
            + "        temp.conversion_id,"
            + "        temp.site_id,"
            + "        temp.closed_month,"
            + "        temp.database_name"
            + "    )";

    /**
     * Returns the {@link SynchronizationData} by the given parameters.
     *
     * @param tableName
     *            the given table name
     * @param databaseName
     *            the given database name
     * @param countryCode
     *            the given country code
     * @return the {@link SynchronizationData} by the given parameters
     */
    @Select(SCRIPT_TAG_START + SELECT_SYNCHRONIZATION_DATA + FROM + WHERE
            + WHERE_TABLE_NAME + AND_DATABASE_NAME + AND_COUNTY_CODE + SCRIPT_TAG_END)
    @ConstructorArgs({ @Arg(column = "syncStartTime", javaType = LocalDateTime.class),
            @Arg(column = "conversionId", javaType = Long.class),
            @Arg(column = "siteId", javaType = Long.class) })
    SynchronizationData findSynchronizationData(@Param("tableName") String tableName,
            @Param("databaseName") String databaseName,
            @Param("countryCode") String countryCode);

    /**
     * Returns the {@link GlobalConversionStatusSynchronizationData} by the given
     * parameters.
     *
     * @param tableName
     *            the given table name
     * @param countryCode
     *            the given country code
     * @return the {@link GlobalConversionStatusSynchronizationData} by the given
     *         parameters
     */
    @Select(SCRIPT_TAG_START + SELECT_SYNCHRONIZATION_DATA + SELECT_CLOSED_MONTH + FROM
            + WHERE + WHERE_TABLE_NAME + AND_DATABASE_NAME_ORACLE + AND_COUNTY_CODE
            + SCRIPT_TAG_END)
    @ConstructorArgs({ @Arg(column = "syncStartTime", javaType = LocalDateTime.class),
            @Arg(column = "conversionId", javaType = long.class),
            @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "closedMonth", javaType = YearMonth.class,
                    typeHandler = YearMonthTypeHandler.class) })
    GlobalConversionStatusSynchronizationData
            findGlobalConversionStatusSynchronizationData(
                    @Param("tableName") String tableName,
                    @Param("countryCode") String countryCode);

    /**
     * Updates the {@code syncStartTime} by the given parameters.
     *
     * @param syncStartTime
     *            the sync start time
     * @param tableName
     *            the given table name
     * @param databaseName
     *            the given database name
     * @param countryCode
     *            the given country code
     * @return the number of updated count
     */
    @Update(SCRIPT_TAG_START + UPDATE + SET_SYNC_START_TIME + WHERE + WHERE_TABLE_NAME
            + AND_DATABASE_NAME + AND_COUNTY_CODE + SCRIPT_TAG_END)
    int updateSyncStartTime(@Param("syncStartTime") LocalDateTime syncStartTime,
            @Param("tableName") String tableName,
            @Param("databaseName") String databaseName,
            @Param("countryCode") String countryCode);

    /**
     * Updates the {@code conversionId} and {@code syncStartTime} by the given parameters.
     *
     * @param conversionId
     *            the ID of given conversion
     * @param latestUpdateTime
     *            the sync start time
     * @param tableName
     *            the given table name
     * @param databaseName
     *            the given database name
     * @param countryCode
     *            the given country code
     * @return the number of updated count
     */
    @Update(SCRIPT_TAG_START + UPDATE + SET_SYNC_START_TIME + SET_CONVERSION_ID + WHERE
            + WHERE_TABLE_NAME + AND_DATABASE_NAME + AND_COUNTY_CODE + SCRIPT_TAG_END)
    int updateSynchronizationData(@Param("conversionId") long conversionId,
            @Param("syncStartTime") LocalDateTime latestUpdateTime,
            @Param("tableName") String tableName,
            @Param("databaseName") String databaseName,
            @Param("countryCode") String countryCode);

    /**
     * Upserts the global conversion status by the given parameters.
     *
     * @param globalSyncData
     *            {@link GlobalConversionStatusSynchronizationData} contains the
     *            conditions to be updated
     * @param countryCode
     *            the given country code
     * @return the number of upserted count
     */
    @Update(UPSERT_GLOBAL_CONVERSIONS_STATUS)
    int upsertGlobalConversionStatus(
            @Param("globalSyncData")
                    GlobalConversionStatusSynchronizationData globalSyncData,
            @Param("countryCode") String countryCode);

    /**
     * Deletes the Synchronization data by the given parameters.
     *
     * @param tableName
     *            the given table name
     * @param databaseName
     *            the given database name
     * @param countryCode
     *            the given country code
     * @return the number of deleted count
     */
    @Delete(SCRIPT_TAG_START + DELETE + WHERE + WHERE_TABLE_NAME + AND_DATABASE_NAME
            + AND_COUNTY_CODE + SCRIPT_TAG_END)
    int delete(@Param("tableName") String tableName,
            @Param("databaseName") String databaseName,
            @Param("countryCode") String countryCode);
}
