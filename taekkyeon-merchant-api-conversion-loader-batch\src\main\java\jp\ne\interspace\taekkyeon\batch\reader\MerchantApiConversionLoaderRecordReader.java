/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.reader;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.GenericRecord;
import org.easybatch.core.record.Header;
import org.easybatch.core.record.Record;

import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionsWithClickIdDetails;
import jp.ne.interspace.taekkyeon.service.MerchantApiConversionService;

import static java.util.Collections.emptyIterator;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_PARTITION_CONVERSION_COUNT;
import static lombok.AccessLevel.PACKAGE;

/**
 * {@link RecordReader} implementation for reading conversion data from merchant API.
 *
 * <AUTHOR> Shin
 */
@Slf4j
public class MerchantApiConversionLoaderRecordReader implements RecordReader {

    @Inject
    private MerchantApiConversionService apiService;

    @Getter(PACKAGE)
    private Iterator<ConversionsWithClickIdDetails> iterator = emptyIterator();

    @Inject @Named(BIND_KEY_PARTITION_CONVERSION_COUNT) @Getter(PACKAGE)
    private int partitionConversionCount;

    private long currentRecordNumber;

    @Override
    public void open() throws Exception {
        List<ConversionRegistrationDetails> details = apiService.getConversionData();
        log.info("Handling total data size: {} ... ", details.size());
        if (!details.isEmpty()) {
            iterator = getConversionRegistrationDetails(details).iterator();
        }
    }

    @Override
    public Record<ConversionsWithClickIdDetails> readRecord() throws Exception {
        if (getIterator().hasNext()) {
            return new GenericRecord<>(
                    new Header(++currentRecordNumber, null, getCurrentDate()),
                    getIterator().next());
        }
        return null;
    }

    @Override
    public void close() throws Exception {
        // do nothing.
    }

    @VisibleForTesting
    Date getCurrentDate() {
        return new Date();
    }

    @VisibleForTesting
    List<ConversionsWithClickIdDetails> getConversionRegistrationDetails(
            List<ConversionRegistrationDetails> details) {
        if (details.size() > getPartitionConversionCount()) {
            List<List<ConversionRegistrationDetails>> partitionConversionRegistrations =
                    Lists.partition(details, getPartitionConversionCount());
            return partitionConversionRegistrations.stream()
                    .flatMap(conversions ->
                            apiService.createConversionsWithClickIdDetailsBy(conversions)
                                    .stream())
                    .collect(Collectors.toList());
        }

        return apiService.createConversionsWithClickIdDetailsBy(details);
    }
}
