package com.github.speedwing.log4j.cloudwatch.appender;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.logs.AWSLogs;
import com.amazonaws.services.logs.AWSLogsClientBuilder;
import com.amazonaws.services.logs.model.CreateLogGroupRequest;
import com.amazonaws.services.logs.model.CreateLogStreamRequest;
import com.amazonaws.services.logs.model.DataAlreadyAcceptedException;
import com.amazonaws.services.logs.model.DescribeLogGroupsRequest;
import com.amazonaws.services.logs.model.DescribeLogGroupsResult;
import com.amazonaws.services.logs.model.DescribeLogStreamsRequest;
import com.amazonaws.services.logs.model.DescribeLogStreamsResult;
import com.amazonaws.services.logs.model.InputLogEvent;
import com.amazonaws.services.logs.model.InvalidSequenceTokenException;
import com.amazonaws.services.logs.model.LogGroup;
import com.amazonaws.services.logs.model.LogStream;
import com.amazonaws.services.logs.model.PutLogEventsRequest;
import com.amazonaws.services.logs.model.PutLogEventsResult;

import org.apache.log4j.AppenderSkeleton;
import org.apache.log4j.Layout;
import org.apache.log4j.Logger;
import org.apache.log4j.spi.LoggingEvent;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.toList;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SHUTDOWN_PERIOD;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SHUT_DOWN_MESSAGE;
import static jp.ne.interspace.taekkyeon.module.Environment.isDevelopmentEnvironment;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.AWS_CREDENTIALS_CHAIN;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.DEFAULT_AWS_REGION;

/**
 * Modified {@link #getAwsRegion()} to return {@code DEFAULT_AWS_REGION}
 * and {@link #activateOptions()} to use {@code AWS_CREDENTIALS_CHAIN}.
 *
 * <AUTHOR> Gargiulo
 * <AUTHOR> OBS DEV Team
 */
public class CloudwatchAppender extends AppenderSkeleton {

    /**
     * Used to keep the daemon thread alive.
     */
    public static AtomicBoolean keepDaemonActive = new AtomicBoolean(false);

    private static final int MAX_RETRY_COUNT = Integer.getInteger("cloudwatchLogMaxRetryCount", 10);
    private static final long RETRY_PERIOD_MILLISECONDS = Integer.getInteger("cloudwatchLogRetryPeriod", 2000);
    private static final String EXECUTION_FAILED_ERROR_FORMAT = "%s execution failed - retry %d";
    private static final String FAILED_ALL_RETRIES_FORMAT = "Execution failed on all of %d retries";
    private static final boolean SKIP_ERROR = !"false".equalsIgnoreCase(System.getProperty("cloudwatchLogSkipError"));
    private static final boolean IS_CLOUD_WATCH_LOG_DISABLED = Boolean.getBoolean("isCloudwatchLogDisabled");
    private static final boolean IS_SCALABLE_BATCH = Boolean.getBoolean("isScalableBatch");

    private final Boolean DEBUG_MODE = System.getProperty("log4j.debug") != null;

    /**
     * Used to make sure that on close() our daemon thread isn't also trying to sendMessage()s
     */
    private Object sendMessagesLock = new Object();

    /**
     * The queue used to buffer log entries
     */
    private LinkedBlockingQueue<LoggingEvent> loggingEventsQueue;

    /**
     * the AWS Cloudwatch Logs API client
     */
    private AWSLogs awsLogsClient;

    private AtomicReference<String> lastSequenceToken = new AtomicReference<>();

    /**
     * The AWS Cloudwatch Log group name
     */
    private String logGroupName;

    /**
     * The AWS Cloudwatch Log stream name
     */
    private String logStreamName;

    /**
     * The AWS region
     */
    private String region = null;

    /**
     * The queue / buffer size
     */
    private int queueLength = 1024;

    /**
     * The maximum number of log entries to send in one go to the AWS Cloudwatch Log service
     */
    private int messagesBatchSize = 512;

    /**
     * True if the cloudwatch appender resources have been correctly initialised
     */
    private AtomicBoolean cloudwatchAppenderInitialised = new AtomicBoolean(false);

    public CloudwatchAppender() {
        super();
    }

    public CloudwatchAppender(Layout layout, String logGroupName, String logStreamName, String region) {
        super();
        this.setLayout(layout);
        this.setLogGroupName(logGroupName);
        this.setLogStreamName(logStreamName);
        this.setRegion(region);
        this.activateOptions();
    }

    public void setLogGroupName(String logGroupName) {
        this.logGroupName = logGroupName;
    }

    public void setLogStreamName(String logStreamName) {
        this.logStreamName = logStreamName;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public void setQueueLength(int queueLength) {
        this.queueLength = queueLength;
    }

    public void setMessagesBatchSize(int messagesBatchSize) {
        this.messagesBatchSize = messagesBatchSize;
    }

    @Override
    protected void append(LoggingEvent event) {
        if (cloudwatchAppenderInitialised.get()) {
            loggingEventsQueue.offer(event);
        } else {
            // just do nothing
        }
    }

    private void sendMessages() {
        synchronized (sendMessagesLock) {
            LoggingEvent polledLoggingEvent;

            List<LoggingEvent> loggingEvents = new ArrayList<>();

            try {

                while ((polledLoggingEvent = loggingEventsQueue.poll()) != null && loggingEvents.size() <= messagesBatchSize) {
                    loggingEvents.add(polledLoggingEvent);
                }

                List<InputLogEvent> inputLogEvents = loggingEvents.stream()
                        .map(loggingEvent -> new InputLogEvent().withTimestamp(loggingEvent.getTimeStamp()).withMessage(layout.format(loggingEvent)))
                        .sorted(comparing(InputLogEvent::getTimestamp))
                        .collect(toList());

                if (!inputLogEvents.isEmpty()) {

                    PutLogEventsRequest putLogEventsRequest = new PutLogEventsRequest(
                            logGroupName,
                            logStreamName,
                            inputLogEvents);

                    try {
                        putLogEventsRequest.setSequenceToken(lastSequenceToken.get());
                        PutLogEventsResult result = awsLogsClient.putLogEvents(putLogEventsRequest);
                        lastSequenceToken.set(result.getNextSequenceToken());
                    } catch (DataAlreadyAcceptedException dataAlreadyAcceptedExcepted) {
                        putLogEventsRequest.setSequenceToken(dataAlreadyAcceptedExcepted.getExpectedSequenceToken());
                        PutLogEventsResult result = awsLogsClient.putLogEvents(putLogEventsRequest);
                        lastSequenceToken.set(result.getNextSequenceToken());
                        if (DEBUG_MODE) {
                            dataAlreadyAcceptedExcepted.printStackTrace();
                        }
                    } catch (InvalidSequenceTokenException invalidSequenceTokenException) {
                        putLogEventsRequest.setSequenceToken(invalidSequenceTokenException.getExpectedSequenceToken());
                        PutLogEventsResult result = awsLogsClient.putLogEvents(putLogEventsRequest);
                        lastSequenceToken.set(result.getNextSequenceToken());
                        if (DEBUG_MODE) {
                            invalidSequenceTokenException.printStackTrace();
                        }
                    } catch (Throwable e) {
                        if (DEBUG_MODE) {
                            e.printStackTrace();
                        }
                    }

                    if (inputLogEvents.stream()
                            .map(InputLogEvent::getMessage)
                            .anyMatch(message -> message.contains(SHUT_DOWN_MESSAGE))) {
                        keepDaemonActive.set(false);
                        Thread.sleep(SHUTDOWN_PERIOD);
                    }
                }
            } catch (Exception e) {
                if (DEBUG_MODE) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public void close() {
        while (loggingEventsQueue != null && !loggingEventsQueue.isEmpty()) {
            this.sendMessages();
        }
        keepDaemonActive.set(false);
    }

    @Override
    public boolean requiresLayout() {
        return true;
    }

    private Regions getAwsRegion() {

        if (this.region != null) {
            return Regions.fromName(this.region);
        }

        return DEFAULT_AWS_REGION;
    }

    @Override
    public void activateOptions() {
        super.activateOptions();
        if (isDevelopmentEnvironment() || IS_CLOUD_WATCH_LOG_DISABLED) {
            return;
        }
        if (isBlank(logGroupName) || isBlank(logStreamName)) {
            Logger.getRootLogger().error("Could not initialise CloudwatchAppender because either or both LogGroupName(" + logGroupName + ") and LogStreamName(" + logStreamName + ") are null or empty");
            this.close();
        } else {
            this.awsLogsClient = AWSLogsClientBuilder
                    .standard()
                    .withCredentials(AWS_CREDENTIALS_CHAIN)
                    .withRegion(getAwsRegion())
                    .build();
            loggingEventsQueue = new LinkedBlockingQueue<>(queueLength);
            try {
                initializeCloudwatchResources();
                keepDaemonActive.set(true);
                initCloudwatchDaemon();
                cloudwatchAppenderInitialised.set(true);
            } catch (Exception e) {
                Logger.getRootLogger().error("Could not initialise Cloudwatch Logs for LogGroupName: " + logGroupName + " and LogStreamName: " + logStreamName, e);
                keepDaemonActive.set(false);
                if (DEBUG_MODE) {
                    System.err.println("Could not initialise Cloudwatch Logs for LogGroupName: " + logGroupName + " and LogStreamName: " + logStreamName);
                    e.printStackTrace();
                }
                if (!SKIP_ERROR) {
                    System.exit(1);
                }
            }
        }
    }

    @SuppressWarnings("static-access")
    private void initCloudwatchDaemon() {
        Thread t = new Thread(() -> {
            while (keepDaemonActive.get()) {
                try {
                    if (loggingEventsQueue.size() > 0) {
                        sendMessages();
                    }
                    Thread.currentThread().sleep(200L);
                } catch (InterruptedException e) {
                    if (DEBUG_MODE) {
                        e.printStackTrace();
                    }
                }
            }
        });
        t.setDaemon(true);
        t.start();
    }

    private void initializeCloudwatchResources() {

        if (IS_SCALABLE_BATCH) {
            logStreamName = logGroupName;
            logGroupName = logGroupName.substring(0, logGroupName.lastIndexOf(HYPHEN));
            System.out.println(String.format("Log group name: %s, Log stream name: %s",
                    logGroupName, logStreamName));
        }

        DescribeLogGroupsRequest describeLogGroupsRequest = new DescribeLogGroupsRequest();
        describeLogGroupsRequest.setLogGroupNamePrefix(logGroupName);

        Optional<LogGroup> logGroupOptional = describeLogGroups(describeLogGroupsRequest)
                .getLogGroups()
                .stream()
                .filter(logGroup -> logGroup.getLogGroupName().equals(logGroupName))
                .findFirst();

        if (!logGroupOptional.isPresent()) {
            CreateLogGroupRequest createLogGroupRequest = new CreateLogGroupRequest().withLogGroupName(logGroupName);
            awsLogsClient.createLogGroup(createLogGroupRequest);
        }

        DescribeLogStreamsRequest describeLogStreamsRequest = new DescribeLogStreamsRequest().withLogGroupName(logGroupName).withLogStreamNamePrefix(logStreamName);

        Optional<LogStream> logStreamOptional = describeLogStreams(
                describeLogStreamsRequest)
                .getLogStreams()
                .stream()
                .filter(logStream -> logStream.getLogStreamName().equals(logStreamName))
                .findFirst();

        if (!logStreamOptional.isPresent()) {
            Logger.getLogger(this.getClass()).info("About to create LogStream: " + logStreamName + "in LogGroup: " + logGroupName);
            CreateLogStreamRequest createLogStreamRequest = new CreateLogStreamRequest().withLogGroupName(logGroupName).withLogStreamName(logStreamName);
            awsLogsClient.createLogStream(createLogStreamRequest);
        }

    }

    private DescribeLogGroupsResult describeLogGroups(
            DescribeLogGroupsRequest describeLogGroupsRequest) {
        for (int retryCount = 0; retryCount < MAX_RETRY_COUNT; retryCount++) {
            try {
                return awsLogsClient.describeLogGroups(describeLogGroupsRequest);
            } catch (Exception e) {
                System.out.println(
                        String.format(EXECUTION_FAILED_ERROR_FORMAT, "describeLogGroups",
                                retryCount));
                try {
                    Thread.sleep(RETRY_PERIOD_MILLISECONDS);
                } catch (Exception ex) {
                    // Ignore
                }
            }
        }
        throw new TaekkyeonException(
                String.format(FAILED_ALL_RETRIES_FORMAT, MAX_RETRY_COUNT));
    }

    private DescribeLogStreamsResult describeLogStreams(
            DescribeLogStreamsRequest describeLogStreamsRequest) {
        for (int retryCount = 0; retryCount < MAX_RETRY_COUNT; retryCount++) {
            try {
                return awsLogsClient.describeLogStreams(describeLogStreamsRequest);
            } catch (Exception e) {
                System.out.println(
                        String.format(EXECUTION_FAILED_ERROR_FORMAT, "describeLogStreams",
                                retryCount));
                try {
                    Thread.sleep(RETRY_PERIOD_MILLISECONDS);
                } catch (Exception ex) {
                    // Ignore
                }
            }
        }
        throw new TaekkyeonException(
                String.format(FAILED_ALL_RETRIES_FORMAT, MAX_RETRY_COUNT));
    }

    private boolean isBlank(String string) {
        return null == string || string.trim().length() == 0;
    }
}
