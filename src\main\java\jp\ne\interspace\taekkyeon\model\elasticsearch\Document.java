/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model.elasticsearch;

import com.google.gson.JsonElement;
import com.google.gson.annotations.SerializedName;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the data of elasticsearch.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public class Document {

    @SerializedName("_index")
    private final String index;
    @SerializedName("_type")
    private final String type;
    @SerializedName("_id")
    private final String id;
    @SerializedName("_score")
    private final Double score;
    @SerializedName("_source")
    private final JsonElement source;
}

