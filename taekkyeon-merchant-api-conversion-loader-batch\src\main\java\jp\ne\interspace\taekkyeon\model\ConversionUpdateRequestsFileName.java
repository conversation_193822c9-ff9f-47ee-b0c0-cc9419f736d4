/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the file name for inserting conversion update requests.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public class ConversionUpdateRequestsFileName {

    private final String fileName;
}
