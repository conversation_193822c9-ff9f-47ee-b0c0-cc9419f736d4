/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.Map;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import jp.ne.interspace.taekkyeon.util.TaekkyeonHttpClient;

import static jp.ne.interspace.taekkyeon.module.TaekkyeonSlackMessageSenderModule.BIND_KEY_SLACK_URL;


/**
 * Service layer for handling sending slack.
 *
 * <AUTHOR>
 */
@Singleton @Slf4j
public class SlackService {

    private static final Map<String, String> HEADERS =
            ImmutableMap.<String, String>builder()
                    .put("Content-Type", "application/text")
                    .build();

    @Inject @Named(BIND_KEY_SLACK_URL) @Getter @VisibleForTesting
    private String slackUrl;

    @Inject
    private TaekkyeonHttpClient httpClient;

    /**
     * Sends the message to the Slack by parameters.
     *
     * @param messageFormat
     *            the given message format
     * @param elemnts
     *            the given message elements
     */
    public void send(String messageFormat, String... elemnts) {
        try {
            httpClient.post(getSlackUrl(), HEADERS,
                    ImmutableMap.of("text",
                            createMessage(messageFormat, elemnts)));
        } catch (Exception e) {
            log.error("Something went wrong while sending message to slack", e);
        }
    }

    /**
     * Sends the message to the Slack by parameters.
     *
     * @param message
     *            the given message
     */
    public void send(String message) {
        try {
            httpClient.post(getSlackUrl(), HEADERS,
                    ImmutableMap.of("text", message));
        } catch (Exception e) {
            log.error("Something went wrong while sending message to slack", e);
        }
    }

    @VisibleForTesting
    String createMessage(String messageFormat, String... elemnts) {
        return String.format(messageFormat, elemnts);
    }
}
