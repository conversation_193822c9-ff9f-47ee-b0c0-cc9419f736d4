/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core;

import com.google.inject.Inject;
import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.ClosedMonth;

import static java.time.YearMonth.of;
import static org.junit.Assert.assertEquals;

/**
 * Integration test for {@link MonthlyClosingMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class MonthlyClosingMapperTest {

    @Inject
    private MonthlyClosingMapper underTest;

    @Test
    public void testFindClosedMonthByShouldReturnCorrectDataWhenCalled() {
        // when
        ClosedMonth actual = underTest.findClosedMonth("MY");

        // then
        assertEquals(of(2024, 2), actual.getClosedMonth());
    }
}
