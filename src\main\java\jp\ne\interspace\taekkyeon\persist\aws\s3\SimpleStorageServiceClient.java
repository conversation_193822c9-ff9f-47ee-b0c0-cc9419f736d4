/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.s3;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import javax.inject.Singleton;

import com.amazonaws.AmazonWebServiceRequest;
import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.AbortMultipartUploadRequest;
import com.amazonaws.services.s3.model.AccessControlList;
import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.CompleteMultipartUploadRequest;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.CopyObjectResult;
import com.amazonaws.services.s3.model.CopyPartRequest;
import com.amazonaws.services.s3.model.CopyPartResult;
import com.amazonaws.services.s3.model.CreateBucketRequest;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.DeleteObjectsRequest;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.GetObjectAclRequest;
import com.amazonaws.services.s3.model.GetObjectMetadataRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.InitiateMultipartUploadRequest;
import com.amazonaws.services.s3.model.InitiateMultipartUploadResult;
import com.amazonaws.services.s3.model.ListMultipartUploadsRequest;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.MultipartUploadListing;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PublicAccessBlockConfiguration;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.RestoreObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.s3.model.SetBucketOwnershipControlsRequest;
import com.amazonaws.services.s3.model.SetObjectAclRequest;
import com.amazonaws.services.s3.model.SetPublicAccessBlockRequest;
import com.amazonaws.services.s3.model.UploadPartRequest;
import com.amazonaws.services.s3.model.UploadPartResult;
import com.amazonaws.services.s3.model.ownership.ObjectOwnership;
import com.amazonaws.services.s3.model.ownership.OwnershipControls;
import com.amazonaws.services.s3.model.ownership.OwnershipControlsRule;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;

import static com.google.common.base.Preconditions.checkArgument;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.AWS_CREDENTIALS_CHAIN;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.DEFAULT_AWS_REGION;

/**
 * Low-level client for Amazon S3 Service.
 *
 * <AUTHOR> OBS DEV Team
 * @see AmazonS3
 */
@Singleton
public class SimpleStorageServiceClient {

    private static final String S3_OBJECT_URL_FORMAT = "https://s3.%s.amazonaws.com/%s/%s";

    private AmazonS3 s3;

    /**
     * Constructs a new Amazon S3 client using the specified AWS credentials provider to
     * access Amazon S3.
     */
    public SimpleStorageServiceClient() {
        s3 = createAmazonS3();
    }

    /**
     * Returns a {@link SimpleStorageServiceClient} creating a new {@link AmazonS3Client}
     * using the custom AWS credentials by the given {@code accessKey}
     * with {@code secretKey}.
     *
     * @param accessKey
     *          the AWS access key
     * @param secretKey
     *          the AWS secret key
     * @return {@link SimpleStorageServiceClient}
     */
    public SimpleStorageServiceClient initializeClientWith(String accessKey,
            String secretKey) {
        if (s3 != null) {
            s3.shutdown();
        }
        s3 = createAmazonS3With(accessKey, secretKey);
        return this;
    }

    private AmazonS3 createAmazonS3() {
        return AmazonS3Client
                .builder()
                .withCredentials(AWS_CREDENTIALS_CHAIN)
                .withRegion(DEFAULT_AWS_REGION)
                .build();
    }

    private AmazonS3 createAmazonS3With(String accessKey, String secretKey) {
        return AmazonS3Client
                .builder()
                .withCredentials(createAwsCredentialsProvider(accessKey, secretKey))
                .withRegion(DEFAULT_AWS_REGION)
                .build();
    }

    private AWSCredentialsProvider createAwsCredentialsProvider(String accessKey,
            String secretKey) {
        if (!Strings.isNullOrEmpty(accessKey) && !Strings.isNullOrEmpty(secretKey)) {
            return new AWSStaticCredentialsProvider(
                    new BasicAWSCredentials(accessKey, secretKey));
        }
        throw new TaekkyeonException("Invalid AWS credentials");
    }

    private void checkRequest(AmazonWebServiceRequest request) {
        checkArgument(request != null, "Invalid S3 API request");
    }

    private void checkParameters(String... parameters) {
        String message = "Invalid String parameter(s) for S3 API";
        checkArgument(parameters != null, message);
        for (String parameter : parameters) {
            checkArgument(Strings.nullToEmpty(parameter).trim().length() > 0, message);
        }
    }

    /**
     * Checks if the specified bucket exists.
     *
     * @param bucketName
     *            the name of the bucket to check
     * @return {@code true} if the specified bucket exists
     * @see AmazonS3#doesBucketExistV2(String)
     */
    public boolean doesBucketExist(String bucketName) {
        checkParameters(bucketName);
        return s3.doesBucketExistV2(bucketName);
    }

    /**
     * Creates a new Amazon S3 bucket with the specified name.
     *
     * @param bucketName
     *            the name of the bucket to create; all buckets in Amazon S3 share a
     *            single namespace; ensure the bucket is given a unique name
     * @return the newly created {@link Bucket}
     * @see AmazonS3#createBucket(String)
     */
    public Bucket createBucket(String bucketName) {
        checkParameters(bucketName);
        return s3.createBucket(bucketName);
    }

    /**
     * Creates a new Amazon S3 bucket.
     *
     * @param request
     *            the request object containing all options for creating an Amazon S3
     *            bucket
     * @return the newly created {@link Bucket}
     * @see AmazonS3#createBucket(CreateBucketRequest)
     */
    public Bucket createBucket(CreateBucketRequest request) {
        checkRequest(request);
        return s3.createBucket(request);
    }

    /**
     * Returns a list of all Amazon S3 buckets that the authenticated sender of the
     * request owns.
     *
     * @return a list of all of the Amazon S3 buckets owned by the authenticated sender of
     *         the request
     * @see AmazonS3#listBuckets()
     */
    public List<Bucket> listBuckets() {
        return s3.listBuckets();
    }

    /**
     * Gets the {@link AccessControlList} (ACL) for the specified Amazon S3 bucket.
     *
     * @param bucketName
     *            the name of the bucket whose ACL is being retrieved
     * @return {@link AccessControlList} for the specified S3 bucket
     * @see AmazonS3#getBucketAcl(String)
     */
    public AccessControlList getBucketAccessControlList(String bucketName) {
        checkParameters(bucketName);
        return s3.getBucketAcl(bucketName);
    }

    /**
     * Sets the {@link AccessControlList} for the specified Amazon S3 bucket.
     *
     * @param bucketName
     *            the name of the bucket whose ACL is being set
     * @param accessControlList
     *            the new {@link AccessControlList} for the specified bucket
     * @see AmazonS3#setBucketAcl(String, AccessControlList)
     */
    public void setBucketAccessControlList(String bucketName,
            AccessControlList accessControlList) {
        checkParameters(bucketName);
        checkArgument(accessControlList != null,
                "Invalid AccessControlList parameter for S3 API");
        s3.setBucketAcl(bucketName, accessControlList);
    }

    /**
     * Sets the {@link CannedAccessControlList} for the specified Amazon S3 bucket. A
     * {@link CannedAccessControlList} provides a quick way to configure an object or
     * bucket with commonly used access control policies.
     *
     * @param bucketName
     *            the name of the bucket whose ACL is being set
     * @param cannedAccessControlList
     *            the pre-configured {@link CannedAccessControlList} to set for the
     *            specified bucket
     * @see AmazonS3#setBucketAcl(String, CannedAccessControlList)
     */
    public void setBucketAccessControlList(String bucketName,
            CannedAccessControlList cannedAccessControlList) {
        checkParameters(bucketName);
        checkArgument(cannedAccessControlList != null,
                "Invalid CannedAccessControlList parameter for S3 API");
        s3.setBucketAcl(bucketName, cannedAccessControlList);
    }

    /**
     * Deletes the specified bucket. All objects (and all object versions, if versioning
     * was ever enabled) in the bucket must be deleted before the bucket itself can be
     * deleted.
     * <p>
     * Only the owner of a bucket can delete it, regardless of the bucket's access control
     * policy.
     * </p>
     *
     * @param bucketName
     *            the name of the bucket to delete
     * @see AmazonS3#deleteBucket(String)
     */
    public void deleteBucket(String bucketName) {
        checkParameters(bucketName);
        s3.deleteBucket(bucketName);
    }

    /**
     * Check if the specified object exists in the given bucket.
     *
     * @param bucketName
     *            the name of bucket that presumably contains object
     * @param objectName
     *            the name of object that has to be checked
     * @return {@code true} if the specified object exsits
     */
    public boolean doesObjectExist(String bucketName, String objectName) {
        checkParameters(bucketName, objectName);
        return s3.doesObjectExist(bucketName, objectName);
    }

    /**
     * Uploads a new object to the specified Amazon S3 bucket.
     *
     * @param request
     *            the request object containing all the parameters to upload a new object
     *            to Amazon S3
     * @return a {@link PutObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object
     * @see AmazonS3#putObject(PutObjectRequest)
     */
    public PutObjectResult putObject(PutObjectRequest request) {
        checkRequest(request);
        return s3.putObject(request);
    }

    /**
     * Encodes a String into the contents of an S3 object.
     * <p>
     * String will be encoded to bytes with UTF-8 encoding.
     * </p>
     *
     * @param bucketName
     *            the name of the bucket to place the new object in
     * @param key
     *            the key of the object to create
     * @param content
     *            the String to encode
     * @return a {@link PutObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object
     * @see AmazonS3#putObject(String, String, String)
     */
    public PutObjectResult putObject(String bucketName, String key, String content) {
        checkParameters(bucketName, key, content);
        return s3.putObject(bucketName, key, content);
    }

    /**
     * Uploads the specified file to Amazon S3 under the specified bucket and key name.
     *
     * @param bucketName
     *            the name of the bucket to place the new object in
     * @param key
     *            the key of the object to create
     * @param file
     *            the file containing the data to be uploaded to Amazon S3
     * @return a {@link PutObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object
     * @see AmazonS3#putObject(String, String, File)
     */
    public PutObjectResult putObject(String bucketName, String key, File file) {
        checkParameters(bucketName, key);
        checkArgument(file != null, "Invalid File parameter for S3 API");
        return s3.putObject(bucketName, key, file);
    }

    /**
     * Uploads the specified input stream and object metadata to Amazon S3 under the
     * specified bucket and key name.
     *
     * @param bucketName
     *            the name of the bucket to place the new object in
     * @param key
     *            the key of the object to create
     * @param inputStream
     *            the input stream containing the data to be uploaded to Amazon S3
     * @param objectMetadata
     *            additional metadata instructing Amazon S3 how to handle the uploaded
     *            data (e.g. custom user metadata, hooks for specifying content type,
     *            etc.)
     * @return a {@link PutObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object
     * @see AmazonS3#putObject(String, String, InputStream, ObjectMetadata)
     */
    public PutObjectResult putObject(String bucketName, String key,
            InputStream inputStream, ObjectMetadata objectMetadata) {
        checkParameters(bucketName, key);
        checkArgument(inputStream != null, "Invalid InputStream parameter for S3 API");
        checkArgument(objectMetadata != null,
                "Invalid ObjectMetadata parameter for S3 API");
        return s3.putObject(bucketName, key, inputStream, objectMetadata);
    }

    /**
     * Uploads and public to access the specified file to Amazon S3 under
     * the specified bucket and key name.
     *
     * @param bucketName
     *            the name of the bucket to place the new object in
     * @param key
     *            the key of the object to create
     * @param file
     *            the file containing the data to be uploaded to Amazon S3
     * @return a {@link PutObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object and make it accessible
     * @see AmazonS3#putObject(String, String, File)
     */
    public PutObjectResult putPublicReadObject(String bucketName, String key, File file) {
        return putObject(new PutObjectRequest(bucketName, key, file)
                .withCannedAcl(CannedAccessControlList.PublicRead));
    }

    /**
     * Copies a source object to a new destination in Amazon S3.
     *
     * @param sourceBucketName
     *            the name of the bucket containing the source object to copy
     * @param sourceKey
     *            the key in the source bucket under which the source object is stored
     * @param destinationBucketName
     *            the name of the bucket in which the new object will be created; this can
     *            be the same name as the source bucket's
     * @param destinationKey
     *            the key in the destination bucket under which the new object will be
     *            created
     * @return a {@link CopyObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object
     * @see AmazonS3#copyObject(String, String, String, String)
     */
    public CopyObjectResult copyObject(String sourceBucketName, String sourceKey,
            String destinationBucketName, String destinationKey) {
        checkParameters(sourceBucketName, sourceKey, destinationBucketName,
                destinationKey);
        return s3.copyObject(sourceBucketName, sourceKey, destinationBucketName,
                destinationKey);
    }

    /**
     * Copies a source object to a new destination in Amazon S3.
     *
     * @param request
     *            the request object containing all the options for copying an Amazon S3
     *            object
     * @return a {@link CopyObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object
     * @see AmazonS3#copyObject(CopyObjectRequest)
     */
    public CopyObjectResult copyObject(CopyObjectRequest request) {
        checkRequest(request);
        return s3.copyObject(request);
    }

    @VisibleForTesting
    List<S3ObjectSummary> fetchAll(String bucketName, String prefix) {
        String continuationToken = null;
        List<S3ObjectSummary> allObjects = new LinkedList<>();
        do {
            ListObjectsV2Request listObjectsV2Request =
                    createV2Request(bucketName, prefix, continuationToken);
            ListObjectsV2Result result = s3.listObjectsV2(listObjectsV2Request);
            List<S3ObjectSummary> summaries = result.getObjectSummaries();
            if (summaries.isEmpty()) {
                break;
            }
            allObjects.addAll(summaries);
            continuationToken = result.getNextContinuationToken();

        } while (continuationToken != null);
        return allObjects;
    }

    /**
     * Return all the set keys in the s3 bucket with given criteria.
     *
     * @param bucketName
     *          the given bucket name
     * @param prefix
     *          the given prefix
     * @return all the set keys in the s3 bucket with given criteria
     */
    public List<String> fetchAllKeys(String bucketName, String prefix) {
        List<S3ObjectSummary> allS3Object = fetchAll(bucketName, prefix);
        return allS3Object.stream().map(S3ObjectSummary::getKey)
                .collect(Collectors.toList());
    }

    @VisibleForTesting
    ListObjectsV2Request createV2Request(String bucketName, String prefix,
            String continuationToken) {
        ListObjectsV2Request listObjectsV2Request = new ListObjectsV2Request();
        listObjectsV2Request.setBucketName(bucketName);
        listObjectsV2Request.setPrefix(prefix);
        listObjectsV2Request.setContinuationToken(continuationToken);
        return listObjectsV2Request;
    }

    /**
     * Returns a list of summary information about the objects in the specified bucket.
     *
     * @param bucketName
     *            the name of the Amazon S3 bucket whose objects are to be listed
     * @return a list of the object summaries describing the objects stored in the S3
     *         bucket
     * @see AmazonS3#listObjects(String)
     * @see ObjectListing#getObjectSummaries()
     */
    public List<S3ObjectSummary> listObjects(String bucketName) {
        checkParameters(bucketName);
        return s3.listObjects(bucketName).getObjectSummaries();
    }

    /**
     * Returns a list of summary information about the objects in the specified bucket.
     *
     * @param bucketName
     *            the name of the Amazon S3 bucket whose objects are to be listed
     * @param prefix
     *            the optional prefix parameter restricting the response to keys that
     *            begin with the specified prefix
     * @return a list of the object summaries describing the objects stored in the S3
     *         bucket
     * @see AmazonS3#listObjects(String, String)
     * @see ObjectListing#getObjectSummaries()
     */
    public List<S3ObjectSummary> listObjects(String bucketName, String prefix) {
        checkParameters(bucketName, prefix);
        return s3.listObjects(bucketName, prefix).getObjectSummaries();
    }

    /**
     * Returns a list of summary information about the objects in the specified bucket.
     *
     * @param bucketName
     *            the name of the Amazon S3 bucket whose objects are to be listed
     * @return a listing of the objects in the specified bucket, along with any other
     *         associated information, such as common prefixes (if a delimiter was
     *         specified), the original request parameters, etc
     * @see AmazonS3#listObjects(String)
     */
    public ObjectListing getObjectListing(String bucketName) {
        checkParameters(bucketName);
        return s3.listObjects(bucketName);
    }

    /**
     * Returns a list of summary information about the objects in the specified bucket.
     *
     * @param bucketName
     *            the name of the Amazon S3 bucket whose objects are to be listed
     * @param prefix
     *            the optional prefix parameter restricting the response to keys that
     *            begin with the specified prefix
     * @return a listing of the objects in the specified bucket, along with any other
     *         associated information, such as common prefixes (if a delimiter was
     *         specified), the original request parameters, etc
     * @see AmazonS3#listObjects(String, String)
     */
    public ObjectListing getObjectListing(String bucketName, String prefix) {
        checkParameters(bucketName, prefix);
        return s3.listObjects(bucketName, prefix);
    }

    /**
     * Gets the object stored in Amazon S3 under the specified bucket and key.
     *
     * @param bucketName
     *            the name of the bucket containing the desired object
     * @param key
     *            the key under which the desired object is stored
     * @return the object stored in Amazon S3 in the specified bucket and key
     * @see AmazonS3#getObject(String, String)
     */
    public S3Object getObject(String bucketName, String key) {
        checkParameters(bucketName, key);
        return s3.getObject(bucketName, key);
    }

    /**
     * Gets the object metadata for the object stored in Amazon S3 under the specified
     * bucket and key, and saves the object contents to the specified file.
     * <p>
     * Returns {@code null} if the specified constraints weren't met.
     * </p>
     *
     * @param request
     *            the request object containing all the options on how to download the
     *            Amazon S3 object content.
     * @param destinationFile
     *            indicates the file (which might already exist) where to save the object
     *            content being downloading from Amazon S3
     * @return all S3 object metadata for the specified object. Returns {@code null} if
     *         constraints were specified but not met
     * @see AmazonS3#getObject(GetObjectRequest, File)
     */
    public ObjectMetadata getObject(GetObjectRequest request, File destinationFile) {
        checkRequest(request);
        checkArgument(destinationFile != null, "Invalid File parameter for S3 API");
        return s3.getObject(request, destinationFile);
    }

    /**
     * Gets the object stored in Amazon S3 under the specified bucket and key.
     * <p>
     * Returns {@code null} if the specified constraints weren't met.
     * </p>
     *
     * @param request
     *            the request object containing all the options on how to download the
     *            object
     * @return the object stored in Amazon S3 in the specified bucket and key. Returns
     *         {@code null} if constraints were specified but not met
     * @see AmazonS3#getObject(GetObjectRequest)
     */
    public S3Object getObject(GetObjectRequest request) {
        checkRequest(request);
        return s3.getObject(request);
    }

    /**
     * Retrieves and decodes the contents of an S3 object to a String.
     *
     * @param bucketName
     *            the name of the bucket containing the object to retrieve
     * @param key
     *            the key of the object to retrieve
     * @return the contents of the object as a {@link String}
     * @see AmazonS3#getObjectAsString(String, String)
     */
    public String getObjectAsString(String bucketName, String key) {
        checkParameters(bucketName, key);
        return s3.getObjectAsString(bucketName, key);
    }

    /**
     * Gets the metadata for the specified Amazon S3 object without actually fetching the
     * object itself.
     * <p>
     * This is useful in obtaining only the object metadata, and avoids wasting bandwidth
     * on fetching the object data.
     * </p>
     *
     * @param bucketName
     *            the name of the bucket containing the object's whose metadata is being
     *            retrieved
     * @param key
     *            the key of the object whose metadata is being retrieved
     * @return all Amazon S3 object metadata for the specified object
     * @see AmazonS3#getObjectMetadata(String, String)
     */
    public ObjectMetadata getObjectMetadata(String bucketName, String key) {
        checkParameters(bucketName, key);
        return s3.getObjectMetadata(bucketName, key);
    }

    /**
     * Gets the metadata for the specified Amazon S3 object without actually fetching the
     * object itself.
     * <p>
     * This is useful in obtaining only the object metadata, and avoids wasting bandwidth
     * on fetching the object data.
     * </p>
     *
     * @param request
     *            the request object specifying the bucket, key and optional version ID of
     *            the object whose metadata is being retrieved
     * @return all S3 object metadata for the specified object
     * @see AmazonS3#getObjectMetadata(GetObjectMetadataRequest)
     */
    public ObjectMetadata getObjectMetadata(GetObjectMetadataRequest request) {
        checkRequest(request);
        return s3.getObjectMetadata(request);
    }

    /**
     * Returns a pre-signed URL for accessing an Amazon S3 resource.
     *
     * @param bucketName
     *            the name of the bucket containing the desired object
     * @param key
     *            the key in the specified bucket under which the desired object is stored
     * @param expiration
     *            the date when to expire the URL; see
     *            {@link #generateExpirationDate(long)} for easy generation of a
     *            {@link Date} instance
     * @param httpMethod
     *            the HTTP method verb to use for this URL
     * @return a pre-signed URL which expires at the specified time, and can be used to
     *         allow anyone to download the specified object from S3, without exposing the
     *         owner's AWS secret access key
     * @see AmazonS3#generatePresignedUrl(String, String, Date, HttpMethod)
     */
    public URL generatePresignedUrl(String bucketName, String key, Date expiration,
            HttpMethod httpMethod) {
        checkParameters(bucketName, key);
        checkArgument(expiration != null, "Invalid expiration parameter for S3 API");
        checkArgument(httpMethod != null, "Invalid HttpMethod parameter for S3 API");
        return s3.generatePresignedUrl(bucketName, key, expiration, httpMethod);
    }

    /**
     * Returns a pre-signed URL for accessing an Amazon S3 resource.
     *
     * @param request
     *            the request object containing all the options for generating a
     *            pre-signed URL (bucket name, key, expiration date, etc)
     * @return a pre-signed URL that can be used to access an Amazon S3 resource without
     *         requiring the user of the URL to know the account's AWS security
     *         credentials
     * @see AmazonS3#generatePresignedUrl(GeneratePresignedUrlRequest)
     */
    public URL generatePresignedUrl(GeneratePresignedUrlRequest request) {
        checkRequest(request);
        return s3.generatePresignedUrl(request);
    }

    /**
     * Gets the {@link AccessControlList} (ACL) for the specified object in Amazon S3.
     *
     * @param bucketName
     *            the name of the bucket containing the object whose ACL is being
     *            retrieved
     * @param key
     *            the key of the object within the specified bucket whose ACL is being
     *            retrieved
     * @return the {@link AccessControlList} for the specified Amazon S3 object.
     * @see AmazonS3#getObjectAcl(String, String)
     */
    public AccessControlList getObjectAccessControlList(String bucketName, String key) {
        checkParameters(bucketName, key);
        return s3.getObjectAcl(bucketName, key);
    }

    /**
     * Gets the {@link AccessControlList} (ACL) for the specified object in Amazon S3.
     *
     * @param request
     *            the request object specifying the bucket and key
     * @return the {@link AccessControlList} for the specified Amazon S3 object
     * @see AmazonS3#getObjectAcl(GetObjectAclRequest)
     */
    public AccessControlList getObjectAccessControlList(GetObjectAclRequest request) {
        checkRequest(request);
        return s3.getObjectAcl(request);
    }

    /**
     * Sets the AccessControlList for the specified object in Amazon S3.
     *
     * @param bucketName
     *            the name of the bucket containing the object whose ACL is being set
     * @param key
     *            the key of the object within the specified bucket whose ACL is being set
     * @param accessControlList
     *            the new {@link AccessControlList} for the specified object
     * @see AmazonS3#setObjectAcl(String, String, AccessControlList)
     */
    public void setObjectAccessControlList(String bucketName, String key,
            AccessControlList accessControlList) {
        checkParameters(bucketName, key);
        checkArgument(accessControlList != null,
                "Invalid AccessControlList parameter for S3 API");
        s3.setObjectAcl(bucketName, key, accessControlList);
    }

    /**
     * Sets the {@link CannedAccessControlList} for the specified object in Amazon S3
     * using one of the pre-configured CannedAccessControlLists.
     *
     * @param bucketName
     *            the name of the bucket containing the object whose ACL is being set
     * @param key
     *            the key of the object within the specified bucket whose ACL is being set
     * @param cannedAccessControlList
     *            the new pre-configured {@link CannedAccessControlList} for the specified
     *            object
     * @see AmazonS3#setObjectAcl(String, String, CannedAccessControlList)
     */
    public void setObjectAccessControlList(String bucketName, String key,
            CannedAccessControlList cannedAccessControlList) {
        checkParameters(bucketName, key);
        checkArgument(cannedAccessControlList != null,
                "Invalid CannedAccessControlList parameter for S3 API");
        s3.setObjectAcl(bucketName, key, cannedAccessControlList);
    }

    /**
     * Sets the {@link AccessControlList} for the specified Amazon S3 object with an
     * optional version ID.
     *
     * @param request
     *            the request object containing the S3 object to modify and the ACL to set
     * @see AmazonS3#setObjectAcl(SetObjectAclRequest)
     */
    public void setObjectAccessControlList(SetObjectAclRequest request) {
        checkRequest(request);
        s3.setObjectAcl(request);
    }

    /**
     * Deletes the specified object in the specified bucket. Once deleted, the object can
     * only be restored if versioning was enabled when the object was deleted.
     * <p>
     * If attempting to delete an object that does not exist, Amazon S3 returns a success
     * message instead of an error message.
     * </p>
     *
     * @param bucketName
     *            The name of the Amazon S3 bucket containing the object to delete
     * @param key
     *            The key of the object to delete
     * @see AmazonS3#deleteObject(String, String)
     */
    public void deleteObject(String bucketName, String key) {
        checkParameters(bucketName, key);
        s3.deleteObject(bucketName, key);
    }

    /**
     * Deletes the specified object in the specified bucket. Once deleted, the object can
     * only be restored if versioning was enabled when the object was deleted.
     *
     * @param request
     *            the request object containing all options for deleting an Amazon S3
     *            object
     * @see AmazonS3#deleteObject(DeleteObjectRequest)
     */
    public void deleteObject(DeleteObjectRequest request) {
        checkRequest(request);
        s3.deleteObject(request);
    }

    /**
     * Deletes multiple objects in a single bucket from S3.
     *
     * @param request
     *            the request object containing all options for deleting multiple objects
     * @see AmazonS3#deleteObjects(DeleteObjectsRequest)
     */
    public void deleteObjects(DeleteObjectsRequest request) {
        checkRequest(request);
        s3.deleteObjects(request);
    }

    /**
     * Restore an object, which was transitioned to Amazon Glacier from Amazon S3 when it
     * was expired, into Amazon S3 again.
     * <p>
     * This copy is by nature temporary and is always stored as RRS in Amazon S3. The
     * customer will be able to set / re-adjust the lifetime of this copy. By re-adjust we
     * mean the customer can call this API to shorten or extend the lifetime of the copy.
     * Note the request will only accepted when there is no ongoing restore request.
     * </p>
     * One needs to have the new s3:RestoreObject permission to perform this operation.
     *
     * @param bucketName
     *            the name of an existing bucket
     * @param key
     *            the key under which to store the specified file
     * @param expirationInDays
     *            the number of days after which the object will expire
     * @see AmazonS3#restoreObjectV2(RestoreObjectRequest)
     */
    public void restoreObject(String bucketName, String key, int expirationInDays) {
        checkParameters(bucketName, key);
        checkArgument(expirationInDays >= 0, "Invalid expiration parameter for S3 API");
        s3.restoreObjectV2(new RestoreObjectRequest(bucketName, key, expirationInDays));
    }

    /**
     * Restore an object, which was transitioned to Amazon Glacier from Amazon S3 when it
     * was expired, into Amazon S3 again.
     * <p>
     * This copy is by nature temporary and is always stored as RRS in Amazon S3. The
     * customer will be able to set / re-adjust the lifetime of this copy. By re-adjust we
     * mean the customer can call this API to shorten or extend the lifetime of the copy.
     * Note the request will only accepted when there is no ongoing restore request.
     * </p>
     * One needs to have the new s3:RestoreObject permission to perform this operation.
     *
     * @param request
     *            the request object containing all the options for restoring an Amazon S3
     *            object
     * @see AmazonS3#restoreObjectV2(RestoreObjectRequest)
     */
    public void restoreObject(RestoreObjectRequest request) {
        checkRequest(request);
        s3.restoreObjectV2(request);
    }

    /**
     * Lists in-progress multipart uploads.
     * <p>
     * An in-progress multipart upload is a multipart upload that has been initiated,
     * using the InitiateMultipartUpload request, but has not yet been completed or
     * aborted.
     * </p>
     *
     * @param request
     *            the {@link ListMultipartUploadsRequest} object that specifies all the
     *            parameters of this operation.
     * @return A {@link MultipartUploadListing} from Amazon S3
     * @see AmazonS3#listMultipartUploads(ListMultipartUploadsRequest)
     */
    public MultipartUploadListing listMultipartUploads(
            ListMultipartUploadsRequest request) {
        checkRequest(request);
        return s3.listMultipartUploads(request);
    }

    /**
     * Initiates a multipart upload and returns an InitiateMultipartUploadResult which
     * contains an upload ID.
     * <p>
     * This upload ID associates all the parts in the specific upload and is used in each
     * of your subsequent uploadPart(UploadPartRequest) requests. You also include this
     * upload ID in the final request to either complete, or abort the multipart upload
     * request.
     * </p>
     * <p>
     * <b>Note</b>: After you initiate a multipart upload and upload one or more parts,
     * <u>you must either <b>complete or abort the multipart upload in order to stop
     * getting charged</b> for storage of the uploaded parts.</u> Once you complete or
     * abort the multipart upload Amazon S3 will release the stored parts and stop
     * charging you for their storage.
     * </p>
     *
     * @param request the InitiateMultipartUploadRequest object that specifies all the
     *                parameters of this operation
     * @return an {@link InitiateMultipartUploadResult} from Amazon S3 containing the
     *         upload ID for the initiated multipart upload
     * @see AmazonS3#initiateMultipartUpload(InitiateMultipartUploadRequest)
     */
    public InitiateMultipartUploadResult initiateMultipartUpload(
            InitiateMultipartUploadRequest request) {
        checkRequest(request);
        return s3.initiateMultipartUpload(request);
    }

    /**
     * Uploads a part in a multipart upload. You must initiate a multipart upload before
     * you can upload any part.
     * <p>
     * Your UploadPart request must include an upload ID and a part number. The upload ID
     * is the ID returned by Amazon S3 in response to your Initiate Multipart Upload
     * request. Part number can be any number between 1 and 10,000, inclusive. A part
     * number uniquely identifies a part and also defines its position within the object
     * being uploaded. If you upload a new part using the same part number that was
     * specified in uploading a previous part, the previously uploaded part is
     * overwritten.
     * </p>
     *
     * @param request
     *            the UploadPartRequest object that specifies all the parameters of this
     *            operation
     * @return an {@link UploadPartResult} from Amazon S3 containing the part number and
     *         ETag of the new part
     * @see AmazonS3#uploadPart(UploadPartRequest)
     */
    public UploadPartResult uploadPart(UploadPartRequest request) {
        checkRequest(request);
        return s3.uploadPart(request);
    }

    /**
     * Copies a source object to a part of a multipart upload. To copy an object, the
     * caller's account must have read access to the source object and write access to the
     * destination bucket.
     * <p>
     * If constraints are specified in the {@link CopyPartRequest} (e.g.
     * {@link CopyPartRequest#setMatchingETagConstraints(List)}) and are not satisfied
     * when Amazon S3 receives the request, this method returns {@code null}. This method
     * returns a non-null result under all other circumstances.
     * </p>
     *
     * @param request
     *            the request object containing all the options for copying an Amazon S3
     *            object
     * @return a {@link CopyPartResult} object containing the information returned by
     *         Amazon S3 about the newly created object, or {@code null} if constraints
     *         were specified that weren't met when Amazon S3 attempted to copy the object
     * @see AmazonS3#copyPart(CopyPartRequest)
     */
    public CopyPartResult copyPart(CopyPartRequest request) {
        checkRequest(request);
        return s3.copyPart(request);
    }

    /**
     * Aborts a multipart upload.
     * <p>
     * After a multipart upload is aborted, no additional parts can be uploaded using that
     * upload ID. The storage consumed by any previously uploaded parts will be freed.
     * However, if any part uploads are currently in progress, those part uploads may or
     * may not succeed. As a result, it may be necessary to abort a given multipart upload
     * multiple times in order to completely free all storage consumed by all parts.
     * </p>
     *
     * @param request
     *            the AbortMultipartUploadRequest object that specifies all the parameters
     *            of this operation
     * @see AmazonS3#abortMultipartUpload(AbortMultipartUploadRequest)
     */
    public void abortMultipartUpload(AbortMultipartUploadRequest request) {
        checkRequest(request);
        s3.abortMultipartUpload(request);
    }

    /**
     * Completes a multipart upload by assembling previously uploaded parts.
     * <p>
     * You first upload all parts using the {@link #uploadPart(UploadPartRequest)} method.
     * After successfully uploading all individual parts of an upload, you call this
     * operation to complete the upload. Upon receiving this request, Amazon S3
     * concatenates all the parts in ascending order by part number to create a new
     * object. In the {@link CompleteMultipartUploadRequest}, you must provide the parts
     * list. For each part in the list, you provide the part number and the ETag header
     * value, returned after that part was uploaded.
     * </p>
     * <p>
     * Processing of a {@link CompleteMultipartUploadRequest} may take several minutes to
     * complete.
     * </p>
     *
     * @param request
     *            the {@link CompleteMultipartUploadRequest} object that specifies all the
     *            parameters of this operation
     * @see AmazonS3#completeMultipartUpload(CompleteMultipartUploadRequest)
     */
    public void completeMultipartUpload(CompleteMultipartUploadRequest request) {
        checkRequest(request);
        s3.completeMultipartUpload(request);
    }

    /**
     * Returns an URL for the object stored in the specified bucket and key.
     *
     * @param bucketName
     *            The name of the bucket containing the object whose URL is
     *            being requested.
     * @param key
     *            The key under which the object whose URL is being requested is
     *            stored.
     * @return an URL for the object stored in the specified bucket and key
     */
    public String getUrl(String bucketName, String key) {
        checkParameters(bucketName, key);
        return String.format(S3_OBJECT_URL_FORMAT, s3.getRegionName(), bucketName, key);
    }

    /**
     * Updates bucket setting as public.
     *
     * @param bucketName
     *            The name of the bucket containing the object whose URL is
     *            being requested
     */
    public void updateBucketAsPublic(String bucketName) {
        checkParameters(bucketName);
        s3.setBucketOwnershipControls(new SetBucketOwnershipControlsRequest()
                .withBucketName(bucketName)
                .withOwnershipControls(new OwnershipControls()
                        .withRules(Arrays.asList(new OwnershipControlsRule()
                                .withOwnership(ObjectOwnership.BucketOwnerPreferred)))));
        s3.setPublicAccessBlock(new SetPublicAccessBlockRequest()
                .withBucketName(bucketName).withPublicAccessBlockConfiguration(
                        new PublicAccessBlockConfiguration().withBlockPublicAcls(false)));
    }
}
