/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import javax.inject.Singleton;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.MerchantAccountMapper;

/**
 * Service layer for handling merchant-account data.
 *
 * <AUTHOR>
 */
@Singleton
public class MerchantAccountService {

    @Inject
    private MerchantAccountMapper merchantAccountMapper;

    private LoadingCache<Long, String> countryCodeCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .build(new CacheLoader<Long, String>() {

                @Override
                public String load(Long campaignId) {
                    return merchantAccountMapper.findCountryCodeBy(campaignId);
                }
            });

    /**
     * Returns the country code of merchant by the given campaign ID.
     *
     * @param campaignId
     *          ID of the given campaign
     * @return the country code of merchant by the given campaign ID
     */
    public String findCountryCodeBy(long campaignId) {
        return countryCodeCache.getUnchecked(campaignId);
    }
}
