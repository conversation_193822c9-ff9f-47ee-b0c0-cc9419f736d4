/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.dynamodb;

import java.util.List;
import java.util.Map;

import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.model.BatchGetItemRequest;
import com.amazonaws.services.dynamodbv2.model.BatchGetItemResult;
import com.amazonaws.services.dynamodbv2.model.BatchWriteItemRequest;
import com.amazonaws.services.dynamodbv2.model.BatchWriteItemResult;
import com.amazonaws.services.dynamodbv2.model.Condition;
import com.amazonaws.services.dynamodbv2.model.KeysAndAttributes;
import com.amazonaws.services.dynamodbv2.model.ScanRequest;
import com.amazonaws.services.dynamodbv2.model.ScanResult;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link DynamoDbClient} methods that execute scan and batch operations on
 * DynamoDB tables.
 *
 * <AUTHOR> OBS DEV Team
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class DynamoDbClientScanBatchTest {

    private static final String DEFAULT_TEST_TABLE_NAME = "don't just blindly mimick; start thinking";

    private static final String DEFAULT_TEST_FILTER_EXPRESSION = "it's not how you've done it";

    private static final String DEFAULT_TEST_PROJECTION_EXPRESSION = "it's what you've done";

    private static final Map<String, Condition> DEFAULT_TEST_SCAN_FILTER = ImmutableMap
            .of("el condición", new Condition());

    private static final List<String> DEFAULT_TEST_ATTRIBUTE_NAMES = ImmutableList
            .of("uno", "dos", "tres");

    @InjectMocks
    private DynamoDbClient underTest;

    @Mock
    private AmazonDynamoDB dynamoDb;

    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenTableNameIsNull() {
        // given
        String tableName = null;

        // when
        underTest.scan(tableName, DEFAULT_TEST_FILTER_EXPRESSION,
                DEFAULT_TEST_PROJECTION_EXPRESSION);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenTableNameIsEmpty() {
        // given
        String tableName = " ";

        // when
        underTest.scan(tableName, DEFAULT_TEST_FILTER_EXPRESSION,
                DEFAULT_TEST_PROJECTION_EXPRESSION);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenFilterExpressionIsNull() {
        // given
        String filterExpression = null;

        // when
        underTest.scan(DEFAULT_TEST_TABLE_NAME, filterExpression,
                DEFAULT_TEST_PROJECTION_EXPRESSION);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenFilterExpressionIsEmpty() {
        // given
        String filterExpression = " ";

        // when
        underTest.scan(DEFAULT_TEST_TABLE_NAME, filterExpression,
                DEFAULT_TEST_PROJECTION_EXPRESSION);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenProjectionExpressionIsNull() {
        // given
        String projectionExpression = null;

        // when
        underTest.scan(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_FILTER_EXPRESSION,
                projectionExpression);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenProjectionExpressionIsEmpty() {
        // given
        String projectionExpression = " ";

        // when
        underTest.scan(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_FILTER_EXPRESSION,
                projectionExpression);
    }

    @Test
    public void testScanShouldReturnScanResultWhenGivenValidStringParameters() {
        // given
        ScanResult expected = new ScanResult();
        when(dynamoDb.scan(new ScanRequest(DEFAULT_TEST_TABLE_NAME)
                .withFilterExpression(DEFAULT_TEST_FILTER_EXPRESSION)
                .withProjectionExpression(DEFAULT_TEST_PROJECTION_EXPRESSION)))
                        .thenReturn(expected);

        // when
        ScanResult actual = underTest.scan(DEFAULT_TEST_TABLE_NAME,
                DEFAULT_TEST_FILTER_EXPRESSION, DEFAULT_TEST_PROJECTION_EXPRESSION);

        // then
        assertSame(expected, actual);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenNullTableName() {
        // given
        String tableName = null;

        // when
        underTest.scan(tableName, DEFAULT_TEST_SCAN_FILTER, DEFAULT_TEST_ATTRIBUTE_NAMES);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenEmptyTableName() {
        // given
        String tableName = " ";

        // when
        underTest.scan(tableName, DEFAULT_TEST_SCAN_FILTER, DEFAULT_TEST_ATTRIBUTE_NAMES);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenNullScanFilter() {
        // given
        Map<String, Condition> scanFilter = null;

        // when
        underTest.scan(DEFAULT_TEST_TABLE_NAME, scanFilter, DEFAULT_TEST_ATTRIBUTE_NAMES);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenEmptyScanFilter() {
        // given
        Map<String, Condition> scanFilter = ImmutableMap.of();

        // when
        underTest.scan(DEFAULT_TEST_TABLE_NAME, scanFilter, DEFAULT_TEST_ATTRIBUTE_NAMES);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenNullAttributeNames() {
        // given
        List<String> attributeNames = null;

        // when
        underTest.scan(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_SCAN_FILTER, attributeNames);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenEmptyAttributeNames() {
        // given
        List<String> attributeNames = ImmutableList.of();

        // when
        underTest.scan(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_SCAN_FILTER, attributeNames);
    }

    @SuppressWarnings("deprecation")
    @Test
    public void testScanShouldReturnScanResultWhenGivenValidParameters() {
        // given
        ScanResult expected = new ScanResult();
        when(dynamoDb.scan(new ScanRequest(DEFAULT_TEST_TABLE_NAME)
                .withScanFilter(DEFAULT_TEST_SCAN_FILTER)
                .withAttributesToGet(DEFAULT_TEST_ATTRIBUTE_NAMES)))
                        .thenReturn(expected);

        // when
        ScanResult actual = underTest.scan(DEFAULT_TEST_TABLE_NAME,
                DEFAULT_TEST_SCAN_FILTER, DEFAULT_TEST_ATTRIBUTE_NAMES);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testScanShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        ScanRequest request = null;

        // when
        underTest.scan(request);
    }

    @Test
    public void testScanShouldReturnScanResultWhenGivenScanRequest() {
        // given
        ScanRequest request = new ScanRequest();
        ScanResult expected = new ScanResult();
        when(dynamoDb.scan(request)).thenReturn(expected);

        // when
        ScanResult actual = underTest.scan(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBatchWriteItemShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        BatchWriteItemRequest request = null;

        // when
        underTest.batchWriteItem(request);
    }

    @Test
    public void testBatchWriteItemShouldReturnBatchWriteItemResultWhenGivenBatchWriteItemRequest() {
        // given
        BatchWriteItemRequest request = new BatchWriteItemRequest();
        BatchWriteItemResult expected = new BatchWriteItemResult();
        when(dynamoDb.batchWriteItem(request)).thenReturn(expected);

        // when
        BatchWriteItemResult actual = underTest.batchWriteItem(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBatchGetItemShouldThrowIllegalArgumentExceptionWhenGivenNullItems() {
        // given
        Map<String, KeysAndAttributes> items = null;

        // when
        underTest.batchGetItem(items);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBatchGetItemShouldThrowIllegalArgumentExceptionWhenGivenEmptyItems() {
        // given
        Map<String, KeysAndAttributes> items = ImmutableMap.of();

        // when
        underTest.batchGetItem(items);
    }

    @Test
    public void testBatchGetItemShouldReturnBatchGetItemResultWhenGivenValidItems() {
        // given
        Map<String, KeysAndAttributes> items = ImmutableMap.of("llaves_y_atributos",
                new KeysAndAttributes());
        BatchGetItemResult expected = new BatchGetItemResult();
        when(dynamoDb.batchGetItem(items)).thenReturn(expected);

        // when
        BatchGetItemResult actual = underTest.batchGetItem(items);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBatchGetItemShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        BatchGetItemRequest request = null;

        // when
        underTest.batchGetItem(request);
    }

    @Test
    public void testBatchGetItemShouldReturnBatchGetItemResultWhenGivenBatchGetItemRequest() {
        // given
        BatchGetItemRequest request = new BatchGetItemRequest();
        BatchGetItemResult expected = new BatchGetItemResult();
        when(dynamoDb.batchGetItem(request)).thenReturn(expected);

        // when
        BatchGetItemResult actual = underTest.batchGetItem(request);

        // then
        assertSame(expected, actual);
    }

}
