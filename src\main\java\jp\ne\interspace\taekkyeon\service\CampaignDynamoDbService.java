/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import javax.inject.Singleton;

import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.model.CampaignStatus;
import jp.ne.interspace.taekkyeon.persist.aws.dynamodb.CampaignDynamoDbTable;

/**
 * Service layer for handling campaign.
 *
 * <AUTHOR>
 */
@Singleton
public class CampaignDynamoDbService {

    @Inject
    private CampaignDynamoDbTable campaignDynamoDbTable;

    /**
     * Update campaign status.
     *
     * @param campaignId
     *          the given campaign ID
     * @param campaignStatus
     *          the given campaign status
     */
    public void updateCampaignStatus(long campaignId, CampaignStatus campaignStatus) {
        campaignDynamoDbTable.updateCampaignStatus(campaignId, campaignStatus);
    }
}
