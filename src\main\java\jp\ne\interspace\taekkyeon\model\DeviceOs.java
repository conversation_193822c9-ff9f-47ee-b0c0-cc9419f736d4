/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding device operating systems.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum DeviceOs implements ValueEnum {
    UNKNOWN(0, "unknown"),
    ANDROID(1, "android"),
    BLACKBERRY(2, "blackberry"),
    IPHONE(3, "iphone"),
    IPOD(4, "ipod"),
    IPAD(5, "ipad"),
    WINDOWS_PHONE(6, "windows phone"),
    MAC(7, "macintosh"),
    WINDOWS(8, "win"),
    LINUX(9, "linux");

    private final int value;
    private final String userAgentFragment;

    /**
     * Returns the {@link DeviceOs} item for the given {@code value}, or {@code null},
     * if no {@link DeviceOs} is associated with {@code value}.
     *
     * @param value
     *            the value whose associated {@link DeviceOs} is to be returned
     * @return the {@link DeviceOs} item for the given {@code value}, or {@code null},
     *         if no {@link DeviceOs} is associated with {@code value}
     */
    public static DeviceOs findBy(int value) {
        for (DeviceOs deviceOs : values()) {
            if (value == deviceOs.value) {
                return deviceOs;
            }
        }
        return DeviceOs.UNKNOWN;
    }
}
