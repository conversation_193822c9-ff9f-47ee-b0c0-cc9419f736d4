/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.util.Map;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;

import org.easybatch.core.record.GenericRecord;
import org.easybatch.core.record.Header;
import org.easybatch.core.record.Record;

/**
 * {@link Record} holding the data to be updated in DynamoDb.
 *
 * <AUTHOR>
 */
public class DynamoDbUpdateRecord
        extends GenericRecord<Map<String, Map<String, AttributeValue>>> {

    public DynamoDbUpdateRecord(Header header,
            Map<String, Map<String, AttributeValue>> payload) {
        super(header, payload);
    }
}
