/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum holding the publisher account statuses.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum PublisherAccountStatus implements ValueEnum {

    NOT_ACTIVATED(0),
    ACTIVATED(1),
    DENIED(2);

    private final int value;
}
