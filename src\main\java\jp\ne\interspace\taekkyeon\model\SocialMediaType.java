/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding all the social media type.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum SocialMediaType implements ValueEnum {

    OTHER(0),
    YOUTUBE(1),
    FACEBOOK(2),
    INSTAGRAM(3),
    TWITTER(4),
    TIKTOK(5),
    PINTEREST(6),
    TWITCH(7),
    LINKEDIN(8),
    LIN<PERSON>(9);

    private final int value;
}
