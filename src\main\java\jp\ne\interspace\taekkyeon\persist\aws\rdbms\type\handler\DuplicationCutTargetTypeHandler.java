/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.DuplicationCutTarget;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler.ValueEnumTypeHandler;

/**
 * MyBatis {@link TypeHandler} for {@link DuplicationCutCondition}.
 *
 * <AUTHOR> Mayur
 */
@MappedTypes(DuplicationCutTarget.class)
public class DuplicationCutTargetTypeHandler
        extends ValueEnumTypeHandler<DuplicationCutTarget> {

    public DuplicationCutTargetTypeHandler() {
        super(DuplicationCutTarget.class, DuplicationCutTarget.DAILY);
    }
}
