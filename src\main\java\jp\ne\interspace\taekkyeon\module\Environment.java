/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * Application environments for Taekkyeon runtime.
 * <p/>
 * (i.e. {@link Environment#DEV}, {@link Environment#STAGING} and
 * {@link Environment#PRODUCTION})
 *
 * <AUTHOR> OBS DEV Team
 */
public enum Environment {

    /**
     * Development environment, which should be specified as a VM argument
     * <i>-Denvironment=dev</i> or <i>-Denvironment=DEV</i>.
     */
    DEV,

    /**
     * Staging environment, which should be specified as a VM argument
     * <i>-Denvironment=staging</i> or <i>-Denvironment=STAGING</i>.
     */
    STAGING,

    /**
     * Production environment, which should be specified as a VM argument
     * <i>-Denvironment=production</i> or <i>-Denvironment=PRODUCTION</i>.
     */
    PRODUCTION;

    private static final String VM_ARG_NAME_FOR_ENVIRONMENT = "environment";

    /**
     * Returns the curent application {@link Environment} corresponding to the given VM
     * argument {@code -Denvironment}.
     *
     * @return application {@link Environment}
     */
    public static Environment getCurrentEnvironment() {
        return Stream.of(VM_ARG_NAME_FOR_ENVIRONMENT)
                .map(System::getProperty)
                .filter(Objects::nonNull)
                .map(String::toUpperCase)
                .map(Environment::valueOf)
                .findFirst().get();
    }

    /**
     * Returns true when curent {@link Environment} is the development environment.
     *
     * @return true when curent {@link Environment} is the development environment
     */
    public static boolean isDevelopmentEnvironment() {
        return Environment
                .valueOf(System.getProperty(VM_ARG_NAME_FOR_ENVIRONMENT, DEV.toString())
                        .toUpperCase()) == DEV;
    }
}
