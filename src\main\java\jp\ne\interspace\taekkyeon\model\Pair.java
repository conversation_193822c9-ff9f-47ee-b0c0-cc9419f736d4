/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO contains two keys.
 *
 * @param <L> type of the first key.
 * @param <R> type of the second key.
 * <AUTHOR>
 */
@AllArgsConstructor(staticName = "of") @Getter @EqualsAndHashCode @ToString
public class Pair<L, R> {

    private final L left;
    private final R right;
}
