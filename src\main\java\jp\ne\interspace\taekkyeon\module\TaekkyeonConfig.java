/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSCredentialsProviderChain;
import com.amazonaws.auth.EnvironmentVariableCredentialsProvider;
import com.amazonaws.auth.InstanceProfileCredentialsProvider;
import com.amazonaws.regions.Regions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableTable;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import jp.ne.interspace.taekkyeon.persist.aws.secretsmanager.SecretsManagerClient;

import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Country.VIETNAM;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;

/**
 * Configurations for Taekkyeon application.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonConfig {

    /**
     * System default AWS Region.
     */
    public static final Regions DEFAULT_AWS_REGION = Regions.AP_SOUTHEAST_1;

    /**
     * AWS Region for SimpleEmailService.
     */
    public static final Regions AWS_SES_REGION = Regions.US_EAST_1;

    /**
     * Resolves the credentials in the following order:
     * i. EC2 instance credentials
     * ii. if that's not available, use the access key of the obs-dev-env-user
     */
    public static final AWSCredentialsProvider AWS_CREDENTIALS_CHAIN = new AWSCredentialsProviderChain(
            InstanceProfileCredentialsProvider.getInstance(),
            new EnvironmentVariableCredentialsProvider());

    /**
     * Resolves the credentials in the following order:
     * i. use jvm access key if it is available
     * ii. EC2 instance credentials
     * iii. if that's not available, use the access key of the obs-dev-env-user
     */
    public static final AWSCredentialsProvider AWS_SQS_CREDENTIALS_CHAIN;

    static {
        String customSqsAccessKey = System.getProperty("customSqsAccessKey");
        String customSqsSecretKey = System.getProperty("customSqsSecretKey");
        List<AWSCredentialsProvider> providers = new LinkedList<>();
        if (!Strings.isNullOrEmpty(customSqsAccessKey)
                && !Strings.isNullOrEmpty(customSqsSecretKey)) {
            providers.add(new BasicAwsCredentialsProvider(customSqsAccessKey,
                    customSqsSecretKey));
        }
        providers.add(InstanceProfileCredentialsProvider.getInstance());
        providers.add(new EnvironmentVariableCredentialsProvider());
        AWS_SQS_CREDENTIALS_CHAIN = new AWSCredentialsProviderChain(
                providers.toArray(new AWSCredentialsProvider[providers.size()]));
    }

    private static final Type SECRET_VALUE_TYPE = new TypeToken<Map<String, String>>() {}.getType();
    private static final ImmutableTable<Country, Environment, String> SECRETS_MANAGER_CREDENTIALS_KEY =
            new ImmutableTable.Builder<Country, Environment, String>()
                    .put(THAILAND, DEV, "THAILAND_DEV_GENERAL_SECRET_VALUES")
                    .put(INDONESIA, DEV, "INDONESIA_DEV_GENERAL_SECRET_VALUES")
                    .put(VIETNAM, DEV, "VIETNAM_DEV_GENERAL_SECRET_VALUES")
                    .put(THAILAND, STAGING, "THAILAND_STAGING_GENERAL_SECRET_VALUES")
                    .put(INDONESIA, STAGING, "INDONESIA_STAGING_GENERAL_SECRET_VALUES")
                    .put(VIETNAM, STAGING, "VIETNAM_STAGING_GENERAL_SECRET_VALUES")
                    .put(THAILAND, PRODUCTION, "THAILAND_PRODUCTION_GENERAL_SECRET_VALUES")
                    .put(INDONESIA, PRODUCTION, "INDONESIA_PRODUCTION_GENERAL_SECRET_VALUES")
                    .put(VIETNAM, PRODUCTION, "VIETNAM_PRODUCTION_GENERAL_SECRET_VALUES")
                    .build();
    private static Map<String, Map<String, String>> TOTAL_SECRET_MANAGERS = new HashMap<>();

    /**
     * Returns secrets manager credentials values for which country, environment and key.
     *
     * @param country
     *              the given country
     * @param environment
     *              the given environment
     *  @param key
     *              the given AWS secret key
     * @return secrets manager credentials values
     */
    public static String getSecretValues(Country country, Environment environment,
            String key) {
        String totalSecretKey = new StringBuilder().append(country.getCode()).append("_")
                .append(environment.toString()).toString();
        if (!TOTAL_SECRET_MANAGERS.containsKey(totalSecretKey)) {
            String secretValue = SecretsManagerClient.getInstance().getSecretValue(
                    SECRETS_MANAGER_CREDENTIALS_KEY.get(country, environment));
            TOTAL_SECRET_MANAGERS.put(totalSecretKey,
                    new Gson().fromJson(secretValue, SECRET_VALUE_TYPE));
        }
        return TOTAL_SECRET_MANAGERS.get(totalSecretKey).get(key);
    }
}
