/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.dynamodb;

import java.util.Map;

import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.AttributeValueUpdate;
import com.amazonaws.services.dynamodbv2.model.DeleteItemRequest;
import com.amazonaws.services.dynamodbv2.model.DeleteItemResult;
import com.amazonaws.services.dynamodbv2.model.GetItemRequest;
import com.amazonaws.services.dynamodbv2.model.GetItemResult;
import com.amazonaws.services.dynamodbv2.model.PutItemRequest;
import com.amazonaws.services.dynamodbv2.model.PutItemResult;
import com.amazonaws.services.dynamodbv2.model.ReturnValue;
import com.amazonaws.services.dynamodbv2.model.UpdateItemRequest;
import com.amazonaws.services.dynamodbv2.model.UpdateItemResult;
import com.google.common.collect.ImmutableMap;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link DynamoDbClient} methods that execute item operations on DynamoDB
 * tables.
 *
 * <AUTHOR> OBS DEV Team
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class DynamoDbClientItemOperationTest {

    private static final String DEFAULT_TEST_TABLE_NAME = "don't just blindly mimick; start thinking";

    private static final String DEFAULT_TEST_CONDITION_EXPRESSION = "it's not how you've done it; it's what you've done";

    private static final Map<String, AttributeValue> DEFAULT_TEST_ITEMS = ImmutableMap
            .of("el atributo", new AttributeValue());

    private static final Map<String, AttributeValue> DEFAULT_TEST_KEY = ImmutableMap
            .of("el llave", new AttributeValue());

    private static final Map<String, AttributeValueUpdate> DEFAULT_TEST_UPDATES = ImmutableMap
            .of("los actualizaciones", new AttributeValueUpdate());

    private static final ReturnValue DEFAULT_TEST_RETURN_VALUES = ReturnValue.ALL_NEW;

    @InjectMocks
    private DynamoDbClient underTest;

    @Mock
    private AmazonDynamoDB dynamoDb;

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenTableNameIsNull() {
        // given
        String tableName = null;

        // when
        underTest.putItem(tableName, DEFAULT_TEST_ITEMS,
                DEFAULT_TEST_CONDITION_EXPRESSION, false);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenTableNameIsEmpty() {
        // given
        String tableName = " ";

        // when
        underTest.putItem(tableName, DEFAULT_TEST_ITEMS,
                DEFAULT_TEST_CONDITION_EXPRESSION, false);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenItemsIsNull() {
        // given
        Map<String, AttributeValue> items = null;

        // when
        underTest.putItem(DEFAULT_TEST_TABLE_NAME, items,
                DEFAULT_TEST_CONDITION_EXPRESSION, false);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenItemsIsEmpty() {
        // given
        Map<String, AttributeValue> items = ImmutableMap.of();

        // when
        underTest.putItem(DEFAULT_TEST_TABLE_NAME, items,
                DEFAULT_TEST_CONDITION_EXPRESSION, false);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenConditionExpressionIsNull() {
        // given
        String conditionExpression = null;

        // when
        underTest.putItem(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_ITEMS,
                conditionExpression, false);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenConditionExpressionIsEmpty() {
        // given
        String conditionExpression = " ";

        // when
        underTest.putItem(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_ITEMS,
                conditionExpression, false);
    }

    @Test
    public void testPutItemShouldReturnPutItemResultWhenGivenValidParametersWithOldValues() {
        // given
        PutItemResult expected = new PutItemResult();
        when(dynamoDb
                .putItem(new PutItemRequest(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_ITEMS)
                        .withConditionExpression(DEFAULT_TEST_CONDITION_EXPRESSION)
                        .withReturnValues(ReturnValue.ALL_OLD))).thenReturn(expected);
        // when
        PutItemResult actual = underTest.putItem(DEFAULT_TEST_TABLE_NAME,
                DEFAULT_TEST_ITEMS, DEFAULT_TEST_CONDITION_EXPRESSION, true);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testPutItemShouldReturnPutItemResultWhenGivenValidParametersWithNoOldValues() {
        // given
        PutItemResult expected = new PutItemResult();
        when(dynamoDb
                .putItem(new PutItemRequest(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_ITEMS)
                        .withConditionExpression(DEFAULT_TEST_CONDITION_EXPRESSION)))
                                .thenReturn(expected);
        // when
        PutItemResult actual = underTest.putItem(DEFAULT_TEST_TABLE_NAME,
                DEFAULT_TEST_ITEMS, DEFAULT_TEST_CONDITION_EXPRESSION, false);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenNullTableName() {
        // given
        String tableName = null;

        // when
        underTest.putItem(tableName, DEFAULT_TEST_ITEMS);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenEmptyTableName() {
        // given
        String tableName = " ";

        // when
        underTest.putItem(tableName, DEFAULT_TEST_ITEMS);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenNullItems() {
        // given
        Map<String, AttributeValue> items = null;

        // when
        underTest.putItem(DEFAULT_TEST_TABLE_NAME, items);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenEmptyItems() {
        // given
        Map<String, AttributeValue> items = ImmutableMap.of();

        // when
        underTest.putItem(DEFAULT_TEST_TABLE_NAME, items);
    }

    @Test
    public void testPutItemShouldReturnPutItemResultWhenGivenValidTableNameAndItems() {
        // given
        PutItemResult expected = new PutItemResult();
        when(dynamoDb
                .putItem(new PutItemRequest(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_ITEMS)))
                        .thenReturn(expected);
        // when
        PutItemResult actual = underTest.putItem(DEFAULT_TEST_TABLE_NAME,
                DEFAULT_TEST_ITEMS);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPutItemShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        PutItemRequest request = null;

        // when
        underTest.putItem(request);
    }

    @Test
    public void testPutItemShouldReturnPutItemResultWhenGivenPutItemRequest() {
        // given
        PutItemRequest request = new PutItemRequest();
        PutItemResult expected = new PutItemResult();
        when(dynamoDb.putItem(request)).thenReturn(expected);

        // when
        PutItemResult actual = underTest.putItem(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetItemShouldThrowIllegalArgumentExceptionWhenGivenNullTableName() {
        // given
        String tableName = null;

        // when
        underTest.getItem(tableName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetItemShouldThrowIllegalArgumentExceptionWhenGivenEmptyTableName() {
        // given
        String tableName = " ";

        // when
        underTest.getItem(tableName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetItemShouldThrowIllegalArgumentExceptionWhenGivenNullItems() {
        // given
        Map<String, AttributeValue> key = null;

        // when
        underTest.getItem(DEFAULT_TEST_TABLE_NAME, key);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetItemShouldThrowIllegalArgumentExceptionWhenGivenEmptyItems() {
        // given
        Map<String, AttributeValue> key = ImmutableMap.of();

        // when
        underTest.getItem(DEFAULT_TEST_TABLE_NAME, key);
    }

    @Test
    public void testGetItemShouldReturnGetItemResultWhenGivenValidTableNameAndItems() {
        // given
        GetItemResult expected = new GetItemResult();
        when(dynamoDb
                .getItem(new GetItemRequest(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_KEY)))
                        .thenReturn(expected);
        // when
        GetItemResult actual = underTest.getItem(DEFAULT_TEST_TABLE_NAME,
                DEFAULT_TEST_KEY);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetItemShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        GetItemRequest request = null;

        // when
        underTest.getItem(request);
    }

    @Test
    public void testGetItemShouldReturnGetItemResultWhenGivenGetItemRequest() {
        // given
        GetItemRequest request = new GetItemRequest();
        GetItemResult expected = new GetItemResult();
        when(dynamoDb.getItem(request)).thenReturn(expected);

        // when
        GetItemResult actual = underTest.getItem(request);

        // then
        assertSame(expected, actual);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testUpdateItemShouldThrowIllegalArgumentExceptionWhenGivenNullTableName() {
        // given
        String tableName = null;

        // when
        underTest.updateItem(tableName, DEFAULT_TEST_KEY, DEFAULT_TEST_UPDATES,
                DEFAULT_TEST_RETURN_VALUES);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testUpdateItemShouldThrowIllegalArgumentExceptionWhenGivenEmptyTableName() {
        // given
        String tableName = " ";

        // when
        underTest.updateItem(tableName, DEFAULT_TEST_KEY, DEFAULT_TEST_UPDATES,
                DEFAULT_TEST_RETURN_VALUES);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testUpdateItemShouldThrowIllegalArgumentExceptionWhenGivenNullKey() {
        // given
        Map<String, AttributeValue> key = null;

        // when
        underTest.updateItem(DEFAULT_TEST_TABLE_NAME, key, DEFAULT_TEST_UPDATES,
                DEFAULT_TEST_RETURN_VALUES);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testUpdateItemShouldThrowIllegalArgumentExceptionWhenGivenEmptyKey() {
        // given
        Map<String, AttributeValue> key = ImmutableMap.of();

        // when
        underTest.updateItem(DEFAULT_TEST_TABLE_NAME, key, DEFAULT_TEST_UPDATES,
                DEFAULT_TEST_RETURN_VALUES);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testUpdateItemShouldThrowIllegalArgumentExceptionWhenGivenNullUpdates() {
        // given
        Map<String, AttributeValueUpdate> updates = null;

        // when
        underTest.updateItem(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_KEY, updates,
                DEFAULT_TEST_RETURN_VALUES);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testUpdateItemShouldThrowIllegalArgumentExceptionWhenGivenEmptyUpdates() {
        // given
        Map<String, AttributeValueUpdate> updates = ImmutableMap.of();

        // when
        underTest.updateItem(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_KEY, updates,
                DEFAULT_TEST_RETURN_VALUES);
    }

    @SuppressWarnings("deprecation")
    @Test(expected = IllegalArgumentException.class)
    public void testUpdateItemShouldThrowIllegalArgumentExceptionWhenGivenNullReturnValue() {
        // given
        ReturnValue returnValue = null;

        // when
        underTest.updateItem(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_KEY,
                DEFAULT_TEST_UPDATES, returnValue);
    }

    @SuppressWarnings("deprecation")
    @Test
    public void testUpdateItemShouldReturnUpdateItemResultWhenGivenValidParameters() {
        // given
        UpdateItemResult expected = new UpdateItemResult();
        when(dynamoDb.updateItem(new UpdateItemRequest(DEFAULT_TEST_TABLE_NAME,
                DEFAULT_TEST_KEY, DEFAULT_TEST_UPDATES, DEFAULT_TEST_RETURN_VALUES)))
                        .thenReturn(expected);
        // when
        UpdateItemResult actual = underTest.updateItem(DEFAULT_TEST_TABLE_NAME,
                DEFAULT_TEST_KEY, DEFAULT_TEST_UPDATES, DEFAULT_TEST_RETURN_VALUES);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUpdateItemShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        UpdateItemRequest request = null;

        // when
        underTest.updateItem(request);
    }

    @Test
    public void testUpdateItemShouldReturnUpdateItemResultWhenGivenUpdateItemRequest() {
        // given
        UpdateItemRequest request = new UpdateItemRequest();
        UpdateItemResult expected = new UpdateItemResult();
        when(dynamoDb.updateItem(request)).thenReturn(expected);

        // when
        UpdateItemResult actual = underTest.updateItem(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteItemShouldThrowIllegalArgumentExceptionWhenGivenNullTableName() {
        // given
        String tableName = null;

        // when
        underTest.deleteItem(tableName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteItemShouldThrowIllegalArgumentExceptionWhenGivenEmptyTableName() {
        // given
        String tableName = " ";

        // when
        underTest.deleteItem(tableName, DEFAULT_TEST_KEY);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteItemShouldThrowIllegalArgumentExceptionWhenGivenNullItems() {
        // given
        Map<String, AttributeValue> key = null;

        // when
        underTest.deleteItem(DEFAULT_TEST_TABLE_NAME, key);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteItemShouldThrowIllegalArgumentExceptionWhenGivenEmptyItems() {
        // given
        Map<String, AttributeValue> key = ImmutableMap.of();

        // when
        underTest.deleteItem(DEFAULT_TEST_TABLE_NAME, key);
    }

    @Test
    public void testDeleteItemShouldReturnDeleteItemResultWhenGivenValidTableNameAndItems() {
        // given
        DeleteItemResult expected = new DeleteItemResult();
        when(dynamoDb.deleteItem(
                new DeleteItemRequest(DEFAULT_TEST_TABLE_NAME, DEFAULT_TEST_KEY)))
                        .thenReturn(expected);
        // when
        DeleteItemResult actual = underTest.deleteItem(DEFAULT_TEST_TABLE_NAME,
                DEFAULT_TEST_KEY);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteItemShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        DeleteItemRequest request = null;

        // when
        underTest.deleteItem(request);
    }

    @Test
    public void testDeleteItemShouldReturnDeleteItemResultWhenGivenDeleteItemRequest() {
        // given
        DeleteItemRequest request = new DeleteItemRequest();
        DeleteItemResult expected = new DeleteItemResult();
        when(dynamoDb.deleteItem(request)).thenReturn(expected);

        // when
        DeleteItemResult actual = underTest.deleteItem(request);

        // then
        assertSame(expected, actual);
    }

}
