/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.elasticsearch;

import com.amazonaws.util.IOUtils;

import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchBulkOperationResponse;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchOperationResponse;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchSearchResponse;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ElasticsearchClient}.
 *
 * <AUTHOR> Shin
 */
@RunWith(MockitoJUnitRunner.class)
public class ElasticsearchClientTest {

    private static final String SOURCE = "source";
    private static final String ID = "id";
    private static final String TYPE = "type";
    private static final String INDEX = "index";
    private static final String REQUEST_SEARCH_PATH = "/_all/_search";
    private static final String REQUEST_BULK_API_PATH = "/_bulk";
    private static final String ERROR_MESSAGE = "Invalid String parameter(s) for Elasticsearch API";

    @InjectMocks @Spy
    private ElasticsearchClient underTest;

    @Mock
    private RestClient client;

    @Test
    public void testCheckParametersShouldNotThrowExceptionWhenGivenParametersAreNotNull() {
        // given
        String parameter1 = "abc";
        String parameter2 = "def";

        // when
        underTest.checkParameters(parameter1, parameter2);
    }

    @Test
    public void testCheckParametersShouldThrowExceptionWhenGivenFirstParameterIsNull() {
        // given
        String parameter1 = null;
        String parameter2 = "def";

        // when
        try {
            underTest.checkParameters(parameter1, parameter2);
            fail();

            // then
        } catch (IllegalArgumentException e) {
            assertEquals(ERROR_MESSAGE, e.getMessage());
        }
    }

    @Test
    public void testCheckParametersShouldThrowExceptionWhenGivenFirstParameterIsEmpty() {
        // given
        String parameter1 = "";
        String parameter2 = "def";

        // when
        try {
            underTest.checkParameters(parameter1, parameter2);
            fail();

            // then
        } catch (IllegalArgumentException e) {
            assertEquals(ERROR_MESSAGE, e.getMessage());
        }
    }

    @Test
    public void testCheckParametersShouldThrowExceptionWhenGivenSecondParameterIsNull() {
        // given
        String parameter1 = "abc";
        String parameter2 = null;

        // when
        try {
            underTest.checkParameters(parameter1, parameter2);
            fail();

            // then
        } catch (IllegalArgumentException e) {
            assertEquals(ERROR_MESSAGE, e.getMessage());
        }
    }

    @Test
    public void testCheckParametersShouldThrowExceptionWhenGivenSecondParameterIsEmpty() {
        // given
        String parameter1 = "abc";
        String parameter2 = "";

        // when
        try {
            underTest.checkParameters(parameter1, parameter2);
            fail();

            // then
        } catch (IllegalArgumentException e) {
            assertEquals(ERROR_MESSAGE, e.getMessage());
        }
    }

    @Test
    public void testIndexDocumentShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        ElasticsearchOperationResponse expected = mock(
                ElasticsearchOperationResponse.class);
        ArgumentCaptor<Request> captor = ArgumentCaptor.forClass(Request.class);
        Response response = mock(Response.class);
        when(client.performRequest(captor.capture())).thenReturn(response);
        String json = "json";
        doReturn(json).when(underTest).toString(response);
        doReturn(expected).when(underTest).fromJson(json,
                ElasticsearchOperationResponse.class);

        // when
        ElasticsearchOperationResponse actual = underTest.indexDocument(INDEX, TYPE,
                ID, SOURCE);

        // then
        assertSame(expected, actual);
        Request actualRequest = captor.getValue();
        assertNotNull(actualRequest);
        assertEquals("POST", actualRequest.getMethod());
        assertEquals("/index/type/id", actualRequest.getEndpoint());
        assertEquals(SOURCE, IOUtils.toString(actualRequest.getEntity().getContent()));
    }

    @Test
    public void testDeleteDocumentShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        ElasticsearchOperationResponse expected = mock(
                ElasticsearchOperationResponse.class);
        ArgumentCaptor<Request> captor = ArgumentCaptor.forClass(Request.class);
        Response response = mock(Response.class);
        when(client.performRequest(captor.capture())).thenReturn(response);
        String json = "json";
        doReturn(json).when(underTest).toString(response);
        doReturn(expected).when(underTest).fromJson(json,
                ElasticsearchOperationResponse.class);

        // when
        ElasticsearchOperationResponse actual = underTest.deleteDocument(INDEX, TYPE,
                ID);

        // then
        assertSame(expected, actual);
        Request actualRequest = captor.getValue();
        assertNotNull(actualRequest);
        assertEquals("DELETE", actualRequest.getMethod());
        assertEquals("/index/type/id", actualRequest.getEndpoint());
    }

    @Test
    public void testFindDocumentByShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        ElasticsearchSearchResponse expected = mock(ElasticsearchSearchResponse.class);
        ArgumentCaptor<Request> captor = ArgumentCaptor.forClass(Request.class);
        Response response = mock(Response.class);
        when(client.performRequest(captor.capture())).thenReturn(response);
        String json = "json";
        doReturn(json).when(underTest).toString(response);
        doReturn(expected).when(underTest).fromJson(json,
                ElasticsearchSearchResponse.class);

        // when
        ElasticsearchSearchResponse actual = underTest.findDocumentBy(SOURCE);

        // then
        assertSame(expected, actual);
        Request actualRequest = captor.getValue();
        assertNotNull(actualRequest);
        assertEquals("GET", actualRequest.getMethod());
        assertEquals(REQUEST_SEARCH_PATH, actualRequest.getEndpoint());
        assertEquals(SOURCE, IOUtils.toString(actualRequest.getEntity().getContent()));
    }

    @Test
    public void testIndexBulkDocumentsShouldReturnCorrectDataWhenCalled()
            throws Exception {
        // given
        String bulkApiBodies = "bulkApiBodies";
        ElasticsearchBulkOperationResponse expected = mock(
                ElasticsearchBulkOperationResponse.class);
        ArgumentCaptor<Request> captor = ArgumentCaptor.forClass(Request.class);
        Response response = mock(Response.class);
        when(client.performRequest(captor.capture())).thenReturn(response);
        String json = "json";
        doReturn(json).when(underTest).toString(response);
        doReturn(expected).when(underTest).fromJson(json,
                ElasticsearchBulkOperationResponse.class);

        // when
        ElasticsearchBulkOperationResponse actual = underTest
                .indexBulkDocuments(bulkApiBodies);

        // then
        assertSame(expected, actual);
        Request actualRequest = captor.getValue();
        assertNotNull(actualRequest);
        assertEquals("POST", actualRequest.getMethod());
        assertEquals(REQUEST_BULK_API_PATH, actualRequest.getEndpoint());
        assertEquals(bulkApiBodies,
                IOUtils.toString(actualRequest.getEntity().getContent()));
    }
}
