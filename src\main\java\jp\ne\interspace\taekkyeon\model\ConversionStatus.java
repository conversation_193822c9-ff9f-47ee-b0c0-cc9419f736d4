/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding all the various conversion statuses.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum ConversionStatus implements ValueEnum {

    PENDING(0),
    APPROVED(1),
    REJECTED(2);

    private final int value;
}
