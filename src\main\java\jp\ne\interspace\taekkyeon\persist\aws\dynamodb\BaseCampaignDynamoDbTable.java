/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.dynamodb;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.AttributeValueUpdate;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.ProvisionedThroughput;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;

import lombok.Getter;

import jp.ne.interspace.taekkyeon.model.CampaignStatus;
import jp.ne.interspace.taekkyeon.module.Country;

import static com.amazonaws.services.dynamodbv2.model.KeyType.HASH;
import static com.google.common.collect.ImmutableMap.of;
import static java.lang.String.join;
import static java.util.Arrays.asList;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Parent class of campaign dynamoDB table.
 *
 * <AUTHOR> Ferreras
 */
public class BaseCampaignDynamoDbTable extends DynamoDbTable {

    public static final String KEY_NAME = "CAMPAIGN_NO";
    public static final String DYNAMODB_KEY_CAMPAIGN_STATUS = "CAMPAIGN_STATUS";

    private static final String PARTIAL_TABLE_NAME = "CAMPAIGN";

    @Getter
    private final String keyName = KEY_NAME;

    @Getter
    private final String partialName = PARTIAL_TABLE_NAME;

    @Getter
    private Function<String, ImmutableMap<String, AttributeValue>>
            keyResolver = key -> of(KEY_NAME, numberOf(key));

    private Country country;

    public BaseCampaignDynamoDbTable(Country country) {
        this.country = country;
    }

    @Override
    public CreateTableRequest getTestSchema() {
        return new CreateTableRequest(
              asList(new AttributeDefinition(KEY_NAME, NUMERIC_TYPE)),
              getTableName(),
              asList(new KeySchemaElement(KEY_NAME, HASH)),
              new ProvisionedThroughput(DEFAULT_CAPACITY_UNITS, DEFAULT_CAPACITY_UNITS));
    }

    @Override
    public String getTableName() {
        String concatenator = "_";
        return getTableNameBy(concatenator, country.getCode()) + getTestSeedIfAvaliable();
    }

    /**
     * Update campaign status.
     *
     * @param campaignId
     *          the given campaign ID
     * @param campaignStatus
     *          the given campaign status
     */
    public void updateCampaignStatus(long campaignId, CampaignStatus campaignStatus) {
        updateItem(createUpdateKey(campaignId),
                createCampaignStatusUpdate(campaignStatus));
    }

    @VisibleForTesting
    Map<String, AttributeValue> createUpdateKey(long campaignId) {
        return ImmutableMap.of(KEY_NAME, numberOf(String.valueOf(campaignId)));
    }

    @VisibleForTesting
    Map<String, AttributeValueUpdate> createCampaignStatusUpdate(
            CampaignStatus campaignStatus) {
        Map<String, AttributeValueUpdate> updateValues = new HashMap<>();
        updateValues.put(DYNAMODB_KEY_CAMPAIGN_STATUS, new AttributeValueUpdate()
                .withValue(numberOf(String.valueOf(campaignStatus.getValue()))));
        return updateValues;
    }

    private String getTableNameBy(String concatenator, String countryCode) {
        switch (getCurrentEnvironment()) {
            case PRODUCTION:
                return join(concatenator, countryCode, partialName);
            case STAGING:
                return join(concatenator, STAGING.name(), countryCode, partialName);
            case DEV:
            // let-through to default
            default:
                return join(concatenator, DEV.name(), partialName);
        }
    }
}
