/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum holding the publisher account types.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum PublisherAccountType implements ValueEnum {

    INDIVIDUAL(0),
    LOCAL_COMPANY(1),
    OVERSEAS_COMPANY(2),
    OVERSEAS_COMPANY_COD(3),
    COMPANY_VAT(4),
    OVERSEAS_INDIVIDUAL(5),
    INFLUENCER(6);

    private final int value;
}
