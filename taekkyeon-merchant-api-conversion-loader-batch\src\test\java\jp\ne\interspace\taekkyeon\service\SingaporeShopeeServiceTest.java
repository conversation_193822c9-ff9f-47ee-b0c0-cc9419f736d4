/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.google.gson.JsonParser;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import jp.ne.interspace.taekkyeon.model.ClickConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.Merchant;
import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryInsertRequest;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;

import static java.math.BigDecimal.ZERO;
import static java.time.ZoneId.of;
import static java.time.ZonedDateTime.of;
import static java.util.Arrays.asList;
import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.REGULAR;
import static jp.ne.interspace.taekkyeon.model.Merchant.SHOPEE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

/**
 * Unit test for {@link SingaporeShopeeService}.
 *
 * <AUTHOR> Nguyen
 */
@RunWith(MockitoJUnitRunner.class)
public class SingaporeShopeeServiceTest {

    private static final long CAMPAIGN_ID = 123L;
    private static final ZoneId ZONE_ID = of("Asia/Singapore");
    private static final int PRODUCT_RESULT_ID = 3;
    private static final int CATEGORY_RESULT_ID = 30;
    private static final String CUSTOMER_TYPE = "customerType";
    private static final String CLICK_ID = "clickId";
    private static final String CHECKOUT_ID = "checkoutId";
    private static final String CONVERSION_ID = "conversionId";
    private static final BigDecimal GROSS_COMMISSION = new BigDecimal("400");
    private static final ZonedDateTime TIME1 = of(2023, 7, 17, 23, 58, 20, 0, ZONE_ID);
    private static final long PURCHASE_TIME2 = 1589613100;
    private static final ZonedDateTime TIME2 = of(2020, 5, 16, 15, 11, 40, 0, ZONE_ID);
    private static final BigDecimal CAPPED_COMMISSION = new BigDecimal("410");
    private static final BigDecimal TOTAL_BRAND_COMMISSION = new BigDecimal("420");
    private static final BigDecimal ESTIMATED_TOTAL_COMMISSION = new BigDecimal("430");
    private static final BigDecimal ITEM_PRICE = new BigDecimal("100");
    private static final BigDecimal ACTUAL_AMOUNT = new BigDecimal("110");
    private static final BigDecimal ITEM_COMMISSION = new BigDecimal("10");
    private static final BigDecimal GROSS_BRAND_COMMISSION = new BigDecimal("20");
    private static final String MODEL_ID = "modelId";
    private static final String GLOBAL_CATEGORY_LV1_NAME = "category, name";
    private static final String BUYER_TYPE = "EXISTING";
    private static final String UTM_CONTENT = "12345-RKVALUEQQQ-url";
    private static final String UTM_CONTENT_954 = "12345-RKVALUEQQQ-url-ca75910166da03ff9d4655a0338e6b09";
    private static final String MD5_HASHED_CAMPAIGN_ID_954 = "ca75910166da03ff9d4655a0338e6b09";
    private static final String ITEM_ID = "id";
    private static final Merchant SHOPEE_MERCHANT = SHOPEE;
    private static final ZonedDateTime START_DATE_TIME = of(2024, 7, 26, 0, 0, 0, 0, ZONE_ID);
    private static final ZonedDateTime END_DATE_TIME = of(2024, 7, 31, 23, 59, 59, 0, ZONE_ID);
    private static final String ORDERED_IN_SAME_SHOP = "ORDERED_IN_SAME_SHOP";
    private static final BigDecimal ITEM_SELLER_COMMISSION = new BigDecimal("20");
    private static final String SHOP_ID = "shopId";
    private static final String GLOBAL_CATEGORY_LV_3_NAME = "globalCategoryLv3Name";

    @InjectMocks @Spy
    private SingaporeShopeeService underTest;

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenGrossBrandCommissionIsGreaterThanZero()
            throws Exception {
        // give
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, SHOP_ID, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, GROSS_BRAND_COMMISSION, null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, GLOBAL_CATEGORY_LV_3_NAME,
                ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                asList(shopeeItem));
        ConversionRegistrationDetails expected1 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, CUSTOMER_TYPE, "category name",
                "orderId_shopId_id_modelId_globalCategoryLv3Name_ShopeeComm",
                ACTUAL_AMOUNT, CLICK_ID);
        ConversionRegistrationDetails expected2 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, PRODUCT_RESULT_ID, CUSTOMER_TYPE, "Brand Commission",
                "orderId_shopId_id_modelId_globalCategoryLv3Name_XTRAComm",
                GROSS_BRAND_COMMISSION, CLICK_ID);
        ConversionRegistrationDetails expected3 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, PRODUCT_RESULT_ID, CUSTOMER_TYPE, "Brand Commission",
                "orderId_shopId_id_modelId_globalCategoryLv3Name_XTRAComm",
                GROSS_BRAND_COMMISSION, CLICK_ID);
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).validateDateTimeAfter(TIME1);
        doReturn(false).when(underTest).isBrandItem(SHOP_ID);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        ClickConversionRegistrationDetails subConversion = new ClickConversionRegistrationDetails(
                TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID, CUSTOMER_TYPE, "category name",
                "orderId_shopId_id_modelId_globalCategoryLv3Name_ShopeeComm",
                ACTUAL_AMOUNT, CLICK_ID, 123L);
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>(
                Arrays.asList(subConversion));
        doReturn(brandItemConversions).when(underTest)
                .processConversionByBrandItemRules(TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID,
                        CUSTOMER_TYPE, "category name",
                        "orderId_shopId_id_modelId_globalCategoryLv3Name_ShopeeComm",
                        ACTUAL_AMOUNT, CLICK_ID, SHOP_ID, false);

        doReturn("SG").when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID, shopeeOrder);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        assertEquals(expected1, actual.get(0));
        assertEquals(expected2, actual.get(1));
        assertEquals(expected3, actual.get(2));
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenGrossBrandCommissionIsZero()
            throws Exception {
        // give
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, SHOP_ID, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, ZERO, null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, GLOBAL_CATEGORY_LV_3_NAME,
                ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                asList(shopeeItem));
        ConversionRegistrationDetails expected = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, CUSTOMER_TYPE, "category name",
                "orderId_shopId_id_modelId_globalCategoryLv3Name_ShopeeComm",
                ACTUAL_AMOUNT, CLICK_ID);
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).validateDateTimeAfter(TIME1);
        doReturn(false).when(underTest).isBrandItem(SHOP_ID);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID, shopeeOrder);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertEquals(asList(expected), actual);
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenItemSellerCommissionIsGreaterThanZero()
            throws Exception {
        // give
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, SHOP_ID, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, GLOBAL_CATEGORY_LV_3_NAME,
                ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                asList(shopeeItem));
        ConversionRegistrationDetails expected1 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, CUSTOMER_TYPE, "category name",
                "orderId_shopId_id_modelId_globalCategoryLv3Name_ShopeeComm",
                ACTUAL_AMOUNT, CLICK_ID);
        ConversionRegistrationDetails expected2 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, PRODUCT_RESULT_ID, CUSTOMER_TYPE, "Brand Commission",
                "orderId_shopId_id_modelId_globalCategoryLv3Name_XTRAComm",
                ITEM_SELLER_COMMISSION, CLICK_ID);
        ConversionRegistrationDetails expected3 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, PRODUCT_RESULT_ID, CUSTOMER_TYPE, "Brand Commission",
                "orderId_shopId_id_modelId_globalCategoryLv3Name_XTRAComm",
                ITEM_SELLER_COMMISSION, CLICK_ID);
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).validateDateTimeAfter(TIME1);
        doReturn(false).when(underTest).isBrandItem(SHOP_ID);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        ClickConversionRegistrationDetails subConversion =
                new ClickConversionRegistrationDetails(TIME1, CHECKOUT_ID,
                        CATEGORY_RESULT_ID, CUSTOMER_TYPE, "category name",
                        "orderId_shopId_id_modelId_globalCategoryLv3Name_ShopeeComm",
                        ACTUAL_AMOUNT, CLICK_ID, 123L);
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>(
                Arrays.asList(subConversion));
        doReturn(brandItemConversions).when(underTest)
                .processConversionByBrandItemRules(TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID,
                        CUSTOMER_TYPE, "category name",
                        "orderId_shopId_id_modelId_globalCategoryLv3Name_ShopeeComm",
                        ACTUAL_AMOUNT, CLICK_ID, SHOP_ID, false);

        doReturn("SG").when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME1, CHECKOUT_ID, shopeeOrder);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        assertEquals(expected1, actual.get(0));
        assertEquals(expected2, actual.get(1));
        assertEquals(expected3, actual.get(2));
    }

    @Test
    public void testCreateConversionDetailsShouldNotReturnDataDuplicateWhenIsDuplicateConversionCheckEnabledAndDuplicateConversion()
            throws Exception {
        // give
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, null, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                asList(shopeeItem));
        MerchantIntegrationHistoryInsertRequest request = mock(
                MerchantIntegrationHistoryInsertRequest.class);
        String key = "0-20200516-checkoutId-orderId-id-modelId";
        doReturn(true).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(true).when(underTest).isDuplicateConversion(key);
        doReturn(0L).when(underTest).getCampaignId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME2, CHECKOUT_ID, shopeeOrder);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.size());
        verify(underTest, never()).insertDataProceeded(request);
    }

    @Test
    public void testParseShouldCallCreateConversionDetailsMethodWhenIsTargetCampaignIdTrue()
            throws Exception {
        // given
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType", null);
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION, PURCHASE_TIME2, CAPPED_COMMISSION,
                TOTAL_BRAND_COMMISSION, ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE,
                UTM_CONTENT_954, null, null, asList(shopeeOrder));
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(
                asList(shopeeNode), null);
        String responseBody = "{}";
        doReturn(conversionReport).when(underTest).parseConversionReport(
                new JsonParser().parse(responseBody).getAsJsonObject());
        List<ConversionRegistrationDetails> details = asList(
                mock(ConversionRegistrationDetails.class));

        long campaignId = 954;
        doReturn(campaignId).when(underTest).getCampaignId();
        doReturn(MD5_HASHED_CAMPAIGN_ID_954).when(underTest).hashMd5By(campaignId);
        doReturn(details).when(underTest).createConversionDetails("RKVALUEQQQ",
                "existing", TIME2, "checkoutId-orderId", shopeeOrder);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(responseBody);

        // then
        assertNotNull(actual);
        assertEquals(details, actual);
        verify(underTest).createConversionDetails("RKVALUEQQQ", "existing", TIME2,
                "checkoutId-orderId", shopeeOrder);
    }

    @Test
    public void testParseShouldCallCreateConversionDetailsMethodWhenIsTargetCampaignIdFalse()
            throws Exception {
        // given
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType", null);
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION, PURCHASE_TIME2, CAPPED_COMMISSION,
                TOTAL_BRAND_COMMISSION, ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE,
                UTM_CONTENT, null, null, asList(shopeeOrder));
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(
                asList(shopeeNode), null);
        String responseBody = "{}";
        doReturn(conversionReport).when(underTest).parseConversionReport(
                new JsonParser().parse(responseBody).getAsJsonObject());

        long campaignId = 954;
        doReturn(campaignId).when(underTest).getCampaignId();
        doReturn(MD5_HASHED_CAMPAIGN_ID_954).when(underTest).hashMd5By(campaignId);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(responseBody);

        // then
        assertNotNull(actual);
        verify(underTest, never()).createConversionDetails("RKVALUEQQQ", "existing",
                TIME2, "checkoutId-orderId", shopeeOrder);
    }

    @Test
    public void testParseShouldReturnEmptyListWhenResponseBodyIsNull() throws Exception {
        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(null);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testCheckDateTimeShouldReturnTrueWhenGivenConversionTimeInTheRange()
            throws Exception {
        // given
        ZonedDateTime conversionTime = of(2024, 7, 26, 0, 0, 0, 0, ZONE_ID);
        doReturn(START_DATE_TIME).when(underTest).getStartDateTime();
        doReturn(END_DATE_TIME).when(underTest).getEndDateTime();

        // when
        boolean actual = underTest.checkDateTime(conversionTime);

        // then
        assertTrue(actual);
    }

    @Test
    public void testCheckDateTimeShouldReturnFalseWhenGivenConversionTimeNotInTheRange()
            throws Exception {
        // given
        ZonedDateTime conversionTime = of(2024, 8, 1, 0, 0, 0, 0, ZONE_ID);
        doReturn(START_DATE_TIME).when(underTest).getStartDateTime();
        doReturn(END_DATE_TIME).when(underTest).getEndDateTime();

        // when
        boolean actual = underTest.checkDateTime(conversionTime);

        // then
        assertFalse(actual);
    }
}
