/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.RecommendedCampaignType;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler.ValueEnumTypeHandler;

/**
 * MyBatis {@link TypeHandler} for {@link RecommendedCampaignType}.
 *
 * <AUTHOR>
 */
@MappedTypes(RecommendedCampaignType.class)
public class RecommendedCampaignTypeHandler
        extends ValueEnumTypeHandler<RecommendedCampaignType> {

    private static final RecommendedCampaignType DEFAULT_RECOMMENDED_CAMPAIGN_TYPE = RecommendedCampaignType.FAST_GROWING;

    public RecommendedCampaignTypeHandler() {
        super(RecommendedCampaignType.class, DEFAULT_RECOMMENDED_CAMPAIGN_TYPE);
    }

    /**
     * Returns the system default {@link RecommendedCampaignType}.
     *
     * @return system default {@link DeviceType}
     */
    public static RecommendedCampaignType getDefaultDeviceType() {
        return DEFAULT_RECOMMENDED_CAMPAIGN_TYPE;
    }
}
