/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding the payload of a {@link SqsRecord}.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @ToString
public class SqsRecordPayload {

    private final String receiptHandle;
    private final String message;
}
