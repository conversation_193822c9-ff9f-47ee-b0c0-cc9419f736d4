/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.EnumSet;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.ValueEnum;

/**
 * {@link TypeHandler} for mapping {@link ValueEnum}s with MyBatis. Descendants should
 * fill up the type-specific data.
 *
 * <AUTHOR>
 * @param <E>
 *            the exact type of the handled {@link ValueEnum}
 */
@RequiredArgsConstructor
public abstract class ValueEnumTypeHandler<E extends Enum<E> & ValueEnum>
        implements TypeHandler<E> {

    private final @NonNull Class<E> handledType;
    private final @NonNull E defaultElement;

    @Override
    public void setParameter(PreparedStatement ps, int index, E parameter,
            JdbcType jdbcType) throws SQLException {
        if (parameter != null) {
            ps.setInt(index, parameter.getValue());
        } else {
            ps.setNull(index, jdbcType.TYPE_CODE);
        }
    }

    @Override
    public E getResult(ResultSet rs, String columnName) throws SQLException {
        int rawValue = rs.getInt(columnName);
        return transform(rawValue);
    }

    @Override
    public E getResult(ResultSet rs, int columnIndex) throws SQLException {
        int rawValue = rs.getInt(columnIndex);
        return transform(rawValue);
    }

    @Override
    public E getResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        int rawValue = cs.getInt(columnIndex);
        return transform(rawValue);
    }

    private E transform(int rawValue) {
        return EnumSet.allOf(handledType).stream()
                .filter((element) -> element.getValue() == rawValue).findFirst()
                .orElse(defaultElement);
    }
}
