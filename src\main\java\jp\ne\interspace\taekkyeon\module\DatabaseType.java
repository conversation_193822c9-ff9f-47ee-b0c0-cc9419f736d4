/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.lang.annotation.Annotation;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Database type for mybatis.
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor @Getter
public enum DatabaseType {

    ORACLE(OracleResolver.class),
    REDSHIFT(RedshiftResolver.class),
    MARIADB(MariaDbResolver.class),
    DYNAMODB(null);

    private final Class<? extends Annotation> annotationType;
}
