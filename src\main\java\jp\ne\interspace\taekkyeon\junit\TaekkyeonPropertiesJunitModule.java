/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.junit;

import com.google.inject.AbstractModule;

import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.module.Environment;
import jp.ne.interspace.taekkyeon.module.LogType;
import jp.ne.interspace.taekkyeon.module.TaekkyeonPropertiesModule;

import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.LogType.IMPRESSION;

/**
 * Guice module for the various system properties in JUnit tests.
 *
 * <AUTHOR> Varga
 */
public class TaekkyeonPropertiesJunitModule extends AbstractModule {

    private static final String VM_ARG_NAME_FOR_TEST_COUNTRY = "country";
    private static final String VM_ARG_NAME_FOR_TEST_ENVIRONMENT = "environment";
    private static final String VM_ARG_NAME_FOR_TEST_BATCH_NAME = "batchName";
    private static final String VM_ARG_NAME_FOR_TEST_LOG_TYPE = "logType";

    private static final Country DEFAULT_TEST_COUNTRY = THAILAND;
    private static final Environment DEFAULT_TEST_ENVIRONMENT = DEV;
    private static final String TEST_BATCH_NAME = "testBatch";
    private static final LogType DEFAULT_TEST_LOG_TYPE = IMPRESSION;

    public static final String SYSTEM_PROPERTY_NAME_FOR_TEST_SEED = "test_seed";
    public static final String SYSTEM_PROPERTY_NAME_FOR_IS_USED_S3_TEST_SEED =
            "is_used_s3_test_seed";

    @Override
    protected void configure() {
        install(new TaekkyeonPropertiesModule());
        setSystemProperties();
    }

    private void setSystemProperties() {
        System.setProperty(VM_ARG_NAME_FOR_TEST_COUNTRY, getCountry().getCode());
        System.setProperty(VM_ARG_NAME_FOR_TEST_ENVIRONMENT, getEnvironment().name());
        System.setProperty(VM_ARG_NAME_FOR_TEST_LOG_TYPE, getLogType().getCode());
        setTestBatchName();
        setTestSeed();
    }

    private void setTestSeed() {
        System.setProperty(SYSTEM_PROPERTY_NAME_FOR_TEST_SEED, generateSeed(16));
        System.setProperty(SYSTEM_PROPERTY_NAME_FOR_IS_USED_S3_TEST_SEED, "true");
    }

    private String generateSeed(int length) {
        return "_" + new StringHelper().generateRandomAlphanumericString(length);
    }

    private Country getCountry() {
        Country country = null;
        try {
            country = Country.getCurrentCountry();
        } catch (Exception e) {
            country = DEFAULT_TEST_COUNTRY;
        }
        return country;
    }

    private Environment getEnvironment() {
        Environment environment = null;
        try {
            environment = Environment.getCurrentEnvironment();
        } catch (Exception e) {
            environment = DEFAULT_TEST_ENVIRONMENT;
        }
        return environment;
    }

    private LogType getLogType() {
        LogType logType = null;
        try {
            logType = LogType.getCurrentLogType();
        } catch (Exception e) {
            logType = DEFAULT_TEST_LOG_TYPE;
        }
        return logType;
    }

    private void setTestBatchName() {
        if (System.getProperty(VM_ARG_NAME_FOR_TEST_BATCH_NAME) == null) {
            System.setProperty(VM_ARG_NAME_FOR_TEST_BATCH_NAME, TEST_BATCH_NAME);
        }
    }
}
