/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.util.List;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CampaignConditionDetails;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

/**
 * Integration test for {@link CampaignMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class CampaignMapperTest {

    @Inject
    private CampaignMapper underTest;

    @Test
    public void testFindCampaignNameByShouldReturnCorrectNameWhenFoundData() {
        // given
        long campaignId = 3000;

        // when
        String actual = underTest.findCampaignNameBy(campaignId);

        // then
        assertEquals("campaignName", actual);
    }

    @Test
    public void testFindCampaignNameByShouldReturnCorrectNameWhenNotFoundData() {
        // given
        long campaignId = 4000;

        // when
        String actual = underTest.findCampaignNameBy(campaignId);

        // then
        assertNull(actual);
    }

    @Test
    public void testFindCampaignConditionByShouldReturnCorrectNameWhenCalled() {
        // given
        long campaignId = 6320;

        // when
        List<CampaignConditionDetails> actual = underTest
                .findCampaignConditionBy(campaignId);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertFields(actual.get(0), campaignId, "shopId",
                "161143541", null, "AND");
        assertFields(actual.get(1), campaignId, "itemName",
                "noroid", "161143541", "AND");
    }

    private void assertFields(CampaignConditionDetails actual, long campaignId,
            String conditionName, String conditionValues, String groupShopId,
            String relativeClauseOperator) {
        assertNotNull(actual);
        assertEquals(campaignId, actual.getCampaignId());
        assertEquals(conditionName, actual.getConditionName());
        assertEquals(conditionValues, actual.getConditionValues());
        assertEquals(groupShopId, actual.getGroupShopId());
        assertEquals(relativeClauseOperator, actual.getRelativeClauseOperator());
    }
}
