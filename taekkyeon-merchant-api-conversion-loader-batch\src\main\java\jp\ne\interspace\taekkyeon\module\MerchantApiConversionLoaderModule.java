/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import javax.inject.Singleton;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableTable;
import com.google.inject.AbstractModule;
import com.google.inject.Injector;
import com.google.inject.Provides;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Named;

import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.batch.processor.DummyRecordProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.MerchantApiConversionLoaderRecordReader;
import jp.ne.interspace.taekkyeon.batch.writer.MerchantApiConversionLoaderRecordWriter;
import jp.ne.interspace.taekkyeon.model.Merchant;
import jp.ne.interspace.taekkyeon.service.IndonesiaShopeeService;
import jp.ne.interspace.taekkyeon.service.MalaysiaShopeeService;
import jp.ne.interspace.taekkyeon.service.MerchantApiConversionService;
import jp.ne.interspace.taekkyeon.service.PhilippinesShopeeService;
import jp.ne.interspace.taekkyeon.service.SingaporeShopeeService;
import jp.ne.interspace.taekkyeon.service.TaiwanShopeeService;
import jp.ne.interspace.taekkyeon.service.ThailandShopeeService;

import static com.fasterxml.jackson.core.io.NumberInput.parseLong;
import static com.google.common.base.Strings.isNullOrEmpty;
import static com.google.inject.name.Names.named;
import static java.lang.Boolean.parseBoolean;
import static java.lang.Integer.parseInt;
import static java.lang.System.getProperty;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.getPropertyBy;
import static jp.ne.interspace.taekkyeon.model.Merchant.SHOPEE;
import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Country.VIETNAM;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Guice module for the merchant API conversion loader.
 *
 * <AUTHOR> Shin
 */
public class MerchantApiConversionLoaderModule extends AbstractModule {

    public static final String BIND_KEY_CONVERSION_UPDATE_REQUESTS_S3_BUCKET = "conversion.update.requests.s3.bucket";
    public static final String BIND_KEY_MERCHANT = "merchant";
    public static final String BIND_KEY_TARGET_COUNTRY_CODE = "target.country.code";
    public static final String BIND_KEY_CAMPAIGN_ID = "campaign.id";
    public static final String BIND_KEY_SCAN_TIME_FROM = "scan.time.from";
    public static final String BIND_KEY_SCAN_TIME_TO = "scan.time.to";
    public static final String BIND_KEY_API_URL = "api.url";
    public static final String BIND_KEY_SECRET_NAME = "secret.name";
    public static final String BIND_KEY_SLEEP_SECOND = "sleep.second";
    public static final String BIND_KEY_PARTITION_CONVERSION_COUNT = "partition.conversion.count";
    public static final String BIND_KEY_LIMIT_RESET_COUNT = "limit.reset.count";
    public static final String BIND_KEY_LIMIT_TIME_CALL_API = "limit.time.call.api";
    public static final String BIND_KEY_STAFF_ID = "staff.id";
    public static final String BIND_KEY_IS_CHECK_DUPLICATE_CONVERSION = "check.duplicate.conversion";
    public static final String BIND_KEY_RECEIVED_CURRENCY = "received.currency";

    private static final String MERCHANT_VM_ARGUMENT = "merchant";
    private static final String TARGET_COUNTRY_CODE_VM_ARGUMENT = "targetCountryCode";
    private static final String API_SECRET_NAME_VM_ARGUMENT = "apiSecretName";
    private static final String CAMPAIGN_ID_VM_ARGUMENT = "campaignId";
    private static final String SCAN_TIME_FROM_VM_ARGUMENT = "scanTimeFrom";
    private static final String SCAN_TIME_TO_VM_ARGUMENT = "scanTimeTo";
    private static final String SLEEP_SECOND = "sleepSecond";
    private static final String PARTITION_CONVERSION_COUNT = "partitionConversionCount";
    private static final String LIMIT_RESET_COUNT = "limitResetCount";
    private static final String LIMIT_TIME_CALL_API = "limitTimeCallApi";
    public static final String INDONESIA_SHOPEE_DEFAULT_API_KEY = "INDONESIA_SHOPEE_API_KEY";
    public static final String INDONESIA_SHOPEE_API_KEY_STORES = "INDONESIA_SHOPEE_API_KEY_STORES";
    public static final String INDONESIA_SHOPEE_API_KEY_KOLS_TIER2 = "INDONESIA_SHOPEE_API_KEY_KOLS_TIER2";
    private static final String STAFF_ID_VM_ARGUMENT = "staffId";
    private static final String DUPLICATE_CONVERSION_CHECK_ENABLED = "isDuplicateConversionCheckEnabled";
    private static final String RECEIVED_CURRENCY = "receivedCurrency";

    private static final ImmutableTable<String, Merchant, Class<? extends MerchantApiConversionService>> SERVICE_MAPPING = new ImmutableTable.Builder<String, Merchant, Class<? extends MerchantApiConversionService>>()
            .put("ID", SHOPEE, IndonesiaShopeeService.class)
            .put("MY", SHOPEE, MalaysiaShopeeService.class)
            .put("TH", SHOPEE, ThailandShopeeService.class)
            .put("SG", SHOPEE, SingaporeShopeeService.class)
            .put("PH", SHOPEE, PhilippinesShopeeService.class)
            .put("TW", SHOPEE, TaiwanShopeeService.class)
            .build();

    private static final ImmutableTable<String, Merchant, String> URL_MAPPING = new ImmutableTable.Builder<String, Merchant, String>()
            .put("ID", SHOPEE, "https://open-api.affiliate.shopee.co.id/graphql")
            .put("MY", SHOPEE, "https://open-api.affiliate.shopee.com.my/graphql")
            .put("TH", SHOPEE, "https://open-api.affiliate.shopee.co.th/graphql")
            .put("SG", SHOPEE, "https://open-api.affiliate.shopee.sg/graphql")
            .put("PH", SHOPEE, "https://open-api.affiliate.shopee.ph/graphql")
            .put("TW", SHOPEE, "https://open-api.affiliate.shopee.tw/graphql")
            .build();

    private static final ImmutableMap<String, List<Long>> ID_CAMPAIGN_SECRET_NAME_MAPPING = new ImmutableMap.Builder<String, List<Long>>()
            .put(INDONESIA_SHOPEE_DEFAULT_API_KEY, singletonList(966L))
            .put(INDONESIA_SHOPEE_API_KEY_STORES,
                    asList(6081L, 6578L, 6574L, 6536L, 6504L, 6297L, 6302L, 6304L, 6306L,
                            6320L, 6314L, 6487L, 6343L, 6345L))
            .put(INDONESIA_SHOPEE_API_KEY_KOLS_TIER2, singletonList(973L))
            .build();

    private static final ImmutableMap<String, List<Long>> SG_CAMPAIGN_SECRET_NAME_MAPPING = new ImmutableMap.Builder<String, List<Long>>()
            .put("SINGAPORE_SHOPEE_API_KEY", singletonList(954L))
            .build();

    private static final ImmutableMap<String, List<Long>> MY_CAMPAIGN_SECRET_NAME_MAPPING = new ImmutableMap.Builder<String, List<Long>>()
            .put("MALAYSIA_SHOPEE_API_KEY", singletonList(959L))
            .build();

    private static final ImmutableMap<String, List<Long>> TH_CAMPAIGN_SECRET_NAME_MAPPING = new ImmutableMap.Builder<String, List<Long>>()
            .put("THAILAND_SHOPEE_API_KEY", asList(677L, 688L))
            .build();

    private static final ImmutableMap<String, List<Long>> PH_CAMPAIGN_SECRET_NAME_MAPPING = new ImmutableMap.Builder<String, List<Long>>()
            .put("PHILIPPINES_SHOPEE_API_KEY", asList(7022L))
            .build();

    private static final ImmutableMap<String, List<Long>> TW_CAMPAIGN_SECRET_NAME_MAPPING = new ImmutableMap.Builder<String, List<Long>>()
            .put("TAIWAN_SHOPEE_API_KEY", asList(7476L))
            .build();

    private static final ImmutableTable<String, Merchant, Map<String, List<Long>>> SECRET_NAME_MAPPING = new ImmutableTable.Builder<String, Merchant, Map<String, List<Long>>>()
            .put("TH", SHOPEE, TH_CAMPAIGN_SECRET_NAME_MAPPING)
            .put("ID", SHOPEE, ID_CAMPAIGN_SECRET_NAME_MAPPING)
            .put("SG", SHOPEE, SG_CAMPAIGN_SECRET_NAME_MAPPING)
            .put("MY", SHOPEE, MY_CAMPAIGN_SECRET_NAME_MAPPING)
            .put("PH", SHOPEE, PH_CAMPAIGN_SECRET_NAME_MAPPING)
            .put("TW", SHOPEE, TW_CAMPAIGN_SECRET_NAME_MAPPING)
            .build();

    private static final ImmutableTable<Country, Environment, Long> STAFF_ID_MAPPING = new ImmutableTable.Builder<Country, Environment, Long>()
            .put(INDONESIA, DEV, 4L)
            .put(THAILAND, DEV, 4L)
            .put(VIETNAM, DEV, 4L)
            .put(INDONESIA, STAGING, 4L)
            .put(THAILAND, STAGING, 4L)
            .put(VIETNAM, STAGING, 4L)
            .put(INDONESIA, PRODUCTION, 4L)
            .put(THAILAND, PRODUCTION, 43L)
            .put(VIETNAM, PRODUCTION, 25L)
            .build();

    private static final ImmutableTable<Country, Environment, String> CONVERSION_UPDATE_REQUESTS_S3_BUCKETS = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia.dev.conversion.update.requests")
            .put(THAILAND, DEV, "thailand.dev.conversion.update.requests")
            .put(VIETNAM, DEV, "vietnam.dev.conversion.update.requests")
            .put(INDONESIA, STAGING, "indonesia.staging.conversion.update.requests")
            .put(THAILAND, STAGING, "thailand.staging.conversion.update.requests")
            .put(VIETNAM, STAGING, "vietnam.staging.conversion.update.requests")
            .put(INDONESIA, PRODUCTION, "indonesia.production.conversion.update.requests")
            .put(THAILAND, PRODUCTION, "thailand.production.conversion.update.requests")
            .put(VIETNAM, PRODUCTION, "vietnam.production.conversion.update.requests")
            .build();

    @Override
    protected void configure() {
        install(new TaekkyeonSlackMessageSenderModule());

        bind(RecordReader.class).annotatedWith(MainRecordReaderBinding.class)
                .to(MerchantApiConversionLoaderRecordReader.class);
        bind(new TypeLiteral<RecordProcessor<? extends Record<?>, ? extends Record<?>>>() {
        }).annotatedWith(MainRecordProcessorBinding.class)
                .to(DummyRecordProcessor.class);
        bind(RecordWriter.class).annotatedWith(MainRecordWriterBinding.class)
                .to(MerchantApiConversionLoaderRecordWriter.class);

        bindConstant().annotatedWith(named(BIND_KEY_API_URL))
                .to(URL_MAPPING.get(getTargetCountryCode(), getMerchant()));
        bindConstant().annotatedWith(named(BIND_KEY_SECRET_NAME))
                .to(getApiSecretName());
        bindConstant().annotatedWith(named(BIND_KEY_CONVERSION_UPDATE_REQUESTS_S3_BUCKET))
                .to(CONVERSION_UPDATE_REQUESTS_S3_BUCKETS.get(getCurrentCountry(),
                        getCurrentEnvironment()));
        bindConstant().annotatedWith(named(BIND_KEY_STAFF_ID))
                .to(getStaffId());
    }

    private Long getStaffId() {
        String staffId = getStaffIdFromSystem();
        if (!isNullOrEmpty(staffId)) {
            return parseLong(staffId);
        }
        return STAFF_ID_MAPPING.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    private String getApiSecretName() {
        String secretName = getApiSecretNameFromSystem();
        if (!isNullOrEmpty(secretName)) {
            return secretName;
        }
        Map<String, List<Long>> campaignIdSecretMapping = SECRET_NAME_MAPPING
                .get(getTargetCountryCode(), getMerchant());
        for (Map.Entry<String, List<Long>> entry : campaignIdSecretMapping.entrySet()) {
            List<Long> campaignIds = entry.getValue();
            if (campaignIds.contains(getCampaignId())) {
                return entry.getKey();
            }
        }
        return EMPTY;
    }

    private String getApiSecretNameFromSystem() {
        return getProperty(API_SECRET_NAME_VM_ARGUMENT, EMPTY);
    }

    private String getStaffIdFromSystem() {
        return getProperty(STAFF_ID_VM_ARGUMENT);
    }

    @Provides @Singleton @Named(BIND_KEY_TARGET_COUNTRY_CODE)
    private String getTargetCountryCode() {
        return getPropertyBy(TARGET_COUNTRY_CODE_VM_ARGUMENT);
    }

    @Provides @Singleton @Named(BIND_KEY_MERCHANT)
    private Merchant getMerchant() {
        return Merchant.valueOf(getPropertyBy(MERCHANT_VM_ARGUMENT).toUpperCase());
    }

    @Provides @Singleton @Named(BIND_KEY_CAMPAIGN_ID)
    private Long getCampaignId() {
        return Long.parseLong(getPropertyBy(CAMPAIGN_ID_VM_ARGUMENT));
    }

    @Provides @Singleton @Named(BIND_KEY_SLEEP_SECOND)
    private int getSleepSecond() {
        return Integer.parseInt(getProperty(SLEEP_SECOND, "3"));
    }

    @Provides @Singleton @Named(BIND_KEY_SCAN_TIME_FROM)
    private LocalDateTime provideScanTimeFrom() {
        String value = getProperty(SCAN_TIME_FROM_VM_ARGUMENT);
        if (isNullOrEmpty(value)) {
            return LocalDateTime.now().minusDays(2);
        } else {
            return LocalDateTime.parse(value, DATE_TIME_FORMATTER);
        }
    }

    @Provides @Singleton @Named(BIND_KEY_SCAN_TIME_TO)
    private LocalDateTime provideScanTimeTo() {
        String value = getProperty(SCAN_TIME_TO_VM_ARGUMENT);
        if (isNullOrEmpty(value)) {
            return LocalDateTime.now();
        } else {
            return LocalDateTime.parse(value, DATE_TIME_FORMATTER);
        }
    }

    @Provides @Singleton
    private MerchantApiConversionService provideMerchantService(Injector injector) {
        return injector
                .getInstance(SERVICE_MAPPING.get(getTargetCountryCode(), getMerchant()));
    }

    @Provides @Singleton @Named(BIND_KEY_PARTITION_CONVERSION_COUNT)
    private int getPartitionConversionCount() {
        return parseInt(getProperty(PARTITION_CONVERSION_COUNT, "10000"));
    }

    @Provides @Singleton @Named(BIND_KEY_LIMIT_RESET_COUNT)
    private int getLimitResetCount() {
        return parseInt(getProperty(LIMIT_RESET_COUNT, "3"));
    }

    @Provides @Singleton @Named(BIND_KEY_LIMIT_TIME_CALL_API)
    private int getLimitTimeCallApi() {
        return parseInt(getProperty(LIMIT_TIME_CALL_API, "30"));
    }

    @Provides @Singleton @Named(BIND_KEY_IS_CHECK_DUPLICATE_CONVERSION)
    private boolean isDuplicateConversionCheckEnabled() {
        return getProperty(DUPLICATE_CONVERSION_CHECK_ENABLED) == null
                || parseBoolean(getProperty(DUPLICATE_CONVERSION_CHECK_ENABLED));
    }

    @Provides @Singleton @Named(BIND_KEY_RECEIVED_CURRENCY)
    private String getReceivedCurrency() {
        return getProperty(RECEIVED_CURRENCY, EMPTY);
    }
}
