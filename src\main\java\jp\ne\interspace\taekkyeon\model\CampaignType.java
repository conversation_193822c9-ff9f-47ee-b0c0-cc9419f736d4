/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding campaign types.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum CampaignType implements ValueEnum {

    CPA(0),
    CPC(1),
    CPL(2),
    CPS(3);

    private final int value;
}
