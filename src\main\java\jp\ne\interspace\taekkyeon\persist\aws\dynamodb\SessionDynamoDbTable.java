/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.dynamodb;

import java.util.function.Function;

import javax.inject.Singleton;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.ProvisionedThroughput;
import com.google.common.collect.ImmutableMap;

import lombok.Getter;

import static com.amazonaws.services.dynamodbv2.model.KeyType.HASH;
import static com.google.common.collect.ImmutableMap.of;
import static java.util.Arrays.asList;

/**
 * Data access layer responsible for fetching raw session data.
 *
 * <AUTHOR>
 */
@Singleton
public class SessionDynamoDbTable extends DynamoDbTable {

    public static final String ADDITIONAL_PARAMETERS_COLUMN_NAME = "ADDITIONAL_PARAMETERS";
    public static final String CLICK_DATE_COLUMN_NAME = "CLICK_DATE";
    public static final String DEVICE_TYPE_COLUMN_NAME = "DEVICE_TYPE";
    public static final String IP_ADDRESS_COLUMN_NAME = "IP_ADDRESS";
    public static final String LANGUAGE_COLUMN_NAME = "LANGUAGE";
    public static final String RK_COLUMN_NAME = "RK";

    @Getter
    private final String keyName = "UUID";

    @Getter
    private final String partialName = "NEW_SESSION";

    @Getter
    private Function<String, ImmutableMap<String, AttributeValue>>
            keyResolver = key -> of(keyName, stringOf(key));

    @Override
    protected CreateTableRequest getTestSchema() {
        return new CreateTableRequest(
                asList(new AttributeDefinition(keyName, STRING_TYPE)),
                getTableName(),
                asList(new KeySchemaElement(keyName, HASH)),
                new ProvisionedThroughput(DEFAULT_CAPACITY_UNITS, DEFAULT_CAPACITY_UNITS));
    }
}
