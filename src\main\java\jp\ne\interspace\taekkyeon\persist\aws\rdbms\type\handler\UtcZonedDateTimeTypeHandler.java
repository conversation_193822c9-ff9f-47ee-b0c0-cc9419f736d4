/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

/**
 * MyBatis {@link TypeHandler} for UTC's {@link ZonedDateTime}.
 *
 * <AUTHOR>
 */
public class UtcZonedDateTimeTypeHandler extends BaseTypeHandler<ZonedDateTime> {

    private static final ZoneId ZONED_ID_UTC = ZoneId.of("UTC");

    @Override
    public void setNonNullParameter(PreparedStatement ps, int parameterIndex,
            ZonedDateTime parameter, JdbcType jdbcType) throws SQLException {
        ps.setTimestamp(parameterIndex, Timestamp
                .valueOf(LocalDateTime.ofInstant(parameter.toInstant(), ZONED_ID_UTC)));
    }

    @Override
    public ZonedDateTime getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        Timestamp timestamp = rs.getTimestamp(columnName);
        if (timestamp == null) {
            return null;
        } else {
            return timestamp.toLocalDateTime().atZone(ZONED_ID_UTC);
        }
    }

    @Override
    public ZonedDateTime getNullableResult(ResultSet rs, int columnIndex)
            throws SQLException {
        Timestamp timestamp = rs.getTimestamp(columnIndex);
        if (timestamp == null) {
            return null;
        } else {
            return timestamp.toLocalDateTime().atZone(ZONED_ID_UTC);
        }
    }

    @Override
    public ZonedDateTime getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        Timestamp timestamp = cs.getTimestamp(columnIndex);
        if (timestamp == null) {
            return null;
        } else {
            return timestamp.toLocalDateTime().atZone(ZONED_ID_UTC);
        }
    }
}
