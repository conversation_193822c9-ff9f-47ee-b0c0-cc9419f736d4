/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import java.time.LocalDateTime;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Unit tests for {@link StringHelper}.
 *
 * <AUTHOR> OBS DEV Team
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class DateTimeHelperTest {

    @Spy
    private DateTimeHelper underTest;

    @Test
    @SuppressWarnings("static-access")
    public void testParseLocalDateTimeFromShouldReturnDateTimeWhenGivenIso8601DateStringWithTimeDesignator() {
        // given
        String timestampWithTimeDesignator = "2017-12-25T01:02:03";
        LocalDateTime expected = LocalDateTime.of(2017, 12, 25, 1, 2, 3);

        // when
        LocalDateTime actual = underTest
                .parseLocalDateTimeFrom(timestampWithTimeDesignator);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    @SuppressWarnings("static-access")
    public void testParseLocalDateTimeFromShouldReturnDateTimeWhenGivenIso8601DateStringWithoutTimeDesignator() {
        // given
        String timestampWithTimeDesignator = "2018-01-02 03:04:05";
        LocalDateTime expected = LocalDateTime.of(2018, 01, 2, 3, 4, 5);

        // when
        LocalDateTime actual = underTest
                .parseLocalDateTimeFrom(timestampWithTimeDesignator);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

}
