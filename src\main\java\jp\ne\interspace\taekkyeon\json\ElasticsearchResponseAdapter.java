/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.json;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import com.google.gson.Gson;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.reflect.TypeToken;

import jp.ne.interspace.taekkyeon.model.elasticsearch.Document;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchSearchResponse;

/**
 * Json deserializer for {@link ElasticsearchSearchResponse}.
 *
 * <AUTHOR>
 */
public class ElasticsearchResponseAdapter
        implements JsonDeserializer<ElasticsearchSearchResponse> {

    private static final Type DOCUMENT_TYPE = new TypeToken<ArrayList<Document>>() {
    }.getType();
    private static final Gson GSON = new Gson();

    @Override
    public ElasticsearchSearchResponse deserialize(JsonElement json, Type typeOfT,
            JsonDeserializationContext context) throws JsonParseException {
        JsonObject hits = json.getAsJsonObject().get("hits").getAsJsonObject();
        long count = hits.get("total").getAsJsonObject().get("value").getAsLong();
        List<Document> documents = GSON.fromJson(hits.get("hits"), DOCUMENT_TYPE);
        return new ElasticsearchSearchResponse(count, documents);
    }
}
