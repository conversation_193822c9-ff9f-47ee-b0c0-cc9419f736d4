/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;

import org.easybatch.core.record.Batch;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.model.ConversionUpdateRequestContent;
import jp.ne.interspace.taekkyeon.model.ConversionsWithClickIdDetails;
import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionUpdateRequestMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.StaffAccountMapper;
import jp.ne.interspace.taekkyeon.service.ConversionUpdateRequestService;
import jp.ne.interspace.taekkyeon.service.JsonSerializerService;

import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_STAFF_ID;
import static lombok.AccessLevel.PACKAGE;

/**
 * {@link RecordWriter} for merchant API conversion loader.
 *
 * <AUTHOR> Shin
 */
public class MerchantApiConversionLoaderRecordWriter implements RecordWriter {

    private static final String IMPORT_TYPE = "CONVERSION_IMPORT";

    @Inject @Named(BIND_KEY_STAFF_ID) @Getter(PACKAGE)
    private long staffId;

    @Inject
    private JsonSerializerService jsonSerializerService;

    @Inject
    private ConversionUpdateRequestService conversionUpdateRequestService;

    @Inject
    private StaffAccountMapper staffAccountMapper;

    @Inject
    private ConversionUpdateRequestMapper conversionUpdateRequestMapper;

    @Override
    public void open() throws Exception {
        // do nothing.
    }

    @SuppressWarnings("unchecked")
    @Override
    public void writeRecords(Batch batch) throws Exception {
        for (Record<ConversionsWithClickIdDetails> record : batch) {
            ConversionsWithClickIdDetails payload = record.getPayload();
            String fileName = conversionUpdateRequestService.upload(IMPORT_TYPE,
                    serializeRequest(payload, getStaffId(), IMPORT_TYPE));
            int dataCount = payload.getConversions().size();
            conversionUpdateRequestMapper.insertRequest(fileName,
                    payload.getCampaignId(), getStaffEmailBy(getStaffId()), dataCount);
        }
    }

    @Override
    public void close() throws Exception {
        // do nothing.
    }

    @VisibleForTesting
    Country getCountry() {
        return Country.getCurrentCountry();
    }

    @VisibleForTesting
    String getStaffEmailBy(long staffId) {
        return staffAccountMapper.findEmailBy(staffId);
    }

    @VisibleForTesting
    String serializeRequest(Object object, long staffId, String type) {
        return jsonSerializerService.toJson(new ConversionUpdateRequestContent(type,
                jsonSerializerService.toJson(object), staffId));
    }
}
