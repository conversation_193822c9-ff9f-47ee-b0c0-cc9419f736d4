/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.ses;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import javax.activation.DataHandler;
import javax.mail.MessagingException;
import javax.mail.internet.AddressException;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.model.Body;
import com.amazonaws.services.simpleemail.model.Content;
import com.amazonaws.services.simpleemail.model.Destination;
import com.amazonaws.services.simpleemail.model.Message;
import com.amazonaws.services.simpleemail.model.SendBounceRequest;
import com.amazonaws.services.simpleemail.model.SendBounceResult;
import com.amazonaws.services.simpleemail.model.SendEmailRequest;
import com.amazonaws.services.simpleemail.model.SendEmailResult;
import com.amazonaws.services.simpleemail.model.SendRawEmailRequest;
import com.amazonaws.services.simpleemail.model.SendRawEmailResult;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static java.nio.charset.StandardCharsets.UTF_8;
import static javax.mail.Message.RecipientType.TO;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link SimpleEmailServiceClient}.
 *
 * <AUTHOR> OBS DEV Team
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SimpleEmailServiceClientTest {

    private static final String DEFAULT_TEST_DUMMY_EMAIL = "<EMAIL>";

    private static final List<String> DEFAULT_TEST_DUMMY_EMAILS = Arrays
            .asList(DEFAULT_TEST_DUMMY_EMAIL, DEFAULT_TEST_DUMMY_EMAIL);

    private static final String DEFAULT_TEST_CONTENT = "ACTA NON VERBA";

    private static final String BODY = "body";
    private static final String ATTACHMENT_PATH = "attachmentPath";
    private static final String FROM_ADDRESS = "form";
    private static final String TO_ADDRESS = "to";
    private static final String SUBJECT = "subject";

    @InjectMocks
    @Spy
    private SimpleEmailServiceClient underTest;

    @Mock
    private AmazonSimpleEmailService simpleEmailService;

    @Test(expected = IllegalArgumentException.class)
    public void testSendTextEmailShouldThrowIllegalArgumentExceptionWhenGivenFromIsNull() {
        // given
        String from = null;

        // when
        underTest.sendTextEmail(from, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendTextEmailShouldThrowIllegalArgumentExceptionWhenGivenFromIsEmpty() {
        // given
        String from = " ";

        // when
        underTest.sendTextEmail(from, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendTextEmailShouldThrowIllegalArgumentExceptionWhenGivenSubjectIsNull() {
        // given
        String subject = null;

        // when
        underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS, subject,
                DEFAULT_TEST_CONTENT);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendTextEmailShouldThrowIllegalArgumentExceptionWhenGivenSubjectIsEmpty() {
        // given
        String subject = " ";

        // when
        underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS, subject,
                DEFAULT_TEST_CONTENT);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendTextEmailShouldThrowIllegalArgumentExceptionWhenGivenBodyIsNull() {
        // given
        String body = null;

        // when
        underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, body);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendTextEmailShouldThrowIllegalArgumentExceptionWhenGivenBodyIsEmpty() {
        // given
        String body = " ";

        // when
        underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, body);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendTextEmailShouldThrowIllegalArgumentExceptionWhenGivenToIsNull() {
        // given
        List<String> to = null;

        // when
        underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL, to, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendTextEmailShouldThrowIllegalArgumentExceptionWhenGivenToIsEmpty() {
        // given
        List<String> to = Collections.emptyList();

        // when
        underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL, to, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
    }

    @Test
    public void testSendTextEmailShouldReturnSendEmailResultWhenGivenValidParameters() {
        // given
        SendEmailRequest request = generateTextEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testSendTextEmailShouldReturnSendEmailResultWhenGivenValidParametersAlongWithNullCc() {
        // given
        SendEmailRequest request = generateTextEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, null, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, null, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testSendTextEmailShouldReturnSendEmailResultWhenGivenValidParametersAlongWithEmptyCc() {
        // given
        SendEmailRequest request = generateTextEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, Collections.emptyList(),
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, Collections.emptyList(),
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testSendTextEmailShouldReturnSendEmailResultWhenGivenValidParametersAlongWithNullBcc() {
        // given
        SendEmailRequest request = generateTextEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS, null,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS, null,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testSendTextEmailShouldReturnSendEmailResultWhenGivenValidParametersAlongWithEmptyBcc() {
        // given
        SendEmailRequest request = generateTextEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                Collections.emptyList(), DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendTextEmail(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                Collections.emptyList(), DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendHtmlEmailShouldThrowIllegalArgumentExceptionWhenGivenFromIsNull() {
        // given
        String from = null;

        // when
        underTest.sendHtmlEmail(from, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendHtmlEmailShouldThrowIllegalArgumentExceptionWhenGivenFromIsEmpty() {
        // given
        String from = " ";

        // when
        underTest.sendHtmlEmail(from, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendHtmlEmailShouldThrowIllegalArgumentExceptionWhenGivenSubjectIsNull() {
        // given
        String subject = null;

        // when
        underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS, subject,
                DEFAULT_TEST_CONTENT);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendHtmlEmailShouldThrowIllegalArgumentExceptionWhenGivenSubjectIsEmpty() {
        // given
        String subject = " ";

        // when
        underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS, subject,
                DEFAULT_TEST_CONTENT);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendHtmlEmailShouldThrowIllegalArgumentExceptionWhenGivenBodyIsNull() {
        // given
        String body = null;

        // when
        underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, body);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendHtmlEmailShouldThrowIllegalArgumentExceptionWhenGivenBodyIsEmpty() {
        // given
        String body = " ";

        // when
        underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, body);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendHtmlEmailShouldThrowIllegalArgumentExceptionWhenGivenToIsNull() {
        // given
        List<String> to = null;

        // when
        underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL, to, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendHtmlEmailShouldThrowIllegalArgumentExceptionWhenGivenToIsEmpty() {
        // given
        List<String> to = Collections.emptyList();

        // when
        underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL, to, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
    }

    @Test
    public void testSendHtmlEmailShouldReturnSendEmailResultWhenGivenValidParameters() {
        // given
        SendEmailRequest request = generateHtmlEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testSendHtmlEmailShouldReturnSendEmailResultWhenGivenValidParametersAlongWithNullCc() {
        // given
        SendEmailRequest request = generateHtmlEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, null, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, null, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testSendHtmlEmailShouldReturnSendEmailResultWhenGivenValidParametersAlongWithEmptyCc() {
        // given
        SendEmailRequest request = generateHtmlEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, Collections.emptyList(),
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, Collections.emptyList(),
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testSendHtmlEmailShouldReturnSendEmailResultWhenGivenValidParametersAlongWithNullBcc() {
        // given
        SendEmailRequest request = generateHtmlEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS, null,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS, null,
                DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testSendHtmlEmailShouldReturnSendEmailResultWhenGivenValidParametersAlongWithEmptyBcc() {
        // given
        SendEmailRequest request = generateHtmlEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                Collections.emptyList(), DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendHtmlEmail(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                Collections.emptyList(), DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendEmailShouldThrowIllegalArgumentExceptionWhenGivenFromIsNull() {
        // given
        String from = null;

        // when
        underTest.sendEmail(from,
                generateDestination(DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                        DEFAULT_TEST_DUMMY_EMAILS),
                generateTextMessage(DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendEmailShouldThrowIllegalArgumentExceptionWhenGivenFromIsEmpty() {
        // given
        String from = " ";

        // when
        underTest.sendEmail(from,
                generateDestination(DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                        DEFAULT_TEST_DUMMY_EMAILS),
                generateTextMessage(DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendEmailShouldThrowIllegalArgumentExceptionWhenGivenDestinationIsNull() {
        // given
        Destination destination = null;

        // when
        underTest.sendEmail(DEFAULT_TEST_DUMMY_EMAIL, destination,
                generateTextMessage(DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendEmailShouldThrowIllegalArgumentExceptionWhenGivenMessageIsNull() {
        // given
        Message message = null;

        // when
        underTest.sendEmail(DEFAULT_TEST_DUMMY_EMAIL,
                generateDestination(DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                        DEFAULT_TEST_DUMMY_EMAILS),
                message);
    }

    @Test
    public void testSendEmailShouldReturnSendEmailResultWhenGivenValidParameters() {
        // given
        SendEmailResult expected = new SendEmailResult();
        SendEmailRequest request = generateEmailRequest(DEFAULT_TEST_DUMMY_EMAIL,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT);
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendEmail(DEFAULT_TEST_DUMMY_EMAIL,
                generateDestination(DEFAULT_TEST_DUMMY_EMAILS, DEFAULT_TEST_DUMMY_EMAILS,
                        DEFAULT_TEST_DUMMY_EMAILS),
                generateTextMessage(DEFAULT_TEST_CONTENT, DEFAULT_TEST_CONTENT));

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendEmailShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        SendEmailRequest request = null;

        // when
        underTest.sendEmail(request);
    }

    @Test
    public void testSendEmailShouldReturnSendEmailResultWhenGivenSendEmailRequest() {
        // given
        SendEmailRequest request = new SendEmailRequest();
        SendEmailResult expected = new SendEmailResult();
        when(simpleEmailService.sendEmail(request)).thenReturn(expected);

        // when
        SendEmailResult actual = underTest.sendEmail(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendBounceShouldThrowIllegalArgumentExceptionWhenGivenNullParameter() {
        // given
        SendBounceRequest request = null;

        // when
        underTest.sendBounce(request);
    }

    @Test
    public void testSendBounceShouldReturnSendBounceResultWhenGivenSendBounceRequest() {
        // given
        SendBounceRequest request = new SendBounceRequest();
        SendBounceResult expected = new SendBounceResult();
        when(simpleEmailService.sendBounce(request)).thenReturn(expected);

        // when
        SendBounceResult actual = underTest.sendBounce(request);

        // then
        assertSame(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMailWithAttachmentShouldThrowIllegalArgumentExceptionWhenGivenFromAddressIsNull()
            throws AddressException, MessagingException, IOException {
        // given
        String fromAddress = null;

        underTest.sendMailWithAttachment(fromAddress, TO_ADDRESS, SUBJECT, BODY,
                ATTACHMENT_PATH);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMailWithAttachmentShouldThrowIllegalArgumentExceptionWhenGivenFromAddressIsEmpty()
            throws AddressException, MessagingException, IOException {
        // given
        String fromAddress = " ";

        underTest.sendMailWithAttachment(fromAddress, TO_ADDRESS, SUBJECT, BODY,
                ATTACHMENT_PATH);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMailWithAttachmentShouldThrowIllegalArgumentExceptionWhenGivenToAddressIsNull()
            throws AddressException, MessagingException, IOException {
        // given
        String toAddress = null;

        underTest.sendMailWithAttachment(FROM_ADDRESS, toAddress, SUBJECT, BODY,
                ATTACHMENT_PATH);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMailWithAttachmentShouldThrowIllegalArgumentExceptionWhenGivenToAddressIsEmpty()
            throws AddressException, MessagingException, IOException {
        // given
        String toAddress = " ";

        underTest.sendMailWithAttachment(FROM_ADDRESS, toAddress, SUBJECT, BODY,
                ATTACHMENT_PATH);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMailWithAttachmentShouldThrowIllegalArgumentExceptionWhenGivenSubjectIsNull()
            throws AddressException, MessagingException, IOException {
        // given
        String subject = null;

        underTest.sendMailWithAttachment(FROM_ADDRESS, TO_ADDRESS, subject, BODY,
                ATTACHMENT_PATH);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMailWithAttachmentShouldThrowIllegalArgumentExceptionWhenGivenSubjectIsEmpty()
            throws AddressException, MessagingException, IOException {
        // given
        String subject = " ";

        underTest.sendMailWithAttachment(FROM_ADDRESS, TO_ADDRESS, subject, BODY,
                ATTACHMENT_PATH);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMailWithAttachmentShouldThrowIllegalArgumentExceptionWhenGivenBodyIsNull()
            throws AddressException, MessagingException, IOException {
        // given
        String body = null;

        underTest.sendMailWithAttachment(FROM_ADDRESS, TO_ADDRESS, SUBJECT, body,
                ATTACHMENT_PATH);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMailWithAttachmentShouldThrowIllegalArgumentExceptionWhenGivenBodyIsEmpty()
            throws AddressException, MessagingException, IOException {
        // given
        String body = " ";

        underTest.sendMailWithAttachment(FROM_ADDRESS, TO_ADDRESS, SUBJECT, body,
                ATTACHMENT_PATH);
    }

    @Test
    public void testSendMailWithAttachmentShouldSendMailWithAttachmentWhenCalled()
            throws MessagingException, IOException {
        // given
        doNothing().when(underTest).checkParameterFilePath(ATTACHMENT_PATH);
        MimeMessage message = mock(MimeMessage.class);
        doReturn(message).when(underTest).createMessageWithAttachment(FROM_ADDRESS,
                TO_ADDRESS, SUBJECT, BODY, ATTACHMENT_PATH);
        SendRawEmailRequest request = mock(SendRawEmailRequest.class);
        doReturn(request).when(underTest).createSendRawEmailRequest(message);
        SendRawEmailResult expected = new SendRawEmailResult();
        when(simpleEmailService.sendRawEmail(request)).thenReturn(expected);

        // when
        SendRawEmailResult actual = underTest.sendMailWithAttachment(FROM_ADDRESS,
                TO_ADDRESS, SUBJECT, BODY, ATTACHMENT_PATH);

        // then
        assertEquals(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCheckParameterFilePathShouldThrowIllegalArgumentExceptionWhenGivenAttachmentPathDoesNotExist()
            throws AddressException, MessagingException, IOException {
        // given
        String attachmentPath = System.getProperty("user.dir") + "/test";

        underTest.checkParameterFilePath(attachmentPath);
    }

    @Test
    public void testCheckParameterFilePathShouldDoNothingWhenGivenAttachmentPathExists()
            throws AddressException, MessagingException, IOException {
        // given
        String attachmentPath = System.getProperty("user.dir") + "/pom.xml";

        try {
            underTest.checkParameterFilePath(attachmentPath);
        } catch (IllegalArgumentException e) {
            fail();
        }
    }

    @Test
    public void testCreateMessageWithAttachmentShouldReturnCorrectMimeMessageWhenCalled()
            throws AddressException, MessagingException, IOException {
        // given
        MimeBodyPart bodyPart1 = mock(MimeBodyPart.class);
        doReturn(bodyPart1).when(underTest).createHtmlMessageBody(BODY);
        MimeBodyPart bodyPart2 = mock(MimeBodyPart.class);
        doReturn(bodyPart2).when(underTest).createAttachment(ATTACHMENT_PATH);

        // when
        MimeMessage actual = underTest.createMessageWithAttachment(FROM_ADDRESS,
                TO_ADDRESS, SUBJECT, BODY, ATTACHMENT_PATH);

        // then
        assertNotNull(actual);
        assertEquals(FROM_ADDRESS, actual.getFrom()[0].toString());
        assertEquals(TO_ADDRESS, actual.getRecipients(TO)[0].toString());
        assertEquals(SUBJECT, actual.getSubject());
        MimeMultipart actualMimeMultipart = (MimeMultipart) actual.getContent();
        assertEquals(bodyPart1, actualMimeMultipart.getBodyPart(0));
        assertEquals(bodyPart2, actualMimeMultipart.getBodyPart(1));
    }

    @Test
    public void testCreateHtmlMessageBodyShouldReturnCorrectMimeBodyPartWhenCalled()
            throws MessagingException, IOException {
        // when
        MimeBodyPart actual = underTest.createHtmlMessageBody(BODY);

        // then
        assertNotNull(actual);
        MimeMultipart actualMessageBodyPart = (MimeMultipart) actual.getContent();
        MimeBodyPart actualBodyPart = (MimeBodyPart) actualMessageBodyPart
                .getBodyPart(0);
        assertNotNull(actualBodyPart);
        assertEquals(BODY, actualBodyPart.getContent());
    }

    @Test
    public void testCreateAttachmentShouldReturnCorrectMimeBodyPartWhenCalled()
            throws MessagingException {
        // when
        MimeBodyPart actual = underTest.createAttachment(ATTACHMENT_PATH);

        // then
        assertNotNull(actual);
        DataHandler actualDataHandler = actual.getDataHandler();
        assertNotNull(actualDataHandler);
        assertEquals(ATTACHMENT_PATH, actualDataHandler.getName());
    }

    private Destination generateDestination(List<String> to, List<String> cc,
            List<String> bcc) {
        return new Destination().withToAddresses(to).withCcAddresses(cc)
                .withBccAddresses(bcc);
    }

    private Message generateTextMessage(String subject, String body) {
        return new Message(generateContent(subject), new Body(generateContent(body)));
    }

    private Message generateHtmlMessage(String subject, String body) {
        return new Message(generateContent(subject),
                new Body().withHtml(generateContent(body)));
    }

    private Content generateContent(String content) {
        return new Content().withCharset(UTF_8.name()).withData(content);
    }

    private SendEmailRequest generateSendEmailRequest(String from,
            Destination destination, Message message) {
        return new SendEmailRequest().withSource(from).withDestination(destination)
                .withMessage(message);
    }

    private SendEmailRequest generateEmailRequest(String from, List<String> to,
            List<String> cc, List<String> bcc, String subject, String body) {
        return generateSendEmailRequest(from, generateDestination(to, cc, bcc),
                generateTextMessage(subject, body));
    }

    private SendEmailRequest generateTextEmailRequest(String from, List<String> to,
            List<String> cc, List<String> bcc, String subject, String body) {
        return generateSendEmailRequest(from, generateDestination(to, cc, bcc),
                generateTextMessage(subject, body));
    }

    private SendEmailRequest generateHtmlEmailRequest(String from, List<String> to,
            List<String> cc, List<String> bcc, String subject, String body) {
        return generateSendEmailRequest(from, generateDestination(to, cc, bcc),
                generateHtmlMessage(subject, body));
    }

}
