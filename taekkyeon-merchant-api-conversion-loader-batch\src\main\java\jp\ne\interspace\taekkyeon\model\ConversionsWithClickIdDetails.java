/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDate;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding conversions with click ID data for register.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @ToString @EqualsAndHashCode
public class ConversionsWithClickIdDetails {
    private final long campaignId;
    private long clickFromCampaignId;
    private final String campaignName;
    private final LocalDate confirmationDate;
    private final List<ConversionRegistrationDetails> conversions;
}
