/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import javax.inject.Singleton;

import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.name.Named;

import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Taekkyeon module for ISVN integration.
 *
 * <AUTHOR>
 */
public class TaekkyeonIsvnIntegrationModule extends AbstractModule {

    public static final String BIND_KEY_ISVN_URL = "integration.isvn.url";
    private static final String VM_ARGUMENT_ISVN_URL = "isvnUrl";
    private static final ImmutableMap<Environment, String> ISVN_API_URLS =
            new ImmutableMap.Builder<Environment, String>()
                    .put(DEV, "http://mock-api.accesstradet.global.test:3003/ok")
                    .put(STAGING, "http://mock-api.accesstrade.global:3003/ok")
                    .put(PRODUCTION,
                            "https://core-aff.dev.accesstrade.me/external-api-gateway/commission-policy-service/v1.0/global/callback/event")
                    .build();

    @Override
    protected void configure() {
        install(new TaekkyeonHttpClientModule());
    }

    @Provides @Singleton @Named(BIND_KEY_ISVN_URL)
    private String provideIsvnApiUrl() {
        String isvnUrl = getProperty(VM_ARGUMENT_ISVN_URL);
        return Strings.isNullOrEmpty(isvnUrl)
                ? ISVN_API_URLS.get(getCurrentEnvironment()) : isvnUrl;
    }
}
