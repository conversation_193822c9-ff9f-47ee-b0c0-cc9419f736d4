/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.Reader;

import javax.sql.DataSource;

import org.apache.ibatis.jdbc.ScriptRunner;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

/**
 * Unit test for {@link DatabaseScriptServiceTest}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class DatabaseScriptServiceTest {

    @InjectMocks @Spy
    private DatabaseScriptService underTest;

    @Mock
    private DataSource dataSource;

    @Test
    public void testRunScriptShouldCallRunScriptOfScriptRunnerWhenGivenStringScript()
            throws Exception {
        // given
        ScriptRunner runner = mock(ScriptRunner.class);
        Reader reader = mock(Reader.class);
        String script = "script";

        doReturn(runner).when(underTest).getRunner();
        doReturn(reader).when(underTest).toReaderFrom(script);

        // when
        underTest.runScript(script, false);

        // then
        verify(underTest.getRunner()).runScript(reader);
    }

    @Test
    public void testRunScriptShouldCallRunScriptOfScriptRunnerWhenGivenReaderScript()
            throws Exception {
        // given
        ScriptRunner runner = mock(ScriptRunner.class);
        Reader script = mock(Reader.class);

        doReturn(runner).when(underTest).getRunner();

        // when
        underTest.runScript(script, true);

        // then
        verify(underTest.getRunner()).runScript(script);
    }

    @Test
    public void testCloseConnectionShouldCallCloseConnectionOfScriptRunnerWhenCalled()
            throws Exception {
        // given
        ScriptRunner runner = mock(ScriptRunner.class);

        doReturn(runner).when(underTest).getRunner();

        // when
        underTest.closeConnection();

        // then
        verify(underTest.getRunner()).closeConnection();
    }
}
