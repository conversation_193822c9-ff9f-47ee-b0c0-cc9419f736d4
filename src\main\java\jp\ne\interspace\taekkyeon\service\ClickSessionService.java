/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.inject.Singleton;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Charsets;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.hash.Hashing;
import com.google.inject.Inject;

import lombok.NonNull;

import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.model.ClickSession;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.persist.aws.dynamodb.DynamoDbTable;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.CREATIVE_ID_END_POSITION;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.CREATIVE_ID_START_POSITION;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SITE_ID_END_POSITION;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SITE_ID_START_POSITION;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.ADDITIONAL_PARAMETERS_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.CLICK_DATE_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.DEVICE_TYPE_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.IP_ADDRESS_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.LANGUAGE_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.RK_COLUMN_NAME;

/**
 * Service layer for handling click session data of DynamoDB.
 *
 * <AUTHOR>
 */
@Singleton
public class ClickSessionService {

    private static final int BASE_36 = 36;
    private static final int MAXIMUM_PARAMETER_LENGTH = 2048;

    private static final AttributeValue EMPTY_STRING_ATTRIBUTE_VALUE = new AttributeValue("");

    private static final DateTimeFormatter DATETIME_FORMATTER_YYYY_MM_DD_HHMMSS = DateTimeFormatter
            .ofPattern(DATE_FORMAT_YYYY_MM_DD_HHMMSS);

    @Inject
    private DynamoDbTable dynamoDbTable;

    @Inject
    private StringHelper stringHelper;

    private LoadingCache<String, ClickSession> clickSessionsCache = CacheBuilder
            .newBuilder().maximumSize(10)
            .build(new CacheLoader<String, ClickSession>() {
                @Override
                public ClickSession load(String uuid) {
                    return findClickSessionBy(uuid);
                }
            });

    /**
     * Returns a {@link ClickSession} by given uuid from DynamoDB.
     *
     * @param campaignId
     *            the ID of give campaign for uuid
     * @param clickId
     *            the give click ID for uuid
     * @return a {@link ClickSession} by given uuid from DynamoDB
     */
    public ClickSession findClickSession(long campaignId, String clickId) {
        return clickSessionsCache.getUnchecked(createSessionUuid(campaignId, clickId));
    }

    /**
     * Returns the ID from rk value.
     *
     * @param rk
     *         the given rk value
     * @param startPosition
     *         the given stat position
     * @param endPosition
     *         the given end position
     * @return the ID from rk value
     */
    public long getIdFrom(String rk, int startPosition, int endPosition) {
        return Long.parseLong(rk.substring(startPosition, endPosition), BASE_36);
    }

    @VisibleForTesting
    String createSessionUuid(long campaignId, String clickId) {
        return generateMD5HashFrom(String.valueOf(campaignId)) + clickId;
    }

    private ClickSession findClickSessionBy(String uuid) {
        Optional<Map<String, AttributeValue>> session = dynamoDbTable
                .fetchBy(uuid);
        if (session.isPresent()) {
            Map<String, AttributeValue> sessionDetails = session.get();
            String rk = sessionDetails.get(RK_COLUMN_NAME).getS();
            Map<String, String> additionalParameters = new HashMap<>();
            sessionDetails.get(ADDITIONAL_PARAMETERS_COLUMN_NAME).getM()
                    .forEach((key, value) -> additionalParameters.put(key, stringHelper
                            .truncateToBytes(value.getS(), MAXIMUM_PARAMETER_LENGTH)));
            String clickIp = sessionDetails
                    .getOrDefault(IP_ADDRESS_COLUMN_NAME, EMPTY_STRING_ATTRIBUTE_VALUE)
                    .getS();
            String language = sessionDetails
                    .getOrDefault(LANGUAGE_COLUMN_NAME, EMPTY_STRING_ATTRIBUTE_VALUE)
                    .getS();
            return new ClickSession(
                    getIdFrom(rk, CREATIVE_ID_START_POSITION, CREATIVE_ID_END_POSITION),
                    getIdFrom(rk, SITE_ID_START_POSITION, SITE_ID_END_POSITION), clickIp,
                    language, uuid,
                    LocalDateTime.parse(sessionDetails.get(CLICK_DATE_COLUMN_NAME).getS(),
                            DATETIME_FORMATTER_YYYY_MM_DD_HHMMSS),
                    DeviceType.findBy(Integer
                            .valueOf(sessionDetails.get(DEVICE_TYPE_COLUMN_NAME).getS())),
                    additionalParameters);
        }
        return ClickSession.DEFAULT_CLICK_SESSION;
    }

    private String generateMD5HashFrom(@NonNull String plainText) {
        return Hashing.md5().hashString(plainText, Charsets.UTF_8).toString();
    }
}
