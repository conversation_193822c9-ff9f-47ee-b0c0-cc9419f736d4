log4j.rootLogger=<PERSON><PERSON><PERSON>, stdout, cloudWatchLogs
log4j.logger.jp.ne.interspace.taekkyeon=DEBUG
log4j.logger.org.easybatch.core.job=DEBUG
log4j.logger.com.gargoylesoftware=OFF

# Direct log messages to stdout
log4j.appender.stdout.Threshold=INFO
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.EnhancedPatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss:SSS Z} %-5p [%t] %c{1}:%L - %m%n%throwable%n

# Append to CloudWatch Logs Stream
log4j.appender.cloudWatchLogs.Threshold=DEBUG
log4j.appender.cloudWatchLogs=com.github.speedwing.log4j.cloudwatch.appender.CloudwatchAppender
log4j.appender.cloudWatchLogs.logGroupName=taekkyeon-${country}-${environment}-${batchName}
log4j.appender.cloudWatchLogs.logStreamName=${batchName}-logs
log4j.appender.cloudWatchLogs.region=ap-southeast-1
log4j.appender.cloudWatchLogs.queueLength=512
log4j.appender.cloudWatchLogs.messagesBatchSize=64
log4j.appender.cloudWatchLogs.layout=org.apache.log4j.EnhancedPatternLayout
log4j.appender.cloudWatchLogs.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss:SSS Z} %-5p [%t] %c{1}:%L - %m%n%throwable%n
