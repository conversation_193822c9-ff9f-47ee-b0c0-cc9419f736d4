/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.YearMonth;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CurrencyRate;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for {@link CurrencyMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class CurrencyMapperTest {

    @Inject
    private CurrencyMapper underTest;

    @Test
    public void testFindCurrencyRateShouldReturnCorrectCurrencyRateWhenFoundData() {
        // given
        long campaignId = 3000;
        YearMonth conversionMonth = YearMonth.of(2020,2);
        String currency = "PHP";

        // when
        CurrencyRate actual = underTest.findCurrencyRate(currency, campaignId, conversionMonth);

        // then
        assertNotNull(actual);
        assertCurrencyRate(actual, currency, new BigDecimal("5.**********"));
    }

    @Test
    public void testFindCampaignCurrencyRateShouldReturnCorrectNameWhenFoundData() {
        // given
        long campaignId = 3000;
        YearMonth conversionMonth = YearMonth.of(2020,2);
        String currency = "SGD";

        // when
        CurrencyRate actual = underTest.findCampaignCurrencyRate(campaignId,
                conversionMonth);

        // then
        assertNotNull(actual);
        assertCurrencyRate(actual, currency, new BigDecimal("1.**********"));
    }

    private void assertCurrencyRate(CurrencyRate actual, String expectedCurrency,
            BigDecimal expectedRate) {
        assertEquals(expectedCurrency, actual.getCurrency());
        assertEquals(expectedRate, actual.getRate());
    }
}
