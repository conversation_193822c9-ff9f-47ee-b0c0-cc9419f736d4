/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding campaign closure status.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum CampaignClosureStatus implements ValueEnum {

    VALIDATING(0),
    PENDING_FOR_MERCHANT_PAYMENT(1),
    CLOSED(2);

    private final int value;
}
