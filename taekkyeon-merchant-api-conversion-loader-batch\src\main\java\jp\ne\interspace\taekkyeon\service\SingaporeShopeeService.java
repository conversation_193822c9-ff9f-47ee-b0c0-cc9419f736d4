/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.LinkedList;
import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.gson.JsonObject;

import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;

import static com.google.common.base.Joiner.on;
import static java.time.Instant.ofEpochSecond;
import static java.time.ZonedDateTime.of;
import static java.time.ZonedDateTime.ofInstant;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.COMMA;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER_YYYYMMDD;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;

/**
 * Implements {@link AbstractShopeeService} for singapore shopee.
 *
 * <AUTHOR> Nguyen
 */
public class SingaporeShopeeService extends AbstractShopeeService {

    private static final int PRODUCT_RESULT_ID = 3;
    private static final int CATEGORY_RESULT_ID = 30;
    private static final String CATEGORY_ID_BRAND_COMMISSION = "Brand Commission";
    private static final String SHOPEE_COMM = "ShopeeComm";
    private static final String XTRA_COMM = "XTRAComm";

    @Override
    public List<ConversionRegistrationDetails> parse(String responseBody)
            throws Exception {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        if (!Strings.isNullOrEmpty(responseBody)) {
            JsonObject obj = getJsonParser().parse(responseBody).getAsJsonObject();
            ShopeeConversionReport conversionReport = parseConversionReport(obj);
            ZoneId zoneId = getZoneId();
            for (ShopeeNode shopeeNode : conversionReport.getNodes()) {
                String[] utmContents = shopeeNode.getUtmContent().split(HYPHEN);
                String clickId = getClickIdFrom(utmContents);
                String customerType = shopeeNode.getBuyerType().toLowerCase();
                Instant instant = ofEpochSecond(shopeeNode.getPurchaseTime());
                ZonedDateTime conversionTime = ofInstant(instant, zoneId);
                String targetMd5CampaignId = hashMd5By(getCampaignId());
                if (isTargetCampaignId(utmContents, targetMd5CampaignId)) {
                    for (ShopeeOrder shopeeOrder : shopeeNode.getOrders()) {
                        String identifier = on(HYPHEN).join(shopeeNode.getCheckoutId(),
                                shopeeOrder.getOrderId());
                        details.addAll(createConversionDetails(clickId, customerType,
                                conversionTime, identifier, shopeeOrder));
                    }
                }
            }
        }
        return details;
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> createConversionDetails(String clickId,
            String customerType, ZonedDateTime conversionTime, String identifier,
            ShopeeOrder shopeeOrder) throws UnsupportedEncodingException {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        List<String> products = new LinkedList<>();
        for (ShopeeItem shopeeItem : shopeeOrder.getItems()) {
            String sku = getSkuFrom(shopeeItem);
            String productId = getItemId(products, sku);
            products.add(sku);
            String category = shopeeItem.getGlobalCategoryLv1Name().replaceAll(COMMA, EMPTY);
            String conversionOccursDate = conversionTime.format(DATE_TIME_FORMATTER_YYYYMMDD);
            String modelId = shopeeItem.getModelId();
            String key = Joiner.on(HYPHEN)
                    .join(getCampaignId(), conversionOccursDate, identifier,
                            shopeeOrder.getOrderId(), shopeeItem.getItemId(), modelId);
            if (!isConversionUnique(key)) {
                continue;
            }

            BigDecimal itemAmount = shopeeItem.getActualAmount();
            String newCustomerType = validateDateTimeAfter(conversionTime)
                    ? getNewCustomerTypeSuffix(customerType,
                    shopeeItem.getAttributionType()) : customerType;

            String productIdShopeeComm = getProductId(shopeeItem,
                    shopeeOrder.getOrderId(), productId, shopeeItem.getItemCommission(),
                    SHOPEE_COMM);

            boolean isBrandItem = isBrandItem(shopeeItem.getShopId());

            List<ConversionRegistrationDetails> brandItemConversions =
                    processConversionByBrandItemRules(conversionTime, identifier,
                    CATEGORY_RESULT_ID, newCustomerType, category, productIdShopeeComm,
                    itemAmount, clickId, shopeeItem.getShopId(), isBrandItem);
            List<ConversionRegistrationDetails> bonusConversions = createExtraBonuses(
                    conversionTime, identifier, PRODUCT_RESULT_ID,
                    newCustomerType, CATEGORY_ID_BRAND_COMMISSION, productId,
                    clickId, shopeeItem, shopeeOrder, brandItemConversions);
            brandItemConversions.addAll(bonusConversions);
            details.addAll(brandItemConversions);

            insertDataProceeded(
                    createConversionProceeded(conversionTime, brandItemConversions, key));
        }
        return details;
    }

    @VisibleForTesting
    boolean checkDateTime(ZonedDateTime conversionTime) {
        return !conversionTime.isBefore(getStartDateTime())
                && !conversionTime.isAfter(getEndDateTime());
    }

    @VisibleForTesting
    ZonedDateTime getStartDateTime() {
        return of(2024, 7, 26, 0, 0, 0, 0, getZoneId());
    }

    @VisibleForTesting
    ZonedDateTime getEndDateTime() {
        return of(2024, 7, 31, 23, 59, 59, 0, getZoneId());
    }
}
