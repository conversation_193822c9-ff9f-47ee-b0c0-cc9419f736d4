/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler.ValueEnumTypeHandler;

/**
 * MyBatis {@link TypeHandler} for {@link DeviceType}.
 *
 * <AUTHOR>
 */
@MappedTypes(DeviceType.class)
public class DeviceTypeTypeHandler extends ValueEnumTypeHandler<DeviceType> {

    private static final DeviceType DEFAULT_DEVICE_TYPE = DeviceType.UNKNOWN;

    public DeviceTypeTypeHandler() {
        super(DeviceType.class, DEFAULT_DEVICE_TYPE);
    }

    /**
     * Returns the system default {@link DeviceType}.
     *
     * @return system default {@link DeviceType}
     */
    public static DeviceType getDefaultDeviceType() {
        return DEFAULT_DEVICE_TYPE;
    }
}
