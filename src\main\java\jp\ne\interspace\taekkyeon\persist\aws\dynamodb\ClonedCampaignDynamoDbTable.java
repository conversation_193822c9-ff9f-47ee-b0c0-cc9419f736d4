/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.dynamodb;

import javax.inject.Singleton;

import jp.ne.interspace.taekkyeon.module.Country;

/**
 * Data access layer responsible for handling cloned campaign data from DynamoDB.
 *
 * <AUTHOR>
 */
@Singleton
public class ClonedCampaignDynamoDbTable extends BaseCampaignDynamoDbTable {

    public ClonedCampaignDynamoDbTable() {
        super(Country.INDONESIA);
    }
}
