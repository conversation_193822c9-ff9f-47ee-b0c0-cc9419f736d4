/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import java.time.LocalDateTime;

import javax.inject.Singleton;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.ISO_8061_DATE_TIME_FORMATTER;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.ISO_8601_TIME_DESIGNATOR;

/**
 * Convenience class for date and time related operations.
 *
 * <AUTHOR> OBS DEV Team
 */
@Singleton
public class DateTimeHelper {

    /**
     * Returns {@link LocalDateTime} corresponding to the given formatted date string.
     *
     * @param inputDateTime
     *            a date {@link String} to parse
     * @return {@link LocalDateTime} corresponding to the given formatted date string
     */
    public static LocalDateTime parseLocalDateTimeFrom(String inputDateTime) {
        if (inputDateTime.contains(ISO_8601_TIME_DESIGNATOR)) {
            return LocalDateTime.parse(inputDateTime, ISO_8061_DATE_TIME_FORMATTER);
        }
        return LocalDateTime.parse(inputDateTime, DATE_TIME_FORMATTER);
    }

}
