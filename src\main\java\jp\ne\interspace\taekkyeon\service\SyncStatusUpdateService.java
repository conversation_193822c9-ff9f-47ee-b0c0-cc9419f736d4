/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.Map;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;

/**
 * Service for updating sync statuses after the dynamodb sync has finished.
 *
 * <AUTHOR>
 */
public interface SyncStatusUpdateService {

    /**
     * Updates the sync status of the given records.
     *
     * @param records
     *            the given {@code records}
     */
    void updateSyncStatusOf(Map<String, Map<String, AttributeValue>> records);
}
