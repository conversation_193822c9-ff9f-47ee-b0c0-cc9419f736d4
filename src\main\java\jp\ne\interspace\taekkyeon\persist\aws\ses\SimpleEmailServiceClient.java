/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.ses;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.nio.ByteBuffer;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Paths;
import java.util.List;
import java.util.Properties;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.inject.Singleton;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.amazonaws.AmazonWebServiceRequest;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClient;
import com.amazonaws.services.simpleemail.model.Body;
import com.amazonaws.services.simpleemail.model.Content;
import com.amazonaws.services.simpleemail.model.Destination;
import com.amazonaws.services.simpleemail.model.Message;
import com.amazonaws.services.simpleemail.model.RawMessage;
import com.amazonaws.services.simpleemail.model.SendBounceRequest;
import com.amazonaws.services.simpleemail.model.SendBounceResult;
import com.amazonaws.services.simpleemail.model.SendEmailRequest;
import com.amazonaws.services.simpleemail.model.SendEmailResult;
import com.amazonaws.services.simpleemail.model.SendRawEmailRequest;
import com.amazonaws.services.simpleemail.model.SendRawEmailResult;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;

import jp.ne.interspace.taekkyeon.module.TaekkyeonConfig;

import static com.google.common.base.Preconditions.checkArgument;
import static java.nio.charset.StandardCharsets.UTF_8;
import static javax.mail.Message.RecipientType.TO;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.AWS_CREDENTIALS_CHAIN;

/**
 * Low-level client for Amazon Simple Email Service.
 *
 * <AUTHOR> OBS DEV Team
 * @see AmazonSimpleEmailService
 */
@Singleton
public class SimpleEmailServiceClient {

    private AmazonSimpleEmailService simpleEmailService;

    /**
     * Constructs a new client to invoke service methods on Amazon SES using the specified
     * AWS account credentials provider.
     * <p>
     * All service calls made using this new client object are blocking, and will not
     * return until the service call completes.
     * </p>
     */
    SimpleEmailServiceClient() {
        simpleEmailService = createAmazonSimpleEmailService();
    }

    private AmazonSimpleEmailService createAmazonSimpleEmailService() {
        return AmazonSimpleEmailServiceClient.builder()
                .withCredentials(AWS_CREDENTIALS_CHAIN)
                .withRegion(TaekkyeonConfig.AWS_SES_REGION).build();
    }

    private void checkRequest(AmazonWebServiceRequest request) {
        checkArgument(request != null, "Invalid Simple Email Service API request");
    }

    private void checkParameters(Serializable... parameters) {
        String message = "Invalid parameter(s) for Simple Email Service API";
        checkArgument(parameters != null, message);
        for (Serializable parameter : parameters) {
            checkArgument(parameter != null, message);
        }
    }

    private void checkParameters(String... parameters) {
        String message = "Invalid String parameter(s) for Simple Email Service API";
        checkArgument(parameters != null, message);
        for (String parameter : parameters) {
            checkArgument(Strings.nullToEmpty(parameter).trim().length() > 0, message);
        }
    }

    private void checkParameters(List<String> parameters) {
        checkArgument(parameters != null && !parameters.isEmpty(),
                "Invalid List parameter(s) for Simple Email Service API");
    }

    /**
     * Composes an email message based on input data, and then immediately queues the
     * message for sending.
     *
     * @param from
     *            the email address that is sending the email. This email address must be
     *            either individually verified with Amazon SES, or from a domain that has
     *            been verified with Amazon SES. For information about verifying
     *            identities, see the <a href=
     *            "http://docs.aws.amazon.com/ses/latest/DeveloperGuide/verify-addresses-and-domains.html"
     *            >Amazon SES Developer Guide</a>
     * @param to
     *            the To: field(s) of the message
     * @param cc
     *            the CC: field(s) of the message; set an empty list if not needed
     * @param bcc
     *            the BCC: field(s) of the message; set an empty list if not needed
     * @param subject
     *            the subject of the message; a short summary of the content, which will
     *            appear in the recipient's inbox
     * @param body
     *            the message body
     * @return {@link SendEmailResult} as a result of the SendEmail operation returned by
     *         the service
     * @see AmazonSimpleEmailService#sendEmail(SendEmailRequest)
     */
    public SendEmailResult sendTextEmail(String from, List<String> to, List<String> cc,
            List<String> bcc, String subject, String body) {
        checkParameters(from, subject, body);
        checkParameters(to);
        return sendEmail(from, to, cc, bcc, subject,
                new Body(new Content().withCharset(UTF_8.name()).withData(body)));
    }

    /**
     * Composes an email message based on input data, and then immediately queues the
     * message for sending.
     *
     * @param from
     *            the email address that is sending the email. This email address must be
     *            either individually verified with Amazon SES, or from a domain that has
     *            been verified with Amazon SES. For information about verifying
     *            identities, see the <a href=
     *            "http://docs.aws.amazon.com/ses/latest/DeveloperGuide/verify-addresses-and-domains.html"
     *            >Amazon SES Developer Guide</a>
     * @param to
     *            the To: field(s) of the message
     * @param cc
     *            the CC: field(s) of the message; set an empty list if not needed
     * @param bcc
     *            the BCC: field(s) of the message; set an empty list if not needed
     * @param subject
     *            the subject of the message; a short summary of the content, which will
     *            appear in the recipient's inbox
     * @param body
     *            the message body
     * @return {@link SendEmailResult} as a result of the SendEmail operation returned by
     *         the service
     * @see AmazonSimpleEmailService#sendEmail(SendEmailRequest)
     */
    public SendEmailResult sendHtmlEmail(String from, List<String> to, List<String> cc,
            List<String> bcc, String subject, String body) {
        checkParameters(from, subject, body);
        checkParameters(to);
        return sendEmail(from, to, cc, bcc, subject,
                new Body().withHtml(
                        new Content().withCharset(UTF_8.name()).withData(body)));
    }

    private SendEmailResult sendEmail(String from, List<String> to, List<String> cc,
            List<String> bcc, String subject, Body body) {
        return sendEmail(from,
                new Destination().withToAddresses(to).withCcAddresses(cc)
                        .withBccAddresses(bcc),
                new Message(new Content().withCharset(UTF_8.name()).withData(subject),
                        body));
    }

    /**
     * Composes an email message based on input data, and then immediately queues the
     * message for sending.
     *
     * @param from
     *            the email address that is sending the email. This email address must be
     *            either individually verified with Amazon SES, or from a domain that has
     *            been verified with Amazon SES. For information about verifying
     *            identities, see the <a href=
     *            "http://docs.aws.amazon.com/ses/latest/DeveloperGuide/verify-addresses-and-domains.html"
     *            >Amazon SES Developer Guide</a>
     * @param destination
     *            the destination for this email, composed of To:, CC:, and BCC: fields
     * @param message
     *            the message to be sent
     * @return {@link SendEmailResult} as a result of the SendEmail operation returned by
     *         the service
     * @see AmazonSimpleEmailService#sendEmail(SendEmailRequest)
     */
    public SendEmailResult sendEmail(String from, Destination destination,
            Message message) {
        checkParameters(from);
        checkParameters(destination, message);
        return sendEmail(new SendEmailRequest().withSource(from)
                .withDestination(destination).withMessage(message));
    }

    /**
     * Composes an email message based on input data, and then immediately queues the
     * message for sending.
     *
     * @param request
     *            represents a request to send a single formatted email using Amazon SES
     * @return {@link SendEmailResult} as a result of the SendEmail operation returned by
     *         the service
     * @see AmazonSimpleEmailService#sendEmail(SendEmailRequest)
     */
    public SendEmailResult sendEmail(SendEmailRequest request) {
        checkRequest(request);
        return simpleEmailService.sendEmail(request);
    }

    /**
     * Generates and sends a bounce message to the sender of an email you received through
     * Amazon SES. You can only use this API on an email up to 24 hours after you receive
     * it.
     * <p>
     * You cannot use this API to send generic bounces for mail that was not received by
     * Amazon SES. <strong>This action is throttled at one request per second.</strong>
     * </p>
     *
     * @param request
     *            represents a request to send a bounce message to the sender of an email
     *            you received through Amazon SES
     * @return {@link SendBounceResult} as a result of the SendBounce operation returned
     *         by the service
     * @see AmazonSimpleEmailService#sendBounce(SendBounceRequest)
     */
    public SendBounceResult sendBounce(SendBounceRequest request) {
        checkRequest(request);
        return simpleEmailService.sendBounce(request);
    }

    /**
     * Composes an email message and attachment based on input data, and then immediately
     * queues the message for sending.
     *
     * @param from
     *            the email address that is sending the email. This email address must be
     *            either individually verified with Amazon SES, or from a domain that has
     *            been verified with Amazon SES. For information about verifying
     *            identities, see the <a href=
     *            "http://docs.aws.amazon.com/ses/latest/DeveloperGuide/verify-addresses-and-domains.html"
     *            >Amazon SES Developer Guide</a>
     * @param to
     *            the To: field(s) of the message
     * @param subject
     *            the subject of the message
     * @param body
     *            the message body
     * @param attachmentPath
     *            the attachment path
     * @return {@link SendEmailResult} as a result of the SendEmail operation returned by
     *         the service
     * @throws MessagingException
     *            when the email body creating fails
     * @throws IOException
     *            when the message row creating fails
     * @see AmazonSimpleEmailService#sendRawEmail(SendRawEmailRequest)
     */
    public SendRawEmailResult sendMailWithAttachment(String from, String to,
            String subject, String body, String attachmentPath)
            throws MessagingException, IOException {

        checkParameters(from, to, subject, body);
        checkParameterFilePath(attachmentPath);

        MimeMessage message = createMessageWithAttachment(from, to, subject, body,
                attachmentPath);
        return simpleEmailService.sendRawEmail(createSendRawEmailRequest(message));
    }

    @VisibleForTesting
    void checkParameterFilePath(String filePath) {
        checkArgument(Files.exists(Paths.get(filePath), LinkOption.NOFOLLOW_LINKS),
                filePath);
    }

    @VisibleForTesting
    MimeMessage createMessageWithAttachment(String from, String to, String subject,
            String body, String attachmentPath)
            throws AddressException, MessagingException {
        Session session = Session.getDefaultInstance(new Properties());
        MimeMessage message = new MimeMessage(session);
        message.setFrom(from);
        message.setRecipients(TO, InternetAddress.parse(to));
        message.setSubject(subject, UTF_8.name());

        MimeMultipart mailPart = new MimeMultipart("mixed");
        mailPart.addBodyPart(createHtmlMessageBody(body));
        mailPart.addBodyPart(createAttachment(attachmentPath));
        message.setContent(mailPart);

        return message;
    }

    @VisibleForTesting
    MimeBodyPart createHtmlMessageBody(String body)
            throws MessagingException {
        MimeMultipart bodyPart = new MimeMultipart("alternative");
        MimeBodyPart htmlBodyPart = new MimeBodyPart();
        htmlBodyPart.setContent(body, "text/html; charset=UTF-8");
        bodyPart.addBodyPart(htmlBodyPart);

        MimeBodyPart messageBody = new MimeBodyPart();
        messageBody.setContent(bodyPart);
        return messageBody;
    }

    @VisibleForTesting
    MimeBodyPart createAttachment(String attachmentPath)
            throws MessagingException {
        MimeBodyPart attachmentPart = new MimeBodyPart();
        DataSource dataSource = new FileDataSource(attachmentPath);
        attachmentPart.setDataHandler(new DataHandler(dataSource));
        attachmentPart.setFileName(dataSource.getName());
        return attachmentPart;
    }

    @VisibleForTesting
    SendRawEmailRequest createSendRawEmailRequest(MimeMessage message)
            throws IOException, MessagingException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        message.writeTo(outputStream);

        RawMessage rawMessage = new RawMessage(
                ByteBuffer.wrap(outputStream.toByteArray()));

        return new SendRawEmailRequest(rawMessage);
    }
}
