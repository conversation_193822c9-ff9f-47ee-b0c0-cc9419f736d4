/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import javax.inject.Singleton;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;

import static java.lang.Integer.parseInt;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.getPropertyBy;

/**
 * Guice module for the VM arguments of the sqs queue consumer.
 *
 * <AUTHOR> <PERSON>
 */
public class SimpleQueueServiceQueueConsumerModule extends AbstractModule {

    private static final String MAX_EXECUTION_SECONDS_VM_ARGUMENT = "maxExecutionSeconds";
    private static final String VISIBILITY_TIMEOUT_SECONDS_VM_ARGUMENT = "visibilityTimeoutSeconds";
    private static final String WAIT_TIME_SECONDS_VM_ARGUMENT = "waitTimeSeconds";

    @Override
    protected void configure() {
    }

    @Provides @Singleton @MaxExecutionSecondsResolver
    private int provideMaxExecutionTimeInSeconds() {
        return parseInt(getPropertyBy(MAX_EXECUTION_SECONDS_VM_ARGUMENT));
    }

    @Provides @Singleton @VisibilityTimeoutSecondsResolver
    private int provideVisibilityTimeoutInSeconds() {
        return parseInt(getPropertyBy(VISIBILITY_TIMEOUT_SECONDS_VM_ARGUMENT));
    }

    @Provides @Singleton @WaitTimeSecondsResolver
    private int provideWaitTimeInSeconds() {
        return parseInt(getPropertyBy(WAIT_TIME_SECONDS_VM_ARGUMENT));
    }
}
