/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

/**
 * MyBatis {@link TypeHandler} for {@link ZonedDateTime} with zoneId column in resultset.
 *
 * <AUTHOR> <PERSON>
 */
public class ResultColumnZonedDateTimeTypeHandler extends BaseTypeHandler<ZonedDateTime> {

    private static final String ZONE_ID_COLUMN_NAME = "zoneId";

    @Override
    public void setNonNullParameter(PreparedStatement ps, int parameterIndex,
            ZonedDateTime parameter, JdbcType jdbcType) throws SQLException {
        ps.setTimestamp(parameterIndex, Timestamp.valueOf(parameter.toLocalDateTime()));
    }

    @Override
    public ZonedDateTime getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        Timestamp timestamp = rs.getTimestamp(columnName);
        if (timestamp == null) {
            return null;
        } else {
            return timestamp.toLocalDateTime()
                    .atZone(ZoneId.of(rs.getString(ZONE_ID_COLUMN_NAME)));
        }
    }

    @Override
    public ZonedDateTime getNullableResult(ResultSet rs, int columnIndex)
            throws SQLException {
        Timestamp timestamp = rs.getTimestamp(columnIndex);
        if (timestamp == null) {
            return null;
        } else {
            return timestamp.toLocalDateTime()
                    .atZone(ZoneId.of(rs.getString(ZONE_ID_COLUMN_NAME)));
        }
    }

    @Override
    public ZonedDateTime getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        Timestamp timestamp = cs.getTimestamp(columnIndex);
        if (timestamp == null) {
            return null;
        } else {
            return timestamp.toLocalDateTime()
                    .atZone(ZoneId.of(cs.getString(ZONE_ID_COLUMN_NAME)));
        }
    }
}
