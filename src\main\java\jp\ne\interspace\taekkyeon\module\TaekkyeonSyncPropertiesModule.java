/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.time.LocalDateTime;

import javax.inject.Singleton;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;

import static com.google.common.base.Strings.isNullOrEmpty;
import static java.lang.System.getProperty;
import static java.time.LocalDateTime.parse;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.INVALID_DATE_TIME;

/**
 * Guice module for the VM arguments of the creative access log synchronization batch.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonSyncPropertiesModule extends AbstractModule {

    private static final String SYNC_START_TIME_VM_ARGUMENT = "syncStartTime";
    private static final String SYNC_END_TIME_VM_ARGUMENT = "syncEndTime";

    @Override
    protected void configure() {
        // do nothing
    }

    @Provides @Singleton @SyncStartTimeResolver
    private LocalDateTime provideSyncStartTime() {
        String syncStartTime = getProperty(SYNC_START_TIME_VM_ARGUMENT);
        if (!isNullOrEmpty(syncStartTime)) {
            return parse(syncStartTime, DATE_TIME_FORMATTER);
        }
        return INVALID_DATE_TIME;
    }

    @Provides @Singleton @SyncEndTimeResolver
    private LocalDateTime provideSyncEndTime() {
        String syncEndTime = getProperty(SYNC_END_TIME_VM_ARGUMENT);
        if (!isNullOrEmpty(syncEndTime)) {
            return parse(syncEndTime, DATE_TIME_FORMATTER);
        }
        return INVALID_DATE_TIME;
    }
}
