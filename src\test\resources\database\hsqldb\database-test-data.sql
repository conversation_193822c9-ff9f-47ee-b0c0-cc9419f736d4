SET DATABASE SQL SYNTAX ORA TRUE;

INSERT INTO TRANSACTION_TEST_TABLE(TEST_ID, TEST_NAME)
VALUES(100, 'test name');

INSERT INTO MERCHANT_ACCOUNT (ACCOUNT_NO, MERCHANT_TYPE_ID, ACCOUNT_STATE, FOSTER_EMAIL, COUNTRY_CODE)
VALUES (1, 1, 1, '<EMAIL>', 'ID');

INSERT INTO MERCHANT_CAMPAIGN(CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, DESCRIPTION_EN, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE)
VALUES(1, 1, 1, 'affiliatedCampaignWithSite1', 'http://test1.ne.jp', 'http://test11.ne.jp', 'This is test', 'This is english test', 0, 0, 0, 0, 0, 0, 1, 0, TO_DATE('2016/03/05', 'YYYY/MM/DD'), TO_DATE('2016/07/03', 'YYYY/MM/DD'), 0);

INSERT INTO MERCHANT_CAMPAIGN(CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, DESCRIPTION_EN, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE)
VALUES(11, 1, 1, 'testCampaign11', 'http://test11.ne.jp', 'http://test11.ne.jp/test.jpg', 'This is test11', 'This is english test11', 0, 0, 0, 0, 0, 0, 1, 0, TO_DATE('2020/01/18', 'YYYY/MM/DD'), TO_DATE('2020/01/19', 'YYYY/MM/DD'), 0);
INSERT INTO MERCHANT_CAMPAIGN(CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, DESCRIPTION_EN, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE)
VALUES(12, 1, 1, 'testCampaign12', 'http://test12.ne.jp', 'http://test12.ne.jp/test.jpg', 'This is test12', 'This is english test12', 0, 0, 0, 0, 0, 0, 1, 0, null, null, 0);

INSERT INTO AFFILIATION_RANK_HISTORY (PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK)
VALUES (1, 1, TO_DATE('201701', 'YYYYMM'), 5);
INSERT INTO AFFILIATION_RANK_HISTORY (PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK)
VALUES (1, 2, TO_DATE('201701', 'YYYYMM'), 6);

INSERT INTO MERCHANT_CAMPAIGN_SETTING (CAMPAIGN_NO, VERIFY_CUT_FLAG, VERIFY_CUT_TARGET)
VALUES (1, 1, 1);
INSERT INTO MERCHANT_CAMPAIGN_SETTING (CAMPAIGN_NO, VERIFY_CUT_FLAG, VERIFY_CUT_TARGET)
VALUES (2, 1, 0);

INSERT INTO COUNTRY (CODE, NAME, CURRENCY, ACCESSTRADE_URL, ZONE_ID, IS_CONVERSION_PROCESSING, LOCALE, TAX_MIN_CALCULATION, EMAIL_SENDER, HAS_PENDING_MERCHANT_PAYMENT)
VALUES ('ID', 'Indonesia', 'IDR', 'https://www.accesstrade.co.id', 'Asia/Jakarta', 1, 'in-ID', 111.11, 'ACCESSTRADE Indonesia <<EMAIL>>', 0);
INSERT INTO COUNTRY (CODE, NAME, CURRENCY, ACCESSTRADE_URL, ZONE_ID, IS_CONVERSION_PROCESSING, LOCALE, TAX_MIN_CALCULATION, EMAIL_SENDER, HAS_PENDING_MERCHANT_PAYMENT)
VALUES ('MY', 'Malaysia', 'MYR', 'https://www.accesstrade.global/my', 'Asia/Kuala_Lumpur', 0, 'en-MY', 222.22, 'ACCESSTRADE Malaysia <<EMAIL>>', 0);
INSERT INTO COUNTRY (CODE, NAME, CURRENCY, ACCESSTRADE_URL, ZONE_ID, IS_CONVERSION_PROCESSING, LOCALE, TAX_MIN_CALCULATION, EMAIL_SENDER, HAS_PENDING_MERCHANT_PAYMENT)
VALUES ('SG', 'Singapore', 'SGD', 'https://www.accesstrade.global/sg', 'Asia/Singapore', 0, 'en-SG', 333.33, 'ACCESSTRADE Singapore <<EMAIL>>', 1);
INSERT INTO COUNTRY (CODE, NAME, CURRENCY, ACCESSTRADE_URL, ZONE_ID, IS_CONVERSION_PROCESSING, LOCALE, TAX_MIN_CALCULATION, EMAIL_SENDER, HAS_PENDING_MERCHANT_PAYMENT)
VALUES ('TH', 'Thailand', 'THB', 'https://www.accesstrade.in.th', 'Asia/Bangkok', 1, 'th-TH', 555.55, 'ACCESSTRADE Thailand <<EMAIL>>', 1);
INSERT INTO COUNTRY (CODE, NAME, CURRENCY, ACCESSTRADE_URL, ZONE_ID, IS_CONVERSION_PROCESSING, LOCALE, TAX_MIN_CALCULATION, EMAIL_SENDER, HAS_PENDING_MERCHANT_PAYMENT)
VALUES ('VN', 'Vietnam', 'VND', 'https://www.accesstrade.vn', 'Asia/Ho_Chi_Minh', 1, 'vi-VN', 666.66, 'ACCESSTRADE Vietnam <<EMAIL>>', 1);
INSERT INTO COUNTRY (CODE, NAME, CURRENCY, ACCESSTRADE_URL, ZONE_ID, IS_CONVERSION_PROCESSING, LOCALE, TAX_MIN_CALCULATION, EMAIL_SENDER, HAS_PENDING_MERCHANT_PAYMENT)
VALUES ('JP', 'japan', 'JPN', 'https://www.accesstrade.ne.jp', 'Asia/Tokyo', 0, 'jp-JP', 777.77, 'ACCESSTRADE Japan <<EMAIL>>', 0);

INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME, CONVERSION_ID, SITE_ID)
VALUES ('creative_access_log_summaries', TO_DATE('2024-07-21 01:02:03', 'YYYY-MM-DD HH:MI:SS'), 'ORACLE', 101, 1001);
INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME, CONVERSION_ID, SITE_ID, COUNTRY_CODE)
VALUES ('click_anomaly_detection', TO_DATE('2024-07-21 02:03:04', 'YYYY-MM-DD HH:MI:SS'), 'ORACLE', 102, 1002, 'TH');
INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME, CONVERSION_ID, SITE_ID, COUNTRY_CODE)
VALUES ('click_anomaly_detection', TO_DATE('2024-07-21 02:03:05', 'YYYY-MM-DD HH:MI:SS'), 'ORACLE', 103, 1003, 'VN');

INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME)
VALUES ('global_conversions', TO_DATE('2024-07-22 03:04:05', 'YYYY-MM-DD HH:MI:SS'), 'ORACLE');
INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME)
VALUES ('conversions', TO_DATE('2024-07-23 03:04:05', 'YYYY-MM-DD HH:MI:SS'), 'ORACLE');

INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME, CONVERSION_ID, COUNTRY_CODE, SITE_ID, CLOSED_MONTH)
VALUES ('table_1', TO_DATE('2024-07-29 22:33:44', 'YYYY-MM-DD HH:MI:SS'), 'ORACLE', 45678, 'TH', 3333, TO_DATE('2024-08-01', 'YYYY-MM-DD'));

INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME, COUNTRY_CODE)
VALUES ('table_a', TO_DATE('2024-07-30 12:34:56', 'YYYY-MM-DD HH:MI:SS'), 'ORACLE', 'SG');

INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME, COUNTRY_CODE)
VALUES ('global_conversion_status', TO_DATE('2024-07-31 11:22:33', 'YYYY-MM-DD HH:MI:SS'), 'ORACLE', 'SG');
INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME, COUNTRY_CODE)
VALUES ('global_conversion_status', TO_DATE('2024-07-31 11:22:33', 'YYYY-MM-DD HH:MI:SS'), 'ORACLE', 'TH');

INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME, COUNTRY_CODE)
VALUES ('userData', TO_DATE('2024-08-01 11:22:33', 'YYYY-MM-DD HH:MI:SS'), 'REDSHIFT', 'MY');
INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME, COUNTRY_CODE)
VALUES ('userData', TO_DATE('2024-08-01 11:22:33', 'YYYY-MM-DD HH:MI:SS'), 'REDSHIFT', 'SG');

INSERT INTO MONTHLY_CLOSING (CLOSED_MONTH, TARGET_MONTH, TEMPORARY_CLOSING_FLAG, COUNTRY_CODE) VALUES ('202404', '202405', 1, 'TH');
INSERT INTO MONTHLY_CLOSING (CLOSED_MONTH, TARGET_MONTH, TEMPORARY_CLOSING_FLAG, COUNTRY_CODE) VALUES ('202403', '202404', 0, 'SG');
INSERT INTO MONTHLY_CLOSING (CLOSED_MONTH, TARGET_MONTH, TEMPORARY_CLOSING_FLAG, COUNTRY_CODE) VALUES ('202402', '202403', 1, 'MY');
