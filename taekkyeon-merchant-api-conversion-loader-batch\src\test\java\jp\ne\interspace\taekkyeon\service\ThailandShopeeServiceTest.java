/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.google.gson.JsonParser;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import jp.ne.interspace.taekkyeon.model.ClickConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.Merchant;
import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryInsertRequest;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;

import static jp.ne.interspace.taekkyeon.model.CampaignAdvType.REGULAR;
import static jp.ne.interspace.taekkyeon.model.Merchant.SHOPEE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

/**
 * Unit test for {@link IndonesiaShopeeService}.
 *
 * <AUTHOR> Shin
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class ThailandShopeeServiceTest {

    private static final long CAMPAIGN_ID = 123L;
    private static final String EXISTING_13 = "Existing13";
    private static final String EXISTING_18 = "Existing18";
    private static final String EXISTING_21 = "Existing21";
    private static final String CATE_23 = "cate23";
    private static final String CATE_30 = "cate30";
    private static final String CATE_45 = "cate45";
    private static final String CATE_55 = "cate55";
    private static final String CATE_65 = "cate65";
    private static final String CATE_70 = "cate70";
    private static final String CATE_80 = "cate80";
    private static final String CATE_95 = "cate95";
    private static final String CATE_100 = "cate100";
    private static final String CATE_105 = "cate105";
    private static final String CATE_120 = "cate120";
    private static final String CATE_140 = "cate140";
    private static final String CATE_145 = "cate145";
    private static final String CATE_150 = "cate150";
    private static final String CATE_160 = "cate160";
    private static final String DEFAULT_CATE = "cate0";
    private static final String SHOP_ID = "SHopID";
    private static final ZoneId ZONE_ID = ZoneId.of("Asia/Jakarta");
    private static final int PRODUCT_RESULT_ID = 3;
    private static final int CATEGORY_RESULT_ID = 30;
    private static final String CUSTOMER_TYPE = "customerType";
    private static final String CLICK_ID = "clickId";
    private static final String CHECKOUT_ID = "checkoutId";
    private static final String CONVERSION_ID = "conversionId";
    private static final BigDecimal GROSS_COMMISSION = new BigDecimal("400");
    private static final long PURCHASE_TIME1 = 1689613100;
    private static final ZonedDateTime TIME1 = ZonedDateTime.of(2023, 7, 17, 23, 58, 20,
            0, ZoneId.of("Asia/Jakarta"));
    private static final long PURCHASE_TIME3 = 1683686287;
    private static final ZonedDateTime TIME3 = ZonedDateTime.of(2023, 5, 16, 14, 11, 40,
            0, ZoneId.of("Asia/Jakarta"));
    private static final BigDecimal CAPPED_COMMISSION = new BigDecimal("410");
    private static final BigDecimal TOTAL_BRAND_COMMISSION = new BigDecimal("420");
    private static final BigDecimal ESTIMATED_TOTAL_COMMISSION = new BigDecimal("430");
    private static final BigDecimal ITEM_PRICE = new BigDecimal("100");
    private static final BigDecimal ACTUAL_AMOUNT = new BigDecimal("110");
    private static final BigDecimal ITEM_COMMISSION = new BigDecimal("10");
    private static final BigDecimal GROSS_BRAND_COMMISSION = new BigDecimal("20");
    private static final String MODEL_ID = "modelId";
    private static final String GLOBAL_CATEGORY_LV1_NAME = "cate0";
    private static final String BUYER_TYPE = "EXISTING";
    private static final String UTM_CONTENT = "12345-RKVALUEQQQ-url";
    private static final String UTM_CONTENT_677
            = "12345-RKVALUEQQQ-url-ca75910166da03ff9d4655a0338e6b09";
    private static final String MD5_HASHED_CAMPAIGN_ID_677
            = "ca75910166da03ff9d4655a0338e6b09";
    private static final Merchant SHOPEE_MERCHANT = SHOPEE;
    private static final String ORDERED_IN_SAME_SHOP = "ORDERED_IN_SAME_SHOP";
    private static final BigDecimal ITEM_SELLER_COMMISSION = new BigDecimal("20");

    @InjectMocks @Spy
    private ThailandShopeeService underTest;

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryDataWhenMatchSpecialGlobalCategoryLv1NameAndTotalCategoryEqualExisting13()
            throws Exception {
        // given
        BigDecimal totalCategory = BigDecimal.ONE;

        // when
        String actual = underTest
                .getCategory(totalCategory);

        // then
        assertEquals(EXISTING_13, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectDataWhenNotMatchSpecialGlobalCategoryLv1Name()
            throws Exception {
        // given
        BigDecimal totalCategory = BigDecimal.valueOf(10);

        // when
        String actual = underTest.getCategory(totalCategory);

        // then
        assertEquals("cate100", actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryDataWhenMatchSpecialGlobalCategoryLv1NameAndTotalCategoryIsDefaultCategory()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(11));

        // then
        assertEquals(DEFAULT_CATE, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionNotBelongToTotalCategorySpecial()
            throws Exception {
        // given
        BigDecimal rsCategory = BigDecimal.valueOf(11);

        // when
        String actual = underTest.getCategory(rsCategory);

        // then
        assertEquals(DEFAULT_CATE, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionBelongToExisting18()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(1.6));

        // then
        assertEquals(EXISTING_18, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionBelongToExisting21()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(2));

        // then
        assertEquals(EXISTING_21, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate23()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(2.3));

        // then
        assertEquals(CATE_23, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate30()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(3.0));

        // then
        assertEquals(CATE_30, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate45()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(4.5));

        // then
        assertEquals(CATE_45, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate55()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(5.5));

        // then
        assertEquals(CATE_55, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate65()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(6.5));

        // then
        assertEquals(CATE_65, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate70()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(7.0));

        // then
        assertEquals(CATE_70, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate80()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(8.0));

        // then
        assertEquals(CATE_80, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate95()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(9.5));

        // then
        assertEquals(CATE_95, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate100()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(10.0));

        // then
        assertEquals(CATE_100, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate105()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(10.5));

        // then
        assertEquals(CATE_105, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate120()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(12.0));

        // then
        assertEquals(CATE_120, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate140()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(14.0));

        // then
        assertEquals(CATE_140, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate145()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(14.5));

        // then
        assertEquals(CATE_145, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate150()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(15.0));

        // then
        assertEquals(CATE_150, actual);
    }

    @Test
    public void testGetCategoryShouldReturnCorrectCategoryWhenIsGlobalCategoryLv1SpecialNameAndTotalCommissionIsCate165()
            throws Exception {
        // when
        String actual = underTest.getCategory(BigDecimal.valueOf(16.0));

        // then
        assertEquals(CATE_160, actual);
    }

    @Test
    public void testCalculateCommissionRatioShouldReturnCorrectDataWhenItemCommissionAndActualAmountIsNotNull()
            throws Exception {
        // when
        BigDecimal actual = underTest.calculateCommissionRatio(BigDecimal.valueOf(1000),
                BigDecimal.valueOf(10000));

        // then
        assertEquals(new BigDecimal("10.0000"), actual);
    }

    @Test
    public void testCalculateCommissionRatioShouldReturnCorrectDataWhenItemCommissionNullAndActualAmountNotNull()
            throws Exception {
        // when
        BigDecimal actual = underTest.calculateCommissionRatio(null,
                BigDecimal.valueOf(10000));

        // then
        assertEquals(new BigDecimal("0"), actual);
    }

    @Test
    public void testCalculateCommissionRatioShouldReturnCorrectDataWhenItemCommissionNotNullAndActualAmountNull()
            throws Exception {
        // when
        BigDecimal actual = underTest.calculateCommissionRatio(BigDecimal.valueOf(1000),
                null);

        // then
        assertEquals(new BigDecimal("0"), actual);
    }

    @Test
    public void testGetClickIdFromShouldReturnEmptyWhenSingleData() throws Exception {
        // given
        String[] utmContent = new String[] {"1"};

        // when
        String actual = underTest.getClickIdFrom(utmContent);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testParseShouldReturnEmptyListWhenResponseBodyIsNull() throws Exception {
        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(null);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testParseShouldCallCreateConversionDetailsMethodWhenTargetMcnIdIsTrue()
            throws Exception {
        // given
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType", null);
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION,
                PURCHASE_TIME1, CAPPED_COMMISSION, TOTAL_BRAND_COMMISSION,
                ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE, UTM_CONTENT_677, null, null,
                Arrays.asList(shopeeOrder));
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(
                Arrays.asList(shopeeNode), null);
        String responseBody = "{}";
        doReturn(conversionReport).when(underTest).parseConversionReport(
                new JsonParser().parse(responseBody).getAsJsonObject());
        List<ConversionRegistrationDetails> details = Arrays
                .asList(mock(ConversionRegistrationDetails.class));
        long campaignId = 677;
        doReturn(campaignId).when(underTest).getCampaignId();
        doReturn(MD5_HASHED_CAMPAIGN_ID_677).when(underTest).hashMd5By(campaignId);
        doReturn(details).when(underTest).createConversionDetails("RKVALUEQQQ",
                "existing", TIME1, shopeeOrder, shopeeNode);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(responseBody);

        // then
        assertNotNull(actual);
        verify(underTest).createConversionDetails("RKVALUEQQQ", "existing", TIME1,
                shopeeOrder, shopeeNode);
    }

    @Test
    public void testParseShouldNotCallCreateConversionDetailsMethodWhenTargetMcnIdIsFalse()
            throws Exception {
        // given
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType", null);
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION,
                PURCHASE_TIME1, CAPPED_COMMISSION, TOTAL_BRAND_COMMISSION,
                ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE, UTM_CONTENT, null, null,
                Arrays.asList(shopeeOrder));
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(
                Arrays.asList(shopeeNode), null);
        String responseBody = "{}";
        doReturn(conversionReport).when(underTest).parseConversionReport(
                new JsonParser().parse(responseBody).getAsJsonObject());
        long campaignId = 677;
        doReturn(campaignId).when(underTest).getCampaignId();
        doReturn(MD5_HASHED_CAMPAIGN_ID_677).when(underTest).hashMd5By(campaignId);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(responseBody);

        // then
        assertNotNull(actual);
        verify(underTest, never()).createConversionDetails("RKVALUEQQQ", "existing",
                TIME1,
                shopeeOrder, shopeeNode);
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenGrossBrandCommissionNotNull()
            throws Exception {
        // given
        ShopeeItem shopeeItem3 = new ShopeeItem("id3", null, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, GROSS_BRAND_COMMISSION, null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, "globalCategoryLv3Name", ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                Arrays.asList(shopeeItem3));
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION,
                PURCHASE_TIME1, CAPPED_COMMISSION, TOTAL_BRAND_COMMISSION,
                ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE, UTM_CONTENT, null, null,
                Arrays.asList(shopeeOrder));
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).isBrandItem(SHOP_ID);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        ClickConversionRegistrationDetails subConversion =
                new ClickConversionRegistrationDetails(TIME1, CHECKOUT_ID,
                        CATEGORY_RESULT_ID, "customerType", "cate0",
                        "orderId_id3_modelId", ACTUAL_AMOUNT, CLICK_ID, 123L);
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>(
                Arrays.asList(subConversion));
        doReturn(brandItemConversions).when(underTest)
                .processConversionByBrandItemRules(TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID,
                        "customerType", "cate0", "orderId_id3_modelId", ACTUAL_AMOUNT,
                        CLICK_ID, null, false);

        doReturn("TH").when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID,
                CUSTOMER_TYPE, TIME1, shopeeOrder, shopeeNode);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        ConversionRegistrationDetails expected1 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, PRODUCT_RESULT_ID, "customerType", "Bonus",
                "orderId_id3_modelId-XTRAComm", new BigDecimal("20"), CLICK_ID);
        ConversionRegistrationDetails expected2 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType", "cate0",
                "orderId_id3_modelId", new BigDecimal("110"), CLICK_ID);
        ConversionRegistrationDetails expected3 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, PRODUCT_RESULT_ID, "customerType", "Bonus",
                "orderId_id3_modelId-XTRAComm", new BigDecimal("20"), CLICK_ID);

        assertEquals(expected2, actual.get(0));
        assertEquals(expected1, actual.get(1));
        assertEquals(expected3, actual.get(2));
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenGrossBrandCommissionIsNull()
            throws Exception {
        // given
        ShopeeItem shopeeItem1 = new ShopeeItem("id1", null, null, 1, ITEM_PRICE, null,
                ITEM_COMMISSION, null, null, MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null,
                null, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                Arrays.asList(shopeeItem1));
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION,
                PURCHASE_TIME3, CAPPED_COMMISSION, TOTAL_BRAND_COMMISSION,
                ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE, UTM_CONTENT, null, null,
                Arrays.asList(shopeeOrder));
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).isBrandItem(SHOP_ID);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID,
                CUSTOMER_TYPE, TIME3, shopeeOrder, shopeeNode);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        ConversionRegistrationDetails expected = new ConversionRegistrationDetails(TIME3,
                CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType", "Existing0",
                "orderId_id1_modelId", null, CLICK_ID);

        assertEquals(expected, actual.get(0));
    }

    @Test
    public void testCreateConversionDetailsShouldNotAddDataWhenConversionWasAddInDatabase()
            throws Exception {
        // given
        ShopeeItem shopeeItem1 = new ShopeeItem("id1", null, null, 1, ITEM_PRICE, null,
                ITEM_COMMISSION, null, null, MODEL_ID, GLOBAL_CATEGORY_LV1_NAME, null,
                null, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                Arrays.asList(shopeeItem1));
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION,
                PURCHASE_TIME3, CAPPED_COMMISSION, TOTAL_BRAND_COMMISSION,
                ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE, UTM_CONTENT, null, null,
                Arrays.asList(shopeeOrder));
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).isBrandItem(SHOP_ID);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID,
                CUSTOMER_TYPE, TIME3, shopeeOrder, shopeeNode);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        ConversionRegistrationDetails expected = new ConversionRegistrationDetails(TIME3,
                CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType", "Existing0",
                "orderId_id1_modelId", null, CLICK_ID);

        assertEquals(expected, actual.get(0));
    }

    @Test
    public void testCreateConversionDetailsShouldNotReturnDataDuplicateWhenIsDuplicateConversionCheckEnabledAndDuplicateConversion()
            throws Exception {
        // given
        ShopeeItem shopeeItem1 = new ShopeeItem("id1", null, null, 1, ITEM_PRICE, null,
                ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                Arrays.asList(shopeeItem1));
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION,
                PURCHASE_TIME3, CAPPED_COMMISSION, TOTAL_BRAND_COMMISSION,
                ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE, UTM_CONTENT, null, null,
                Arrays.asList(shopeeOrder));
        MerchantIntegrationHistoryInsertRequest request =
                mock(MerchantIntegrationHistoryInsertRequest.class);
        String key = "0-20230516-checkoutId-orderId-id1-modelId";
        doReturn(true).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(true).when(underTest).isDuplicateConversion(key);

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID,
                CUSTOMER_TYPE, TIME3, shopeeOrder, shopeeNode);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.size());
        verify(underTest).isConversionUnique(key);
        verify(underTest, never()).insertDataProceeded(request);
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenItemSellerCommissionNotNullAndGreaterThanZero()
            throws Exception {
        // given
        ShopeeItem shopeeItem3 = new ShopeeItem("id3", null, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, "globalCategoryLv3Name", ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder("orderId", "shopType",
                Arrays.asList(shopeeItem3));
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION, PURCHASE_TIME1, CAPPED_COMMISSION,
                TOTAL_BRAND_COMMISSION, ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE,
                UTM_CONTENT, null, null, Arrays.asList(shopeeOrder));
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(false).when(underTest).isBrandItem(SHOP_ID);
        doReturn(REGULAR).when(underTest).findCampaignType(CAMPAIGN_ID);
        doReturn(CAMPAIGN_ID).when(underTest).getCampaignId();

        ClickConversionRegistrationDetails subConversion =
                new ClickConversionRegistrationDetails(TIME1, CHECKOUT_ID,
                        CATEGORY_RESULT_ID, "customerType", "cate0",
                        "orderId_id3_modelId", ACTUAL_AMOUNT, CLICK_ID, 123L);
        List<ConversionRegistrationDetails> brandItemConversions = new ArrayList<>(
                Arrays.asList(subConversion));
        doReturn(brandItemConversions).when(underTest)
                .processConversionByBrandItemRules(TIME1, CHECKOUT_ID, CATEGORY_RESULT_ID,
                        "customerType", "cate0", "orderId_id3_modelId", ACTUAL_AMOUNT,
                        CLICK_ID, null, false);

        doReturn("TH").when(underTest).getTargetCountryCode();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME1, shopeeOrder, shopeeNode);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        ConversionRegistrationDetails expected1 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, PRODUCT_RESULT_ID, "customerType", "Bonus",
                "orderId_id3_modelId-XTRAComm", new BigDecimal("20"), CLICK_ID);
        ConversionRegistrationDetails expected2 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, CATEGORY_RESULT_ID, "customerType", "cate0",
                "orderId_id3_modelId", new BigDecimal("110"), CLICK_ID);
        ConversionRegistrationDetails expected3 = new ConversionRegistrationDetails(TIME1,
                CHECKOUT_ID, PRODUCT_RESULT_ID, "customerType", "Bonus",
                "orderId_id3_modelId-XTRAComm", new BigDecimal("20"), CLICK_ID);

        assertEquals(expected2, actual.get(0));
        assertEquals(expected1, actual.get(1));
        assertEquals(expected3, actual.get(2));
    }
}
