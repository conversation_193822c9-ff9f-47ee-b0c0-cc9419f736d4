/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.elasticsearch;

import java.io.IOException;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.inject.Inject;

import lombok.extern.slf4j.Slf4j;

import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchBulkOperationResponse;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchDeleteByQueryOperationResponse;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchOperationResponse;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchSearchResponse;

import static com.amazonaws.http.HttpMethodName.DELETE;
import static com.amazonaws.http.HttpMethodName.GET;
import static com.amazonaws.http.HttpMethodName.POST;
import static com.google.common.base.Preconditions.checkArgument;
import static java.lang.String.format;

/**
 * Low-level client for elasticsearch.
 *
 * <AUTHOR> Shin
 */
@Singleton @Slf4j
public class ElasticsearchClient {

    private static final String REQUEST_PATH_FORMAT = "/%s/%s/%s";
    private static final String REQUEST_SEARCH_PATH = "/_all/_search";
    private static final String REQUEST_BULK_API_PATH = "/_bulk";
    private static final String DELETE_BY_QUERY_PATH_FORMAT = "/%s/_delete_by_query?requests_per_second=%s";
    private static final String UPDATE_BY_QUERY_PATH_FORMAT = "/%s/_update_by_query?requests_per_second=%s";
    private static final String COUNT_PATH_FORMAT = "/%s/_count";

    private static final String ERROR_MESSAGE_UPDATE_DOCUMENT = "Error occurs while updating document";

    @Inject
    private RestClient client;

    @Inject
    private Gson gson;

    /**
     * Indexes a document.
     *
     * @param index
     *          the logical namespace of elasticsearch
     * @param type
     *          the type of the document
     * @param id
     *          the ID of the document
     * @param source
     *          the document body
     * @return a result of the index document operation
     * @throws IOException
     *          when occurred exception
     */
    public ElasticsearchOperationResponse indexDocument(String index, String type,
            String id, String source) throws IOException {
        checkParameters(index, id, source);
        Request request = new Request(POST.name(),
                format(REQUEST_PATH_FORMAT, index, type, id));
        request.setJsonEntity(source);
        return fromJson(toString(client.performRequest(request)),
                ElasticsearchOperationResponse.class);
    }

    /**
     * Returns documents by the given json query.
     *
     * @param jsonQuery
     *          json query for finding documents
     * @return documents by the given json query
     * @throws IOException
     *          when occurred exception
     */
    public ElasticsearchSearchResponse findDocumentBy(String jsonQuery)
            throws IOException {
        checkParameters(jsonQuery);
        Request request = new Request(GET.name(), REQUEST_SEARCH_PATH);
        request.setJsonEntity(jsonQuery);
        return fromJson(toString(client.performRequest(request)),
                ElasticsearchSearchResponse.class);
    }

    /**
     * Deletes a document.
     *
     * @param index
     *          the logical namespace of elasticsearch
     * @param type
     *          the type of the document
     * @param id
     *          the ID of the document
     * @return a result of the delete document operation
     * @throws IOException
     *          when occurred exception
     */
    public ElasticsearchOperationResponse deleteDocument(String index, String type,
            String id) throws IOException {
        checkParameters(index, type, id);
        Request request = new Request(DELETE.name(),
                format(REQUEST_PATH_FORMAT, index, type, id));
        return fromJson(toString(client.performRequest(request)),
                ElasticsearchOperationResponse.class);
    }

    /**
     * Indexes bulk data by the given json body.
     *
     * @param bulkApiBody
     *          {@code Bulk API Body}
     * @return a result of the bulk operations
     * @throws IOException
     *          when occurred exception
     */
    public ElasticsearchBulkOperationResponse indexBulkDocuments(String bulkApiBody)
            throws IOException {
        checkParameters(bulkApiBody);
        Request request = new Request(POST.name(), REQUEST_BULK_API_PATH);
        request.setJsonEntity(bulkApiBody);
        return fromJson(toString(client.performRequest(request)),
                ElasticsearchBulkOperationResponse.class);
    }

    /**
     * Delete data by the given json body.
     *
     * @param index
     *          the logical namespace of elasticsearch
     * @param jsonQuery
     *          json query for finding documents
     * @param requestPerSecond
     *          the given request per second count
     * @return a result of the delete operation
     * @throws IOException
     *          when occurred exception
     */
    public ElasticsearchDeleteByQueryOperationResponse delete(String index,
            String jsonQuery, int requestPerSecond) throws IOException {
        Request request = new Request(POST.name(),
                format(DELETE_BY_QUERY_PATH_FORMAT, index, requestPerSecond));
        request.setJsonEntity(jsonQuery);
        return fromJson(toString(client.performRequest(request)),
                ElasticsearchDeleteByQueryOperationResponse.class);
    }

    /**
     * Updates data by the given json body.
     *
     * @param index
     *          the logical namespace of elasticsearch
     * @param jsonQuery
     *          json query for finding documents
     * @param requestPerSecond
     *          the given request per second count
     * @return a result of the update operation
     */
    public long update(String index, String jsonQuery, int requestPerSecond) {
        Request request = new Request(POST.name(),
                format(UPDATE_BY_QUERY_PATH_FORMAT, index, requestPerSecond));
        request.setJsonEntity(jsonQuery);
        try {
            String response = toString(client.performRequest(request));
            JsonParser jsonParser = new JsonParser();
            JsonObject responseJson = jsonParser.parse(response).getAsJsonObject();
            return responseJson.get("updated").getAsLong();
        } catch (Exception e) {
            getLogger().error(ERROR_MESSAGE_UPDATE_DOCUMENT, e);
            throw new TaekkyeonException(e.getMessage());
        }
    }

    /**
     * Returns the number of counted data by the given json body.
     *
     * @param index
     *          the logical namespace of elasticsearch
     * @param jsonQuery
     *          json query for finding documents
     * @return the number of counted data by the given json body
     */
    public long count(String index, String jsonQuery) {
        Request request = new Request(GET.name(), format(COUNT_PATH_FORMAT, index));
        request.setJsonEntity(jsonQuery);
        try {
            String response = toString(client.performRequest(request));
            JsonParser jsonParser = new JsonParser();
            JsonObject responseJson = jsonParser.parse(response).getAsJsonObject();
            return responseJson.get("count").getAsLong();
        } catch (Exception e) {
            getLogger().error(ERROR_MESSAGE_UPDATE_DOCUMENT, e);
            throw new TaekkyeonException(e.getMessage());
        }
    }

    @VisibleForTesting
    void checkParameters(String... parameters) {
        String message = "Invalid String parameter(s) for Elasticsearch API";
        checkArgument(parameters != null, message);
        for (String parameter : parameters) {
            checkArgument(Strings.nullToEmpty(parameter).trim().length() > 0, message);
        }
    }

    @VisibleForTesting
    <T> T fromJson(String json, Class<T> clazz) {
        return gson.fromJson(json, clazz);
    }

    @VisibleForTesting
    String toString(Response response) throws IOException {
        return EntityUtils.toString(response.getEntity());
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }
}
