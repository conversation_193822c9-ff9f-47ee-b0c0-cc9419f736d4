/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.exception;

/**
 * Taekkyeon exception.
 *
 * <AUTHOR>
 */
public class TaekkyeonException extends RuntimeException {

    private static final long serialVersionUID = 4335674958015027942L;

    @SuppressWarnings("unused")
    private TaekkyeonException() {
        // forbid no argument constructor
    }

    public TaekkyeonException(String message) {
        super(message);
    }
}
