/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding site traffic data.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum SiteTrafficSource implements ValueEnum {

    EMPTY(0),
    ORGANIC_SEARCH(1),
    PAID_SEARCH(2),
    OTHER_ORGANIC_TRAFFIC(3),
    OTHER_PAID_TRAFFIC(4),
    FACEBOOK_PAGE(5),
    FACEBOOK_ADS(6),
    YOUTUBE_CHANNEL(7),
    TWITTER(8),
    INSTAGRAM(9),
    OTHER_SOCIAL_NETWORK(10),
    EMAIL_MARKETING(11),
    AFFILIATE_NETWORK(12),
    OTHER_ADVERTISING_NETWORK(13);
    private final int value;
}

