/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.ses;

import java.util.Locale;
import java.util.Map;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.module.EmailSendingEnabledResolver;

import static lombok.AccessLevel.PACKAGE;

/**
 * Email sender class that consumes any errors that happen during email sending.
 *
 * <AUTHOR>
 */
@Slf4j
@Singleton
public class FailSafeEmailSender {

    private static final String EMAIL_TEMPLATE_DETAIL = "sender: {} - recipient: {} - subject: {} - template: {} - parameters: {} - locale: {}";
    private static final String EMAIL_NON_TEMPLATE_DETAIL = "sender: {} - recipient: {} - subject: {} - messageTags: {}";
    private static final String EMAIL_TEMPLATE_DETAIL_WITH_ATTACHMENT = "sender: {} - recipient: {} - subject: {} - template: {} - parameters: {} - attachmentPath: {} - locale: {}";
    private static final String EMAIL_SENDING_SUCCESS_MESSAGE_FORMAT = "Email has been sent: %s";
    private static final String EMAIL_SENDING_ERROR_MESSAGE_FORMAT = "Could not send email: %s";
    private static final String EMAIL_SENDING_EXCEPTION_ERROR_MESSAGE = "Exception during email sending:";

    @Inject
    private TemplateEmailSender emailSender;

    @Inject @EmailSendingEnabledResolver @Getter(PACKAGE) @VisibleForTesting
    private boolean emailSendingEnabled;

    /**
     * Sends an HTML email from the given sender to the given recipient with the given
     * subject and body.
     *
     * @param sender
     *            the email address of the sender
     * @param recipient
     *            the email address of the recipient
     * @param subject
     *            the subject of the email
     * @param bodyTemplateFileName
     *            the file name of the template to be used for the email body
     * @param templateParameters
     *            the parameters to be used in the email body template
     * @param locale
     *            the {@link Locale} of the email template
     */
    public void send(String sender, String recipient, String subject,
            String bodyTemplateFileName, Object templateParameters, Locale locale) {
        getLogger().info("Call send");
        if (isEmailSendingEnabled()) {
            try {
                emailSender.send(sender, recipient, subject, bodyTemplateFileName,
                        templateParameters, locale);
                getLogger().info(
                        String.format(EMAIL_SENDING_SUCCESS_MESSAGE_FORMAT,
                                EMAIL_TEMPLATE_DETAIL),
                        sender, recipient, subject, bodyTemplateFileName,
                        templateParameters, locale);
            } catch (Exception ex) {
                getLogger().error(
                        String.format(EMAIL_SENDING_ERROR_MESSAGE_FORMAT,
                                EMAIL_TEMPLATE_DETAIL),
                        sender, recipient, subject, bodyTemplateFileName,
                        templateParameters, locale);
                getLogger().error(EMAIL_SENDING_EXCEPTION_ERROR_MESSAGE, ex);
            }
        }
    }

    /**
     * Sends an HTML email from the given sender to the given recipient with the given
     * subject and body and message tags.
     *
     * @param sender
     *            the email address of the sender
     * @param recipient
     *            the email address of the recipient
     * @param subject
     *            the subject of the email
     * @param body
     *            the body of the email
     * @param messageTags
     *            the parameters to be used in the email message tag
     */
    public void sendWithCustomTags(String sender, String recipient, String subject,
            String body, Map<String, String> messageTags) {
        getLogger().info("Call sendWithCustomTags");
        if (isEmailSendingEnabled()) {
            try {
                emailSender.sendWithCustomTags(sender, recipient, subject, body,
                        messageTags);
                getLogger().info(
                        String.format(EMAIL_SENDING_SUCCESS_MESSAGE_FORMAT,
                                EMAIL_NON_TEMPLATE_DETAIL),
                        sender, recipient, subject, messageTags);
            } catch (Exception ex) {
                getLogger().error(
                        String.format(EMAIL_SENDING_ERROR_MESSAGE_FORMAT,
                                EMAIL_NON_TEMPLATE_DETAIL),
                        sender, recipient, subject, messageTags);
                getLogger().error(EMAIL_SENDING_EXCEPTION_ERROR_MESSAGE, ex);
            }
        }
    }

    /**
     * Sends an HTML mail with attachment from given criteria.
     *
     * @param sender
     *            the email address of the sender
     * @param recipient
     *            the email address of the recipient
     * @param subject
     *            the subject of the email
     * @param bodyTemplateFileName
     *            the file name of the template to be used for the email body
     * @param templateParameters
     *            the parameters to be used in the email body template
     * @param attachmentPath
     *            the file path of attachment
     * @param locale
     *            the {@link Locale} of the email template
     */
    public void sendWithAttachment(String sender, String recipient, String subject,
            String bodyTemplateFileName, Object templateParameters,
            String attachmentPath, Locale locale) {
        getLogger().info("Call sendWithAttachment");
        if (isEmailSendingEnabled()) {
            try {
                emailSender.sendWithAttachmentFile(sender, recipient, subject,
                        bodyTemplateFileName, templateParameters, attachmentPath,
                        locale);
                getLogger().info(
                        String.format(EMAIL_SENDING_SUCCESS_MESSAGE_FORMAT,
                                EMAIL_TEMPLATE_DETAIL_WITH_ATTACHMENT),
                        sender, recipient, subject, bodyTemplateFileName,
                        templateParameters, attachmentPath, locale);
            } catch (Exception ex) {
                getLogger().error(
                        String.format(EMAIL_SENDING_ERROR_MESSAGE_FORMAT,
                                EMAIL_TEMPLATE_DETAIL_WITH_ATTACHMENT),
                        sender, recipient, subject, bodyTemplateFileName,
                        templateParameters, attachmentPath, locale);
                getLogger().error(EMAIL_SENDING_EXCEPTION_ERROR_MESSAGE, ex);
            }
        }
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }
}
