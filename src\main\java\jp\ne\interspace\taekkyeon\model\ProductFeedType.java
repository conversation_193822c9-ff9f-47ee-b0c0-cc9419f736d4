/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding all the different product feed types.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum ProductFeedType implements ValueEnum {

    PRODUCT(0, "p"),
    CREATIVE(1, "c");

    private final int value;
    private final String prefix;
}
