/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding merchant integration history details for test.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class MerchantIntegrationHistoryDetailTest {
    private final String key;
    private final Long campaignId;
    private final LocalDateTime conversionOccursDate;
    private final String data;
    private final String merchant;
}
