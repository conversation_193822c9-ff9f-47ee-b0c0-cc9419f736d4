/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.util.Arrays;
import java.util.List;

import com.amazonaws.services.s3.model.PutObjectResult;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.module.Environment;
import jp.ne.interspace.taekkyeon.persist.aws.redshift.mapper.core.RedshiftDataMapper;
import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceClient;

import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link RedshiftInsertClickAnomalyDetectionService}.
 *
 * <AUTHOR> NT
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class RedshiftServiceTest {

    private static final String FAILED_TO_PROCESS_CSV_FILE = "Failed to process CSV file FileName on S3 or deactivate data";
    private static final Country COUNTRY = Country.INDONESIA;

    private static final Environment ENVIRONMENT = Environment.DEV;
    private static final String CSV_CONTENTS = "csvContents";
    private static final String FILE_NAME = "FileName";
    private static final String BUCKET_NAME = "BucketName";
    private static final String REDSHIFT_CREDENTIALS = "redshiftCredentials";
    private static final String SUCCESS = "success";
    private static final String ERROR = "error";
    private static final String S3_FILE_PATH_FORMAT = "s3://BucketName/FileName";
    private static final String TEMPORARY_TABLE_KEY = "temporaryTableKey";

    @Spy
    private RedshiftService underTest;

    @Mock
    private SimpleStorageServiceClient s3Client;

    @Mock
    private RedshiftDataMapper redshiftDataMapper;

    @Mock
    private Logger logger;

    @Test
    public void testSafelyUpsertDataShouldProcessSuccessfullyAndCallCorrectMethodsWhenNotOccurredException() {
        // given
        PutObjectResult result = mock(PutObjectResult.class);
        doReturn(s3Client).when(underTest).getS3Client();
        doReturn(BUCKET_NAME).when(underTest).getBucketName();
        when(s3Client.putObject(BUCKET_NAME, FILE_NAME, CSV_CONTENTS)).thenReturn(result);
        doNothing().when(underTest).upsertData(COUNTRY, ENVIRONMENT, FILE_NAME,
                TEMPORARY_TABLE_KEY);
        doNothing().when(underTest).move(FILE_NAME, SUCCESS);

        // when
        underTest.safelyUpsertData(COUNTRY, ENVIRONMENT, CSV_CONTENTS, FILE_NAME,
                TEMPORARY_TABLE_KEY);

        // then
        verify(underTest).upsertData(COUNTRY, ENVIRONMENT, FILE_NAME,
                TEMPORARY_TABLE_KEY);
        verify(underTest).move(FILE_NAME, SUCCESS);
        verify(underTest, never()).move(FILE_NAME, ERROR);
        verify(underTest, never()).getLogger();
    }

    @Test
    public void testSafelyUpsertDataShouldMoveFileToErrorPathWhenOccurredException() {
        // given
        doReturn(s3Client).when(underTest).getS3Client();
        RuntimeException exception = new RuntimeException();
        doReturn(BUCKET_NAME).when(underTest).getBucketName();
        doThrow(exception).when(s3Client).putObject(BUCKET_NAME, FILE_NAME, CSV_CONTENTS);
        doNothing().when(underTest).move(FILE_NAME, ERROR);
        doReturn(logger).when(underTest).getLogger();

        // when
        try {
            underTest.safelyUpsertData(COUNTRY, ENVIRONMENT, CSV_CONTENTS, FILE_NAME,
                    TEMPORARY_TABLE_KEY);
            fail();

            // then
        } catch (Exception e) {
            verify(underTest, never()).upsertData(any(Country.class),
                    any(Environment.class), anyString(), anyString());
            verify(underTest).move(FILE_NAME, ERROR);
            verify(underTest, never()).move(FILE_NAME, SUCCESS);
            verify(logger).error(FAILED_TO_PROCESS_CSV_FILE, e);
        }
    }

    @Test
    public void testUpsertDataShouldCallCopyWhenCalled() {
        // given
        doReturn(redshiftDataMapper).when(underTest).getRedShiftDataMapper();
        doReturn(BUCKET_NAME).when(underTest).getBucketName();
        doReturn(REDSHIFT_CREDENTIALS).when(underTest).getRedshiftCredentials();

        // when
        underTest.upsertData(COUNTRY, ENVIRONMENT, FILE_NAME, TEMPORARY_TABLE_KEY);

        // then
        verify(underTest, times(5)).getRedShiftDataMapper();
        verify(redshiftDataMapper).createTemporarySyncTable(COUNTRY, ENVIRONMENT,
                TEMPORARY_TABLE_KEY);
        verify(redshiftDataMapper).copy(COUNTRY, ENVIRONMENT, S3_FILE_PATH_FORMAT,
                REDSHIFT_CREDENTIALS, TEMPORARY_TABLE_KEY);
        verify(redshiftDataMapper).updateDataByTemporaryTable(COUNTRY, ENVIRONMENT,
                TEMPORARY_TABLE_KEY);
        verify(redshiftDataMapper).dropTemporaryTable(COUNTRY, ENVIRONMENT,
                TEMPORARY_TABLE_KEY);
        verify(redshiftDataMapper).insertDataByTemporaryTable(COUNTRY, ENVIRONMENT,
                TEMPORARY_TABLE_KEY);
    }

    @Test
    public void testMoveShouldCallCopyAndDeleteWhenS3ObjectIsExisting() {
        // given
        doReturn(s3Client).when(underTest).getS3Client();
        doReturn(BUCKET_NAME).when(underTest).getBucketName();
        when(s3Client.doesObjectExist(BUCKET_NAME, FILE_NAME)).thenReturn(true);

        // when
        underTest.move(FILE_NAME, SUCCESS);

        // then
        verify(s3Client).copyObject(BUCKET_NAME, FILE_NAME, BUCKET_NAME,
                "success/FileName");
        verify(s3Client).deleteObject(BUCKET_NAME, FILE_NAME);
    }

    @Test
    public void testMoveShouldNotCallCopyAndDeleteWhenS3ObjectIsNotExisting() {
        // given
        doReturn(s3Client).when(underTest).getS3Client();
        when(s3Client.doesObjectExist(BUCKET_NAME, FILE_NAME)).thenReturn(false);
        doReturn(BUCKET_NAME).when(underTest).getBucketName();

        // when
        underTest.move(FILE_NAME, SUCCESS);

        // then
        verify(s3Client, never()).copyObject(anyString(), anyString(), anyString(),
                anyString());
        verify(s3Client, never()).deleteObject(anyString(), anyString());
    }

    @Test
    public void testDropAllTemporaryTablesShouldProcessSuccessfullyAndCallCorrectMethodsWhenCalled() {
        // given
        doReturn(redshiftDataMapper).when(underTest).getRedShiftDataMapper();
        List<String> tableNames = Arrays.asList("table1");
        when(redshiftDataMapper.findDistinctTemporaryTables(COUNTRY, ENVIRONMENT))
                .thenReturn(tableNames);

        // when
        underTest.dropAllTemporaryTables(COUNTRY, ENVIRONMENT);

        // then
        verify(underTest, times(2)).getRedShiftDataMapper();
        verify(redshiftDataMapper).dropTemporaryTableBy("table1");
    }
}
