/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core;

import java.time.LocalDateTime;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;

/**
 * Integration test for {@link AffiliationRankHistoryMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class AffiliationRankHistoryMapperTest {

    @Inject
    private AffiliationRankHistoryMapper underTest;

    @Test
    public void testFindRankByShouldReturnCorrectDataWhenCalled() {
        // when
        int actual = underTest.findRankBy(1, 1, LocalDateTime.of(2021, 2, 1, 0, 0, 0));

        // then
        assertEquals(5, actual);
    }
}
