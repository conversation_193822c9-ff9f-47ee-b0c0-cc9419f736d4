/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for handling a conversion update request.
 *
 * <AUTHOR> Van
 */
public interface ConversionUpdateRequestMapper {

    /**
        INSERT INTO
            conversion_update_request (
                file_name,
                error_count,
                campaign_id,
                staff_email,
                data_count)
        VALUES (
            #{fileName},
            0,
            #{campaignId},
            #{staffEmail},
            #{dataCount})
     */
    @Multiline String INSERT = "";

    /**
     * Inserts a conversion update request.
     *
     * @param fileName
     *      given the file name
     * @param campaignId
     *      the given campaign ID
     * @param staffEmail
     *      the given staff Email
     * @param dataCount
     *      the given data Count
     * @return number of inserted count
     */
    @Insert(INSERT)
    int insertRequest(@Param("fileName") String fileName,
            @Param("campaignId") long campaignId,
            @Param("staffEmail") String staffEmail,
            @Param("dataCount") int dataCount);
}
