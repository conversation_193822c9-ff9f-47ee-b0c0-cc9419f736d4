/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.name.Named;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import jp.ne.interspace.taekkyeon.util.TaekkyeonHttpClient;

import static org.apache.http.Consts.UTF_8;

/**
 * Injection provider for {@link TaekkyeonHttpClient}.
 *
 * <AUTHOR>
 */
public class TaekkyeonHttpClientProvider implements Provider<TaekkyeonHttpClient> {

    static final String BIND_KEY_HTTP_MAX_TOTAL_CONNECTIONS = "http.maxTotalConnections";
    static final String BIND_KEY_HTTP_CONNECTION_TIMEOUT = "http.connectionTimeout";
    static final String BIND_KEY_HTTP_SOCKET_TIMEOUT = "http.socketTimeout";
    static final String BIND_KEY_REDIRECTS_ENABLED = "http.redirectsEnabled";

    private final TaekkyeonHttpClient taekkyeonHttpClient;

    /**
     * Creates a {@link TaekkyeonHttpClientProvider} instance with the given parameters.
     *
     * @param maxTotalConnections
     *            the maximum limit of HTTP connections
     * @param connectionTimeoutInMillis
     *            the maximum limit of connection timeout in milliseconds
     * @param socketTimeoutInMillis
     *            the maximum limit of socket timeout in milliseconds
     * @param redirectsEnabled
     *            the redirect enabled
     */
    @Inject
    public TaekkyeonHttpClientProvider(
            @Named(BIND_KEY_HTTP_MAX_TOTAL_CONNECTIONS) int maxTotalConnections,
            @Named(BIND_KEY_HTTP_CONNECTION_TIMEOUT) int connectionTimeoutInMillis,
            @Named(BIND_KEY_HTTP_SOCKET_TIMEOUT) int socketTimeoutInMillis,
            @Named(BIND_KEY_REDIRECTS_ENABLED) boolean redirectsEnabled) {

        HttpClientConnectionManager connectionManager = createConnectionManager(
                maxTotalConnections);

        CloseableHttpClient httpClient = createHttpClientWith(connectionManager);

        RequestConfig requestConfiguration = createRequestConfigurationWith(
                connectionTimeoutInMillis, socketTimeoutInMillis, redirectsEnabled);

        taekkyeonHttpClient = new TaekkyeonHttpClient(httpClient, requestConfiguration);
    }

    private HttpClientConnectionManager createConnectionManager(int maxTotalConnections) {
        PoolingHttpClientConnectionManager connectionManager =
                new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(maxTotalConnections);
        connectionManager.setDefaultMaxPerRoute(maxTotalConnections);

        ConnectionConfig connectionConfig = createConnectionConfig();
        connectionManager.setDefaultConnectionConfig(connectionConfig);

        return connectionManager;
    }

    private ConnectionConfig createConnectionConfig() {
        return ConnectionConfig.custom().setCharset(UTF_8).build();
    }

    private CloseableHttpClient createHttpClientWith(
            HttpClientConnectionManager connectionManager) {
        return HttpClients.custom().setConnectionManager(connectionManager).build();
    }

    private RequestConfig createRequestConfigurationWith(int connectionTimeoutInMillis,
            int socketTimeoutInMillis, boolean redirectsEnabled) {
        return RequestConfig.custom()
                .setConnectTimeout(connectionTimeoutInMillis)
                .setSocketTimeout(socketTimeoutInMillis)
                .setRedirectsEnabled(redirectsEnabled)
                .build();
    }

    @Override
    public TaekkyeonHttpClient get() {
        return taekkyeonHttpClient;
    }

}
