/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding conversion rank calculation types.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum ConversionRankCalculationType implements ValueEnum {

    NUMBER_OF_CONVERSIONS(0),
    TOTAL_COMMISION(1),
    PUBLISHER_COMMISSION(2);

    private final int value;
}
