/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

import com.google.common.collect.ImmutableList;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.openqa.selenium.By;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.common.HttpHelper;
import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.AdsScraper;
import jp.ne.interspace.taekkyeon.model.GoogleAd;
import jp.ne.interspace.taekkyeon.model.Location;
import jp.ne.interspace.taekkyeon.model.Pair;
import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceBucket;
import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceClient;
import jp.ne.interspace.taekkyeon.util.TaekkyeonHttpClient;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link WebScraperService}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class WebScraperServiceTest {

    private static final String SEARCH_URL = "https://www.google.co.id/q=random";
    private static final int RANDOM_SEARCH_WORD_LENGTH = 7;

    private static final String LANGUAGE = "en";
    private static final String USER_AGENT = "userAgent";
    private static final BigDecimal LATITIDE = BigDecimal.valueOf(1.23);
    private static final BigDecimal LONGITUDE = BigDecimal.valueOf(4.56);

    private static final String GOOGLE_TRACKING_TAG_ATTRIBUTE_NAME = "data-rw";
    private static final String GOOGLE_HREF_TAG_ATTRIBUTE_NAME = "href";

    private static final String COUNTRY_CODE = "SG";
    private static final String SEARCH_KEYWORD = "searchKeyword";
    private static final String COUNTRY = "Japan";
    private static final String CITY = "Tokyo";
    private static final String DOMAIN = "domain";
    private static final String GOOGLE_URL = "https://www.google.co.id";
    private static final String HEADING = "heading";
    private static final String DESCRIPTION = "description";
    private static final long CAMPAIGN_ID = 1;

    private static final int AD_HEADLINE_MAX_BYTE_LENGTH = 256;
    private static final int AD_DESCRIPTION_MAX_BYTE_LENGTH = 512;
    private static final int COUNTRY_MAX_BYTE_LENGTH = 64;
    private static final int CITY_MAX_BYTE_LENGTH = 128;
    private static final int TRACKING_TEMPLATE_MAX_BYTE_LENGTH = 64;

    private static final String DESKTOP_LOCATION_UPDATE = "DESKTOP_LOCATION_UPDATE";
    private static final String SMARTPHONE_LOCATION_UPDATE = "SMARTPHONE_LOCATION_UPDATE";
    private static final String FOOTER_LOCATION = "FOOTER_LOCATION";
    private static final String SEARCH = "SEARCH";
    private static final String COUNTRY_ELEMENT = "COUNTRY";
    private static final String SMARTPHONE_CITY = "SMARTPHONE_CITY";
    private static final String DESKTOP_CITY = "DESKTOP_CITY";
    private static final String ADS = "ADS";
    private static final String AD_HEADING = "AD_HEADING";
    private static final String AD_DESCRIPTION = "AD_DESCRIPTION";
    private static final String AD_TAG = "AD_TAG";
    private static final String AD_DOMAIN = "AD_DOMAIN";
    private static final String AD_DOMAIN_SPAN = "AD_DOMAIN_SPAN";
    private static final String SMARTPHONE_PAGE_2 = "SMARTPHONE_PAGE_2";
    private static final String DESKTOP_PAGE_2 = "DESKTOP_PAGE_2";

    private static final int PAGE_NUMBER_1 = 1;

    private static final boolean IS_MOBILE_TRUE = true;
    private static final boolean IS_MOBILE_FALSE = false;

    private static final int NUMBER_OF_REDIRECTS = 0;
    private static final int MAXIMUM_NUMBER_OF_REDIRECTS = 3;

    @InjectMocks @Spy
    private WebScraperService underTest;

    @Mock
    private StringHelper stringHelper;

    @Mock
    private TaekkyeonHttpClient taekkyeonHttpClient;

    @Mock
    private Map<String, String> googleUrls;

    @Mock
    private Logger logger;

    @Mock
    private ChromeDriver chromeDriver;

    @Mock
    private HttpHelper httpHelper;

    @Mock
    private SimpleStorageServiceBucket s3Bucket;

    @Mock
    private SimpleStorageServiceClient s3Client;

    @Captor
    private ArgumentCaptor<Map<String, Object>> permissions;

    @Captor
    private ArgumentCaptor<Map<String, Object>> coordinates;

    @Captor
    private ArgumentCaptor<Map<String, Object>> userAgentLanguageParameters;

    @Test
    public void testScrapeDataShouldReturnEmptyWhenLocationElementIsNotFound()
            throws InterruptedException, IOException {
        // given
        Location location = mock(Location.class);
        AdsScraper adsScraper = createAdScraper(location, IS_MOBILE_FALSE);
        WebElement locationElement = null;
        when(googleUrls.get(COUNTRY_CODE)).thenReturn(GOOGLE_URL);
        doReturn(chromeDriver).when(underTest).initChromeDriver();
        doNothing().when(underTest).clearCoockies(chromeDriver);
        doReturn(SEARCH_URL).when(underTest).createGoogleUrlToSearchRandomKeyword(
                GOOGLE_URL);
        doNothing().when(underTest).setSearchPath(chromeDriver, SEARCH_URL);
        doNothing().when(underTest).waitLoadTime();
        doReturn(locationElement).when(underTest).findLocationElement(chromeDriver);

        // when
        List<GoogleAd> actual = underTest.scrapeData(adsScraper);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
        verify(underTest).clearCoockies(chromeDriver);
        verify(underTest).grantGeolocationPermission(GOOGLE_URL);
        verify(underTest).updateLocation(location);
        verify(underTest).updateUserAgentAndLanguage(USER_AGENT, LANGUAGE);
        verify(underTest).setSearchPath(chromeDriver, SEARCH_URL);
        verify(underTest).waitLoadTime();
        verify(underTest, never()).getLocatorBy(anyString());
        verify(underTest, never()).findCountryFrom(any(WebElement.class));
        verify(underTest, never()).findCityFrom(any(WebElement.class));
        verify(underTest, never()).findGoogleAds(any(ChromeDriver.class), anyString(),
                anyString(), anyString(), anyBoolean());
    }

    @Test
    public void testScrapeDataShouldReturnCorrectDataWhenLocationElementIsFoundAndKeywordSearchIsNotPossible()
            throws InterruptedException, IOException {
        // given
        Location location = mock(Location.class);
        WebElement locationElement = mock(WebElement.class);
        when(googleUrls.get(COUNTRY_CODE)).thenReturn(GOOGLE_URL);
        doReturn(chromeDriver).when(underTest).initChromeDriver();
        doNothing().when(underTest).clearCoockies(chromeDriver);
        doReturn(SEARCH_URL).when(underTest).createGoogleUrlToSearchRandomKeyword(
                GOOGLE_URL);
        doNothing().when(underTest).setSearchPath(chromeDriver, SEARCH_URL);
        doNothing().when(underTest).waitLoadTime();
        doReturn(locationElement).when(underTest).findLocationElement(chromeDriver);
        doReturn(COUNTRY).when(underTest).findCountryFrom(locationElement);
        doReturn(CITY).when(underTest).findCityFrom(locationElement);
        doReturn(false).when(underTest).isKeywordSearchable(chromeDriver, SEARCH_KEYWORD);
        AdsScraper adsScraper = createAdScraper(location, IS_MOBILE_FALSE);

        // when
        List<GoogleAd> actual = underTest.scrapeData(adsScraper);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
        verify(underTest).clearCoockies(chromeDriver);
        verify(underTest).grantGeolocationPermission(GOOGLE_URL);
        verify(underTest).updateLocation(location);
        verify(underTest).updateUserAgentAndLanguage(USER_AGENT, LANGUAGE);
        verify(underTest).setSearchPath(chromeDriver, SEARCH_URL);
        verify(underTest).waitLoadTime();
        verify(underTest, never()).findGoogleAds(any(ChromeDriver.class), anyString(),
                anyString(), anyString(), anyBoolean());
        verify(underTest, never()).findCity(chromeDriver);
        verify(underTest, never()).findCityFrom(any());
        verify(underTest, never()).findCountryFrom(any());
        verify(underTest, never()).getLocatorBy(anyString());
    }

    @Test
    public void testScrapeDataShouldReturnCorrectDataWhenLocationElementIsFoundAndKeywordSearchIsPossible()
            throws InterruptedException, IOException {
        // given
        By by = mock(By.class);
        Location location = mock(Location.class);
        WebElement locationElement = mock(WebElement.class);
        GoogleAd googleAd = mock(GoogleAd.class);
        List<GoogleAd> googleAds = Arrays.asList(googleAd);
        when(googleUrls.get(COUNTRY_CODE)).thenReturn(GOOGLE_URL);
        doReturn(by).when(underTest).getLocatorBy(FOOTER_LOCATION);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(locationElement).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);
        doReturn(chromeDriver).when(underTest).initChromeDriver();
        doNothing().when(underTest).clearCoockies(chromeDriver);
        doReturn(SEARCH_URL).when(underTest)
                .createGoogleUrlToSearchRandomKeyword(GOOGLE_URL);
        doNothing().when(underTest).setSearchPath(chromeDriver, SEARCH_URL);
        doNothing().when(underTest).waitLoadTime();
        doReturn(locationElement).when(underTest).findLocationElement(chromeDriver);
        doReturn(COUNTRY).when(underTest).findCountryFrom(locationElement);
        doReturn(CITY).when(underTest).findCityFrom(locationElement);
        doReturn(true).when(underTest).isKeywordSearchable(chromeDriver, SEARCH_KEYWORD);
        doReturn(googleAds).when(underTest).findGoogleAds(chromeDriver, COUNTRY, CITY,
                USER_AGENT, IS_MOBILE_FALSE);
        AdsScraper adsScraper = createAdScraper(location, IS_MOBILE_FALSE);

        // when
        List<GoogleAd> actual = underTest.scrapeData(adsScraper);

        // then
        assertNotNull(actual);
        assertSame(googleAds, actual);
        verify(underTest).clearCoockies(chromeDriver);
        verify(underTest).grantGeolocationPermission(GOOGLE_URL);
        verify(underTest).updateLocation(location);
        verify(underTest).updateUserAgentAndLanguage(USER_AGENT, LANGUAGE);
        verify(underTest).setSearchPath(chromeDriver, SEARCH_URL);
        verify(underTest).waitLoadTime();
    }

    @Test
    public void testScrapeDataShouldReturnCorrectDataWhenLocationElementIsFoundAndKeywordSearchIsPossibleForMobile()
            throws InterruptedException, IOException {
        // given
        Location location = mock(Location.class);
        WebElement locationElement = mock(WebElement.class);
        GoogleAd googleAd = mock(GoogleAd.class);
        List<GoogleAd> googleAds = Arrays.asList(googleAd);
        when(googleUrls.get(COUNTRY_CODE)).thenReturn(GOOGLE_URL);
        doReturn(chromeDriver).when(underTest).initChromeDriver();
        doNothing().when(underTest).clearCoockies(chromeDriver);
        doReturn(SEARCH_URL).when(underTest)
                .createGoogleUrlToSearchRandomKeyword(GOOGLE_URL);
        doNothing().when(underTest).setSearchPath(chromeDriver, SEARCH_URL);
        doNothing().when(underTest).waitLoadTime();
        doReturn(locationElement).when(underTest).findLocationElement(chromeDriver);
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(FOOTER_LOCATION);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(locationElement).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);
        doReturn(COUNTRY).when(underTest).findCountryFrom(locationElement);
        doReturn(CITY).when(underTest).findCity(chromeDriver);
        doReturn(true).when(underTest).isKeywordSearchable(chromeDriver, SEARCH_KEYWORD);
        doReturn(googleAds).when(underTest).findGoogleAds(chromeDriver, COUNTRY, CITY,
                USER_AGENT, IS_MOBILE_TRUE);
        AdsScraper adsScraper = createAdScraper(location, IS_MOBILE_TRUE);

        // when
        List<GoogleAd> actual = underTest.scrapeData(adsScraper);

        // then
        assertNotNull(actual);
        assertSame(googleAds, actual);
        verify(underTest).clearCoockies(chromeDriver);
        verify(underTest).grantGeolocationPermission(GOOGLE_URL);
        verify(underTest).updateLocation(location);
        verify(underTest).updateUserAgentAndLanguage(USER_AGENT, LANGUAGE);
        verify(underTest).setSearchPath(chromeDriver, SEARCH_URL);
        verify(underTest).waitLoadTime();
    }

    @Test
    public void testCreateGoogleUrlToSearchRandomKeywordShouldReturnCorrectDataWhenCalled() {
        // given
        when(stringHelper.generateRandomAlphanumericString(RANDOM_SEARCH_WORD_LENGTH))
                .thenReturn("random");

        // when
        String actual = underTest.createGoogleUrlToSearchRandomKeyword(GOOGLE_URL);

        // then
        assertEquals("https://www.google.co.id/search?q=random", actual);
    }

    @Test
    public void testFindLocationElementShouldReturnCorrectWebElementWhenLocationLinkElementIsFound()
            throws InterruptedException {
        // given
        WebElement locationLinkElement = mock(WebElement.class);
        WebElement locationElement = mock(WebElement.class);
        By by = mock(By.class);

        doReturn(locationLinkElement).when(underTest).getLocationElementBy(chromeDriver);
        doNothing().when(underTest)
                .waitTillElementToBeClickableAndClickBy(locationLinkElement);
        doReturn(by).when(underTest).getLocatorBy(FOOTER_LOCATION);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(locationElement).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);

        // when
        WebElement actual = underTest.findLocationElement(chromeDriver);

        // then
        assertSame(locationElement, actual);
    }

    @Test
    public void testFindLocationElementShouldReturnNullWebElementWhenLocationLinkElementIsNotFound()
            throws InterruptedException {
        // given
        doReturn(null).when(underTest).getLocationElementBy(chromeDriver);

        // when
        WebElement actual = underTest.findLocationElement(chromeDriver);

        // then
        assertNull(actual);
    }

    @Test
    public void testGetLocationElementByShouldReturnCorrectWebElementWhenLocationElementForDesktopIsFound()
            throws InterruptedException {
        // given
        WebElement locationElement = mock(WebElement.class);
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(DESKTOP_LOCATION_UPDATE);
        BiConsumer<By, Exception> logExceptionWithWarnLevel = getBiConsumer();
        doReturn(logExceptionWithWarnLevel).when(underTest)
                .getLogExceptionWithWarnLevel();
        doReturn(locationElement).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithWarnLevel);

        // when
        WebElement actual = underTest.getLocationElementBy(chromeDriver);

        // then
        assertSame(locationElement, actual);
    }

    @Test
    public void testGetLocationElementByShouldReturnCorrectWebElementWhenLocationElementForDesktopIsNotFoundAndFoundForSmartphone()
            throws InterruptedException {
        // given
        WebElement locationElement = mock(WebElement.class);
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(DESKTOP_LOCATION_UPDATE);
        BiConsumer<By, Exception> logExceptionWithWarnLevel = getBiConsumer();
        doReturn(logExceptionWithWarnLevel).when(underTest)
                .getLogExceptionWithWarnLevel();
        doReturn(null).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithWarnLevel);
        doReturn(by).when(underTest).getLocatorBy(SMARTPHONE_LOCATION_UPDATE);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(locationElement).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);

        // when
        WebElement actual = underTest.getLocationElementBy(chromeDriver);

        // then
        assertSame(locationElement, actual);
    }

    @Test
    public void testFindWebElementShouldReturnNullWhenErrorOccurred() {
        // given
        By by = mock(By.class);
        RuntimeException exception = new RuntimeException();
        when(chromeDriver.findElement(by)).thenThrow(exception);
        when(by.toString()).thenReturn("By");
        doNothing().when(underTest).waitTillVisibilityOfAllElementsLocated(by);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();

        // when
        WebElement actual = underTest.findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);

        // then
        assertNull(actual);
        verify(logExceptionWithErrorLevel).accept(by, exception);
    }

    @Test
    public void testFindWebElementShouldReturnCorrectWebElementWhenCanFindElement() {
        // given
        WebElement webElement = mock(WebElement.class);
        By by = mock(By.class);
        when(chromeDriver.findElement(by)).thenReturn(webElement);
        doNothing().when(underTest).waitTillVisibilityOfAllElementsLocated(by);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();

        // when
        WebElement actual = underTest.findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);

        // then
        assertSame(webElement, actual);
        verify(logger, never()).error(anyString(), any(Exception.class));
    }

    @Test
    public void testFindCountryFromShouldReturnEmptyWhenCountryElementIsNull() {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(COUNTRY_ELEMENT);
        WebElement webElement = mock(WebElement.class);
        doReturn(null).when(underTest).findWebElementFrom(webElement, by);

        // when
        String actual = underTest.findCountryFrom(webElement);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testFindCountryFromShouldReturnCorrectCountryWhenCountryElementIsNotNull() {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(COUNTRY_ELEMENT);
        WebElement webElement = mock(WebElement.class);
        WebElement countryElement = mock(WebElement.class);

        doReturn(countryElement).when(underTest).findWebElementFrom(webElement, by);
        when(countryElement.getText()).thenReturn(COUNTRY);
        when(stringHelper.truncateToBytes(COUNTRY, COUNTRY_MAX_BYTE_LENGTH))
                .thenReturn(COUNTRY);

        // when
        String actual = underTest.findCountryFrom(webElement);

        // then
        assertSame(COUNTRY, actual);
    }

    @Test
    public void testFindCityShouldReturnEmptyWhenCityElementIsNull() {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(SMARTPHONE_CITY);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(null).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);

        // when
        String actual = underTest.findCity(chromeDriver);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testFindCityShouldReturnCorrectCityWhenCityElementIsNotNull() {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(SMARTPHONE_CITY);
        WebElement webElement = mock(WebElement.class);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(webElement).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);
        doReturn(CITY).when(underTest).findCityFrom(webElement);

        // when
        String actual = underTest.findCity(chromeDriver);

        // then
        assertSame(CITY, actual);
    }

    @Test
    public void testFindCityFromShouldReturnEmptyWhenCityElementIsNull() {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(DESKTOP_CITY);
        WebElement webElement = mock(WebElement.class);
        doReturn(null).when(underTest).findWebElementFrom(webElement, by);

        // when
        String actual = underTest.findCityFrom(webElement);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testFindCityFromShouldReturnCorrectCountryWhenCityElementIsNotNull() {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(DESKTOP_CITY);
        WebElement webElement = mock(WebElement.class);
        WebElement cityElement = mock(WebElement.class);

        doReturn(cityElement).when(underTest).findWebElementFrom(webElement, by);
        when(cityElement.getText()).thenReturn(CITY);
        when(stringHelper.truncateToBytes(CITY, CITY_MAX_BYTE_LENGTH)).thenReturn(CITY);

        // when
        String actual = underTest.findCityFrom(webElement);

        // then
        assertSame(CITY, actual);
    }

    @Test
    public void testFindWebElementFromShouldReturnEmptyWhenErrorOccurred() {
        // given
        WebElement webElement = mock(WebElement.class);
        By by = mock(By.class);

        RuntimeException exception = new RuntimeException();
        when(webElement.findElement(by)).thenThrow(exception);
        doReturn(logger).when(underTest).getLogger();
        when(by.toString()).thenReturn("By");

        // when
        WebElement actual = underTest.findWebElementFrom(webElement, by);

        // then
        assertNull(actual);
        verify(logger).error("Failed to find element from element. By=By", exception);
    }

    @Test
    public void testFindWebElementFromShouldReturnCorrectWebElementWhenWebElementIsFound() {
        // given
        WebElement webElement = mock(WebElement.class);
        WebElement expected = mock(WebElement.class);
        By by = mock(By.class);

        when(webElement.findElement(by)).thenReturn(expected);

        // when
        WebElement actual = underTest.findWebElementFrom(webElement, by);

        // then
        assertSame(expected, actual);
        verify(logger, never()).error(anyString(), any(Exception.class));
    }

    @Test
    public void testIsKeywordSearchableShouldReturnFalseWhenSearchWebElementIsNull()
            throws InterruptedException {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(SEARCH);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(null).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);
        doNothing().when(underTest).waitTillElementToBeClickableBy(by);

        // when
        boolean actual = underTest.isKeywordSearchable(chromeDriver, SEARCH_KEYWORD);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsKeywordSearchableShouldReturnTrueWhenSearchWebElementIsNotNull()
            throws InterruptedException {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(SEARCH);
        WebElement searchWebElement = mock(WebElement.class);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(searchWebElement).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);
        doNothing().when(underTest).waitLoadTime();
        doNothing().when(underTest).waitTillElementToBeClickableBy(by);

        // when
        boolean actual = underTest.isKeywordSearchable(chromeDriver, SEARCH_KEYWORD);

        // then
        assertTrue(actual);
        verify(searchWebElement).clear();
        verify(searchWebElement).sendKeys(SEARCH_KEYWORD);
        verify(searchWebElement).submit();
        verify(underTest).waitLoadTime();
    }

    @Test
    public void testIsKeywordSearchableShouldReturnFalseWhenSearchLocatorIsNull()
            throws InterruptedException {
        // given
        By by = null;
        doReturn(null).when(underTest).getLocatorBy(SEARCH);

        // when
        boolean actual = underTest.isKeywordSearchable(chromeDriver, SEARCH_KEYWORD);

        // then
        assertFalse(actual);
    }

    @Test
    public void testFindGoogleAdsShouldReturnEmptyWhenGoogleAdIsNotFound()
            throws InterruptedException {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(ADS);
        List<WebElement> adElements = Collections.emptyList();
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(adElements).when(underTest).findWebElements(chromeDriver, by,
                logExceptionWithErrorLevel);
        doReturn(adElements).when(underTest).getSecondPageGoogleAds(chromeDriver, COUNTRY,
                CITY, USER_AGENT, IS_MOBILE_FALSE, 0);

        // when
        List<GoogleAd> actual = underTest.findGoogleAds(chromeDriver, COUNTRY, CITY,
                USER_AGENT, IS_MOBILE_FALSE);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testFindGoogleAdsShouldReturnCorrectDataWhenGoogleAdsIsFound()
            throws InterruptedException {
        // given
        WebElement adElement = mock(WebElement.class);
        List<WebElement> firstPageAdElements = Arrays.asList(adElement);
        GoogleAd firstPageGoogleAd = mock(GoogleAd.class);
        GoogleAd secondPageGoogleAd = mock(GoogleAd.class);
        List<GoogleAd> secondPageGoogleAds = Arrays.asList(secondPageGoogleAd);
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(ADS);
        Pair<File, String> firstPageScreenshot = Pair.of(null, EMPTY);
        doReturn(firstPageScreenshot).when(underTest)
                .takeScreenshot(chromeDriver);
        BiConsumer<By, Exception> logExceptionWithWarnLevel = getBiConsumer();
        doReturn(logExceptionWithWarnLevel).when(underTest)
                .getLogExceptionWithWarnLevel();
        doReturn(firstPageAdElements).when(underTest).findWebElements(chromeDriver, by,
                logExceptionWithWarnLevel);
        doReturn(firstPageGoogleAd).when(underTest).findGoogleAdsFrom(adElement, COUNTRY,
                CITY, USER_AGENT, IS_MOBILE_FALSE, PAGE_NUMBER_1,
                firstPageScreenshot);
        doReturn(secondPageGoogleAds).when(underTest).getSecondPageGoogleAds(chromeDriver,
                COUNTRY, CITY, USER_AGENT, IS_MOBILE_FALSE, 1);

        // when
        List<GoogleAd> actual = underTest.findGoogleAds(chromeDriver, COUNTRY, CITY,
                USER_AGENT, IS_MOBILE_FALSE);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertSame(firstPageGoogleAd, actual.get(0));
        assertSame(secondPageGoogleAd, actual.get(1));
    }

    @Test
    public void testGetSecondPageGoogleAdsShouldReturnEmptyWhenMoreResultElementIsNotFoundForMobile()
            throws InterruptedException {
        // given
        int firstPageAdElementsSize = 1;
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(SMARTPHONE_PAGE_2);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(null).when(underTest).findWebElements(chromeDriver, by,
                logExceptionWithErrorLevel);
        doNothing().when(underTest).waitTillVisibilityOfAllElementsLocated(by);

        // when
        List<GoogleAd> actual = underTest.getSecondPageGoogleAds(chromeDriver, COUNTRY,
                CITY, USER_AGENT, IS_MOBILE_TRUE, firstPageAdElementsSize);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
        verify(underTest).waitTillVisibilityOfAllElementsLocated(by);
    }

    @Test
    public void testGetSecondPageGoogleAdsShouldReturnCorrectAdsWhenMoreResultElementIsFoundForMobile()
            throws InterruptedException {
        // given
        WebElement moreResultElement = mock(WebElement.class);
        GoogleAd secondPageGoogleAd = mock(GoogleAd.class);
        List<GoogleAd> secondPageGoogleAds = Arrays.asList(secondPageGoogleAd);
        int firstPageAdElementsSize = 1;
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(SMARTPHONE_PAGE_2);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(moreResultElement).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);
        doReturn(secondPageGoogleAds).when(underTest).getSecondPageGoogleAds(
                moreResultElement, chromeDriver, IS_MOBILE_TRUE, firstPageAdElementsSize,
                COUNTRY, CITY, USER_AGENT);
        doNothing().when(underTest).waitTillVisibilityOfAllElementsLocated(by);

        // when
        List<GoogleAd> actual = underTest.getSecondPageGoogleAds(chromeDriver, COUNTRY,
                CITY, USER_AGENT, IS_MOBILE_TRUE, firstPageAdElementsSize);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertSame(secondPageGoogleAd, actual.get(0));
    }

    @Test
    public void testGetSecondPageGoogleAdsShouldReturnEmptyWhenMoreResultElementIsNotFoundForDesktop()
            throws InterruptedException {
        // given
        int firstPageAdElementsSize = 1;
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(DESKTOP_PAGE_2);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(null).when(underTest).findWebElements(chromeDriver, by,
                logExceptionWithErrorLevel);
        doNothing().when(underTest).waitTillVisibilityOfAllElementsLocated(by);

        // when
        List<GoogleAd> actual = underTest.getSecondPageGoogleAds(chromeDriver, COUNTRY,
                CITY, USER_AGENT, IS_MOBILE_FALSE, firstPageAdElementsSize);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testGetSecondPageGoogleAdsShouldReturnCorrectADsWhenMoreResultElementIsFound()
            throws InterruptedException {
        // given
        WebElement moreResultElement = mock(WebElement.class);
        GoogleAd secondPageGoogleAd = mock(GoogleAd.class);
        List<GoogleAd> secondPageGoogleAds = Arrays.asList(secondPageGoogleAd);
        int firstPageAdElementsSize = 1;
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(DESKTOP_PAGE_2);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();
        doReturn(logExceptionWithErrorLevel).when(underTest)
                .getLogExceptionWithErrorLevel();
        doReturn(moreResultElement).when(underTest).findWebElement(chromeDriver, by,
                logExceptionWithErrorLevel);
        doReturn(secondPageGoogleAds).when(underTest).getSecondPageGoogleAds(
                moreResultElement, chromeDriver, IS_MOBILE_FALSE, firstPageAdElementsSize,
                COUNTRY, CITY, USER_AGENT);
        doNothing().when(underTest).waitTillVisibilityOfAllElementsLocated(by);

        // when
        List<GoogleAd> actual = underTest.getSecondPageGoogleAds(chromeDriver, COUNTRY,
                CITY, USER_AGENT, IS_MOBILE_FALSE, firstPageAdElementsSize);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertSame(secondPageGoogleAd, actual.get(0));
    }

    @Test
    public void testFindWebElementsShouldReturnEmptyWhenErrorOccurred() {
        // given
        By by = mock(By.class);
        RuntimeException exception = new RuntimeException();
        when(chromeDriver.findElements(by)).thenThrow(exception);
        when(by.toString()).thenReturn("By");
        doNothing().when(underTest).waitTillVisibilityOfAllElementsLocated(by);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();

        // when
        List<WebElement> actual = underTest.findWebElements(chromeDriver, by,
                logExceptionWithErrorLevel);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
        verify(underTest).waitTillVisibilityOfAllElementsLocated(by);
        verify(logExceptionWithErrorLevel).accept(by, exception);
    }

    @Test
    public void testFindWebElementsShouldReturnCorrectWebElementWhenElementIsFound() {
        // given
        WebElement webElement = mock(WebElement.class);
        List<WebElement> webElements = Arrays.asList(webElement);
        By by = mock(By.class);
        doNothing().when(underTest).waitTillVisibilityOfAllElementsLocated(by);

        when(chromeDriver.findElements(by)).thenReturn(webElements);
        BiConsumer<By, Exception> logExceptionWithErrorLevel = getBiConsumer();

        // when
        List<WebElement> actual = underTest.findWebElements(chromeDriver, by,
                logExceptionWithErrorLevel);

        // then
        assertSame(webElements, actual);
        verify(logger, never()).error(anyString(), any(Exception.class));
        verify(underTest).waitTillVisibilityOfAllElementsLocated(by);
    }

    @Test
    public void testFindGoogleAdsFromShouldReturnCorrectDataWhenCalled() {
        // given
        WebElement adElement = mock(WebElement.class);
        String finalUrl = "https://www.accesstrade.co.id/?gclid=12345";
        String googleTrackingUrl = "https://google.tracking.url";
        Pair<File, String> screenshotImage = Pair.of(null, EMPTY);
        doReturn(googleTrackingUrl).when(underTest).findGoogleTrackingUrl(adElement);
        doReturn(finalUrl).when(underTest).getFinalUrl(googleTrackingUrl, USER_AGENT, 0);
        doReturn(DOMAIN).when(underTest).getDomainFrom(adElement);
        doReturn(HEADING).when(underTest).getAdHeadingFrom(adElement);
        doReturn(DESCRIPTION).when(underTest).getAdDescriptionFrom(adElement);

        // when
        GoogleAd actual = underTest.findGoogleAdsFrom(adElement, COUNTRY, CITY,
                USER_AGENT, IS_MOBILE_FALSE, PAGE_NUMBER_1, screenshotImage);

        // then
        assertNotNull(actual);
        assertEquals(HEADING, actual.getAdHeading());
        assertEquals(DESCRIPTION, actual.getAdDescription());
        assertEquals(finalUrl, actual.getFinalUrl());
        assertEquals(DOMAIN, actual.getDomain());
        assertEquals(COUNTRY, actual.getCountry());
        assertEquals(CITY, actual.getCity());
        assertEquals(PAGE_NUMBER_1, actual.getPageNumber());
        assertEquals(screenshotImage.getLeft(), actual.getScreenshotFile());
        assertEquals(screenshotImage.getRight(), actual.getScreenshotImageUrl());
    }

    @Test
    public void testGetAdHeadingFromShouldReturnCorrectHeadingWhenElementExists() {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(AD_HEADING);
        WebElement adElement = mock(WebElement.class);
        WebElement adHeadingElement = mock(WebElement.class);
        doReturn(adHeadingElement).when(underTest).findWebElementFrom(adElement, by);
        doReturn(HEADING).when(adHeadingElement).getText();
        when(stringHelper.truncateToBytes(HEADING, AD_HEADLINE_MAX_BYTE_LENGTH))
                .thenReturn(HEADING);

        // when
        String actual = underTest.getAdHeadingFrom(adElement);

        // then
        assertSame(HEADING, actual);
    }

    @Test
    public void testGetAdHeadingFromShouldReturnEmptyHeadingWhenElementDoesNotExists() {
        // given
        WebElement adElement = mock(WebElement.class);
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(AD_HEADING);
        doReturn(null).when(underTest).findWebElementFrom(adElement, by);

        // when
        String actual = underTest.getAdHeadingFrom(adElement);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testGetAdDescriptionFromShouldReturnCorrectDescriptionWhenElementExists() {
        // given
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(AD_DESCRIPTION);
        WebElement adElement = mock(WebElement.class);
        WebElement adHeadingElement = mock(WebElement.class);
        doReturn(adHeadingElement).when(underTest).findWebElementFrom(adElement, by);
        doReturn(DESCRIPTION).when(adHeadingElement).getText();
        when(stringHelper.truncateToBytes(DESCRIPTION, AD_DESCRIPTION_MAX_BYTE_LENGTH))
                .thenReturn(DESCRIPTION);

        // when
        String actual = underTest.getAdDescriptionFrom(adElement);

        // then
        assertSame(DESCRIPTION, actual);
    }

    @Test
    public void testGetAdDescriptionFromShouldReturnEmptyDescriptionWhenElementDoesNotExists() {
        // given
        WebElement adElement = mock(WebElement.class);
        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(AD_DESCRIPTION);
        doReturn(null).when(underTest).findWebElementFrom(adElement, by);

        // when
        String actual = underTest.getAdDescriptionFrom(adElement);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testFindGoogleTrackingUrlShouldReturnCorrectUrlWhenCalled() {
        // given
        WebElement adElement = mock(WebElement.class);
        WebElement tag = mock(WebElement.class);
        String googleTrackingUrl = "https://www.googleadservices.com/pagead/aclk?sa=L";

        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(AD_TAG);
        doReturn(tag).when(underTest).findWebElementFrom(adElement, by);
        when(tag.getAttribute(GOOGLE_TRACKING_TAG_ATTRIBUTE_NAME))
                .thenReturn(googleTrackingUrl);

        // when
        String actual = underTest.findGoogleTrackingUrl(adElement);

        // then
        assertSame(googleTrackingUrl, actual);
    }

    @Test
    public void testFindGoogleTrackingUrlShouldReturnCorrectUrlWhenGoogleTrackinhTagNotAvailableAndHrefTagIsAvailable() {
        // given
        WebElement adElement = mock(WebElement.class);
        WebElement tag = mock(WebElement.class);
        String googleTrackingUrl = "https://www.googleadservices.com/pagead/aclk?sa=L";

        By by = mock(By.class);
        doReturn(by).when(underTest).getLocatorBy(AD_TAG);
        doReturn(tag).when(underTest).findWebElementFrom(adElement, by);
        when(tag.getAttribute(GOOGLE_TRACKING_TAG_ATTRIBUTE_NAME)).thenReturn(null);
        when(tag.getAttribute(GOOGLE_HREF_TAG_ATTRIBUTE_NAME))
                .thenReturn(googleTrackingUrl);

        // when
        String actual = underTest.findGoogleTrackingUrl(adElement);

        // then
        assertSame(googleTrackingUrl, actual);
    }

    @Test
    public void testGetFinalUrlShouldReturnCorrectUrlWhenRedirectUrlIsFound()
            throws IOException {
        // given
        String url = "https://www.google.com";
        String redirectUrl1 = "https://www.googleadservices.com/";
        String redirectUrl2 = "https://www.googleadservices.com/2";

        doReturn(MAXIMUM_NUMBER_OF_REDIRECTS).when(underTest)
                .getMaximumNumberOfRedirects();
        doReturn(Collections.emptyList()).when(underTest)
                .getGoogleAdsScraperBlacklistUrls();
        doReturn(Collections.emptyList()).when(underTest).getGoogleAdsScraperDomainNames();
        when(taekkyeonHttpClient.redirect(url, USER_AGENT)).thenReturn(redirectUrl1);
        doReturn(redirectUrl2).when(underTest).getFinalUrl(redirectUrl1, USER_AGENT, 1);
        when(taekkyeonHttpClient.redirect(redirectUrl1, USER_AGENT))
                .thenReturn(redirectUrl2);
        doReturn(EMPTY).when(underTest).getFinalUrl(redirectUrl2, USER_AGENT, 2);

        // when
        String actual = underTest.getFinalUrl(url, USER_AGENT, NUMBER_OF_REDIRECTS);

        // then
        assertSame(redirectUrl2, actual);
    }

    @Test
    public void testGetFinalUrlShouldReturnUrlWhenUrlIsInGoogleAdsScraperBlacklistUrls()
            throws IOException {
        // given
        String expectedUrl = "https://www.google.com";
        doReturn(Arrays.asList(expectedUrl)).when(underTest)
                .getGoogleAdsScraperBlacklistUrls();
        doReturn(MAXIMUM_NUMBER_OF_REDIRECTS).when(underTest)
                .getMaximumNumberOfRedirects();

        // when
        String actual = underTest.getFinalUrl(expectedUrl, USER_AGENT,
                NUMBER_OF_REDIRECTS);

        // then
        assertSame(expectedUrl, actual);
    }

    @Test
    public void testGetFinalUrlShouldReturnUrlWhenUrlContainAtnct3Parameter()
            throws IOException {
        // given
        String url = "https://www.google.com/atnct3='avtsq001w7q000001'";

        doReturn(MAXIMUM_NUMBER_OF_REDIRECTS).when(underTest)
                .getMaximumNumberOfRedirects();
        doReturn(Collections.emptyList()).when(underTest)
                .getGoogleAdsScraperBlacklistUrls();

        // when
        String actual = underTest.getFinalUrl(url, USER_AGENT,
                NUMBER_OF_REDIRECTS);

        // then
        assertSame(url, actual);
    }

    @Test
    public void testGetFinalUrlShouldReturnUrlWhenUrlContainAccessTradeDomain()
            throws IOException {
        // given
        String url = "https://www.accesstrade.co.id";
        String domain = "accesstrad";
        doReturn(MAXIMUM_NUMBER_OF_REDIRECTS).when(underTest)
                .getMaximumNumberOfRedirects();
        doReturn(Collections.emptyList()).when(underTest)
                .getGoogleAdsScraperBlacklistUrls();
        doReturn(Arrays.asList(domain)).when(underTest).getGoogleAdsScraperDomainNames();


        // when
        String actual = underTest.getFinalUrl(url, USER_AGENT,
                NUMBER_OF_REDIRECTS);

        // then
        assertSame(url, actual);
    }

    @Test
    public void testGetFinalUrlShouldReturnEmptyWhenUrlIsEmpty()
            throws IOException {
        // given
        doReturn(MAXIMUM_NUMBER_OF_REDIRECTS).when(underTest)
                .getMaximumNumberOfRedirects();

        // when
        String actual = underTest.getFinalUrl(EMPTY, USER_AGENT, NUMBER_OF_REDIRECTS);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testGetFinalUrlShouldReturnEmptyWhenUrlIsNull()
            throws IOException {
        // given
        doReturn(MAXIMUM_NUMBER_OF_REDIRECTS).when(underTest)
                .getMaximumNumberOfRedirects();

        // when
        String actual = underTest.getFinalUrl(null, USER_AGENT, NUMBER_OF_REDIRECTS);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testGetFinalUrlShouldReturnEmptyWhenUrlIsNotNullAndUrlIsNotEmptyAndNumberOfRedirectsIsGreaterThanMaximumNumberOfRedirects()
            throws IOException {
        // given
        String url = "https://www.google.com";
        int numberOfRedirects = 4;

        doReturn(MAXIMUM_NUMBER_OF_REDIRECTS).when(underTest)
                .getMaximumNumberOfRedirects();

        // when
        String actual = underTest.getFinalUrl(url, USER_AGENT, numberOfRedirects);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testGetFinalUrlShouldCallGetFinalUrlByWhenErrorOccurred()
            throws IOException {
        // given
        String url = "https://www.google.com";

        doReturn(MAXIMUM_NUMBER_OF_REDIRECTS).when(underTest)
                .getMaximumNumberOfRedirects();
        IOException exception = new IOException();
        when(taekkyeonHttpClient.redirect(url, USER_AGENT)).thenThrow(exception);
        doReturn(logger).when(underTest).getLogger();
        doReturn(url).when(httpHelper).getFinalUrlBy(url);
        doReturn(Collections.emptyList()).when(underTest)
                .getGoogleAdsScraperBlacklistUrls();
        doReturn(Collections.emptyList()).when(underTest).getGoogleAdsScraperDomainNames();

        // when
        String actual = underTest.getFinalUrl(url, USER_AGENT,
                NUMBER_OF_REDIRECTS);

        // then
        assertSame(url, actual);
    }

    @Test
    public void testGetDomainFromShouldReturnEmptyWhenAdDomainElementIsNotFound() {
        // given
        WebElement adElement = mock(WebElement.class);
        By adDomain = mock(By.class);
        doReturn(adDomain).when(underTest).getLocatorBy(AD_DOMAIN);
        doReturn(null).when(underTest).findWebElementFrom(adElement, adDomain);

        // when
        String actual = underTest.getDomainFrom(adElement);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testGetDomainFromShouldReturnEmptyWhenAdDomainElementIsFoundAndDomainSpanElementsIsNotIncludingHost() {
        // given
        WebElement adElement = mock(WebElement.class);
        WebElement adDomainElement = mock(WebElement.class);
        List<WebElement> adDomainSpanElements = Collections.emptyList();
        By adDomain = mock(By.class);
        doReturn(adDomain).when(underTest).getLocatorBy(AD_DOMAIN);
        By adDomainSpan = mock(By.class);
        doReturn(adDomainSpan).when(underTest).getLocatorBy(AD_DOMAIN_SPAN);
        doReturn(adDomainElement).when(underTest).findWebElementFrom(adElement, adDomain);
        doReturn(adDomainSpanElements).when(underTest)
                .findWebElementsFrom(adDomainElement, adDomainSpan);
        doReturn(false).when(underTest).isIncludedHost(adDomainSpanElements);

        // when
        String actual = underTest.getDomainFrom(adElement);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testGetDomainFromShouldReturnCorrectDomainWhenAdDomainElementIsFoundAndDomainSpanElementsIsIncludingHostAndIsFoundCorrectUrl() {
        // given
        String url = "https://www.accesstrade.co.id/";
        String expected = "www.accesstrade.co.id";
        WebElement adElement = mock(WebElement.class);
        WebElement adDomainElement = mock(WebElement.class);
        WebElement adDomainSpanElement1 = mock(WebElement.class);
        WebElement adDomainSpanElement2 = mock(WebElement.class);
        WebElement adDomainSpanElement3 = mock(WebElement.class);
        List<WebElement> adDomainSpanElements = Arrays.asList(adDomainSpanElement1,
                adDomainSpanElement2, adDomainSpanElement3);
        By adDomain = mock(By.class);
        doReturn(adDomain).when(underTest).getLocatorBy(AD_DOMAIN);
        By adDomainSpan = mock(By.class);
        doReturn(adDomainSpan).when(underTest).getLocatorBy(AD_DOMAIN_SPAN);
        doReturn(adDomainElement).when(underTest).findWebElementFrom(adElement, adDomain);
        doReturn(adDomainSpanElements).when(underTest)
                .findWebElementsFrom(adDomainElement, adDomainSpan);
        doReturn(true).when(underTest).isIncludedHost(adDomainSpanElements);
        when(adDomainSpanElement3.getText()).thenReturn(url);
        doReturn(expected).when(stringHelper).truncateToBytes(expected,
                TRACKING_TEMPLATE_MAX_BYTE_LENGTH);

        // when
        String actual = underTest.getDomainFrom(adElement);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetDomainFromShouldReturnEmptyWhenAdDomainElementIsFoundAndDomainSpanElementsIsIncludingHostAndIsNotFoundCorrectUrl() {
        // given
        String url = "accesstrade";
        WebElement adElement = mock(WebElement.class);
        WebElement adDomainElement = mock(WebElement.class);
        WebElement adDomainSpanElement1 = mock(WebElement.class);
        WebElement adDomainSpanElement2 = mock(WebElement.class);
        WebElement adDomainSpanElement3 = mock(WebElement.class);
        List<WebElement> adDomainSpanElements = Arrays.asList(adDomainSpanElement1,
                adDomainSpanElement2, adDomainSpanElement3);
        By adDomain = mock(By.class);
        doReturn(adDomain).when(underTest).getLocatorBy(AD_DOMAIN);
        By adDomainSpan = mock(By.class);
        doReturn(adDomainSpan).when(underTest).getLocatorBy(AD_DOMAIN_SPAN);
        doReturn(adDomainElement).when(underTest).findWebElementFrom(adElement, adDomain);
        doReturn(adDomainSpanElements).when(underTest)
                .findWebElementsFrom(adDomainElement, adDomainSpan);
        doReturn(true).when(underTest).isIncludedHost(adDomainSpanElements);
        when(adDomainSpanElement3.getText()).thenReturn(url);

        // when
        String actual = underTest.getDomainFrom(adElement);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testIsIncludedHostShouldReturnFalseWhenAdDomainSpanElementsIsEmpty() {
        // given
        List<WebElement> adDomainSpanElements = Collections.emptyList();

        // when
        boolean actual = underTest.isIncludedHost(adDomainSpanElements);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsIncludedHostShouldReturnFalseWhenAdDomainSpanElementsIsNotEmptyAndAdDomainSpanElementsSizeIsLessThanThree() {
        // given
        WebElement adDomainSpanElement1 = mock(WebElement.class);
        WebElement adDomainSpanElement2 = mock(WebElement.class);
        List<WebElement> adDomainSpanElements = Arrays.asList(adDomainSpanElement1,
                adDomainSpanElement2);

        // when
        boolean actual = underTest.isIncludedHost(adDomainSpanElements);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsIncludedHostShouldReturnFalseWhenAdDomainSpanElementsIsNotEmptyAndAdDomainSpanElementsSizeIsGreaterThanTwoAndThirdAdDomainSpanElementIsNull() {
        // given
        WebElement adDomainSpanElement1 = mock(WebElement.class);
        WebElement adDomainSpanElement2 = mock(WebElement.class);
        WebElement adDomainSpanElement3 = null;
        List<WebElement> adDomainSpanElements = Arrays.asList(adDomainSpanElement1,
                adDomainSpanElement2, adDomainSpanElement3);

        // when
        boolean actual = underTest.isIncludedHost(adDomainSpanElements);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsIncludedHostShouldReturnFalseWhenAdDomainSpanElementsIsNotEmptyAndAdDomainSpanElementsSizeIsGreaterThanTwoAndThirdAdDomainSpanElementIsNotContainingWww() {
        // given
        WebElement adDomainSpanElement1 = mock(WebElement.class);
        WebElement adDomainSpanElement2 = mock(WebElement.class);
        WebElement adDomainSpanElement3 = mock(WebElement.class);
        List<WebElement> adDomainSpanElements = Arrays.asList(adDomainSpanElement1,
                adDomainSpanElement2, adDomainSpanElement3);
        String text = "Install now";
        when(adDomainSpanElement3.getText()).thenReturn(text);

        // when
        boolean actual = underTest.isIncludedHost(adDomainSpanElements);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsIncludedHostShouldReturnTrueWhenAdDomainSpanElementsIsNotEmptyAndAdDomainSpanElementsSizeIsGreaterThanTwoAndThirdAdDomainSpanElementIsNotNullAndContainWww() {
        // given
        WebElement adDomainSpanElement1 = mock(WebElement.class);
        WebElement adDomainSpanElement2 = mock(WebElement.class);
        WebElement adDomainSpanElement3 = mock(WebElement.class);
        List<WebElement> adDomainSpanElements = Arrays.asList(adDomainSpanElement1,
                adDomainSpanElement2, adDomainSpanElement3);
        String text = "www.google.com";
        when(adDomainSpanElement3.getText()).thenReturn(text);

        // when
        boolean actual = underTest.isIncludedHost(adDomainSpanElements);

        // then
        assertTrue(actual);
    }

    @Test
    public void testFindWebElementsFromShouldReturnEmptyWhenErrorOccurred() {
        // given
        WebElement webElement = mock(WebElement.class);
        By by = mock(By.class);

        RuntimeException exception = new RuntimeException();
        when(webElement.findElements(by)).thenThrow(exception);
        doReturn(logger).when(underTest).getLogger();
        when(by.toString()).thenReturn("By");

        // when
        List<WebElement> actual = underTest.findWebElementsFrom(webElement, by);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
        verify(logger).error("Failed to find elements from element. By=By", exception);
    }

    @Test
    public void testFindWebElementsFromShouldReturnCorrectWebElementsWhenWebElementsIsFound() {
        // given
        WebElement webElement = mock(WebElement.class);
        WebElement webElement1 = mock(WebElement.class);
        List<WebElement> expected = Arrays.asList(webElement1);
        By by = mock(By.class);

        when(webElement.findElements(by)).thenReturn(expected);

        // when
        List<WebElement> actual = underTest.findWebElementsFrom(webElement, by);

        // then
        assertSame(expected, actual);
        verify(logger, never()).error(anyString(), any(Exception.class));
    }

    @Test
    public void testWaitLoadTimeShouldCallCorrectMethodWhenGetFooterElementReturnNull()
            throws Exception {
        // given
        doReturn(null).when(underTest).getFooterElement();

        // when
        underTest.waitLoadTime();

        // then
        verify(underTest, times(3)).sleepFor(1000);
        verify(chromeDriver, times(3))
                .executeScript(eq("window.scrollTo(0, document.body.scrollHeight)"));
        verify(chromeDriver, times(1)).executeScript(eq("window.scrollTo(0, 0)"));
    }

    @Test
    public void testWaitLoadTimeShouldCallCorrectMethodWhenGetFooterElementReturnResultNotNull()
            throws Exception {
        // given
        doReturn(mock(WebElement.class)).when(underTest).getFooterElement();

        // when
        underTest.waitLoadTime();

        // then
        verify(underTest, times(1)).sleepFor(1000);
        verify(chromeDriver, times(1))
                .executeScript(eq("window.scrollTo(0, document.body.scrollHeight)"));
        verify(chromeDriver, times(1)).executeScript(eq("window.scrollTo(0, 0)"));
    }

    @Test
    public void testGrantGeolocationPermissionShouldCallCorrectMethodWhenCalled() {
        // when
        underTest.grantGeolocationPermission(GOOGLE_URL);

        // then
        verify(chromeDriver).executeCdpCommand(eq("Browser.grantPermissions"),
                permissions.capture());
        Map<String, Object> actualPermissions = permissions.getValue();
        assertNotNull(actualPermissions);
        assertEquals(2, actualPermissions.size());
        assertTrue(actualPermissions.containsKey("origin"));
        assertSame(GOOGLE_URL, actualPermissions.get("origin"));
        assertTrue(actualPermissions.containsKey("permissions"));
        String[] permissionValues = (String[]) actualPermissions.get("permissions");
        assertNotNull(permissionValues);
        assertEquals(1, permissionValues.length);
        assertEquals("geolocation", permissionValues[0]);
    }

    @Test
    public void testUpdateLocationShouldCallCorrectMethodWhenCalled() {
        // given
        Location location = new Location(LATITIDE, LONGITUDE);

        // when
        underTest.updateLocation(location);

        // then
        verify(chromeDriver).executeCdpCommand(eq("Emulation.setGeolocationOverride"),
                coordinates.capture());
        Map<String, Object> actualCoordinates = coordinates.getValue();
        assertNotNull(actualCoordinates);
        assertEquals(3, actualCoordinates.size());
        assertTrue(actualCoordinates.containsKey("latitude"));
        assertSame(LATITIDE, actualCoordinates.get("latitude"));
        assertTrue(actualCoordinates.containsKey("longitude"));
        assertSame(LONGITUDE, actualCoordinates.get("longitude"));
        assertTrue(actualCoordinates.containsKey("accuracy"));
        assertEquals(1, actualCoordinates.get("accuracy"));
    }

    @Test
    public void testUserAgentLanguageParametersShouldCallCorrectMethodWhenCalled() {
        // when
        underTest.updateUserAgentAndLanguage(USER_AGENT, LANGUAGE);

        // then
        verify(chromeDriver).executeCdpCommand(eq("Network.setUserAgentOverride"),
                userAgentLanguageParameters.capture());
        Map<String, Object> actualUserAgentLanguageParameters =
                userAgentLanguageParameters.getValue();
        assertNotNull(actualUserAgentLanguageParameters);
        assertEquals(2, actualUserAgentLanguageParameters.size());
        assertTrue(actualUserAgentLanguageParameters.containsKey("userAgent"));
        assertSame(USER_AGENT, actualUserAgentLanguageParameters.get("userAgent"));
        assertTrue(actualUserAgentLanguageParameters.containsKey("acceptLanguage"));
        assertSame(LANGUAGE, actualUserAgentLanguageParameters.get("acceptLanguage"));
    }

    @Test
    public void testTakeScreenshotShouldReturnCorrectDataWhenCapturedScreenshotIsTrueAndCapturedScreenshotOnlyWhenSearchingIsTrue() {
        // given
        TakesScreenshot takesScreenshot = mock(TakesScreenshot.class);
        doReturn(takesScreenshot).when(underTest).getTakesScreenshot(chromeDriver);
        File screenshotFile = mock(File.class);
        when(takesScreenshot.getScreenshotAs(OutputType.FILE)).thenReturn(screenshotFile);
        String random = "random";
        when(stringHelper.generateRandomAlphanumericString(RANDOM_SEARCH_WORD_LENGTH))
                .thenReturn(random);
        String currentTime = "currentTime";
        doReturn(currentTime).when(underTest).getCurrentTime();
        String bucketName = "bucketName";
        when(s3Bucket.getBucketName()).thenReturn(bucketName);
        String fileName = "currentTime.random.png";
        String url = "url";
        when(s3Client.getUrl(bucketName, fileName)).thenReturn(url);
        doReturn(true).when(underTest).isCapturedScreenshotOnlyWhenSearching();
        doReturn(true).when(underTest).isCapturedScreenshot();

        // when
        Pair<File, String> actual = underTest.takeScreenshot(chromeDriver);

        // then
        assertEquals(Pair.of(screenshotFile, url), actual);
        verify(s3Bucket).putPublicReadObject(fileName, screenshotFile);
    }

    @Test
    public void testTakeScreenshotShouldReturnCorrectDataWhenCapturedScreenshotIsTrueAndCapturedScreenshotOnlyWhenSearchingIsFalse() {
        // given
        TakesScreenshot takesScreenshot = mock(TakesScreenshot.class);
        doReturn(takesScreenshot).when(underTest).getTakesScreenshot(chromeDriver);
        File screenshotFile = mock(File.class);
        when(takesScreenshot.getScreenshotAs(OutputType.FILE)).thenReturn(screenshotFile);
        String random = "random";
        when(stringHelper.generateRandomAlphanumericString(RANDOM_SEARCH_WORD_LENGTH))
                .thenReturn(random);
        String currentTime = "currentTime";
        doReturn(currentTime).when(underTest).getCurrentTime();
        String bucketName = "bucketName";
        when(s3Bucket.getBucketName()).thenReturn(bucketName);
        String fileName = "currentTime.random.png";
        doReturn(false).when(underTest).isCapturedScreenshotOnlyWhenSearching();
        doReturn(true).when(underTest).isCapturedScreenshot();

        // when
        Pair<File, String> actual = underTest.takeScreenshot(chromeDriver);

        // then
        assertEquals(Pair.of(screenshotFile, EMPTY), actual);
        verify(s3Bucket, never()).putObject(fileName, screenshotFile);
        verify(s3Client, never()).getUrl(bucketName, fileName);
    }

    @Test
    public void testTakeScreenshotShouldReturnCorrectDataWhenCapturedScreenshotIsFalse() {
        // given
        doReturn(false).when(underTest).isCapturedScreenshot();

        // when
        Pair<File, String> actual = underTest.takeScreenshot(chromeDriver);

        // then
        assertEquals(Pair.of(null, EMPTY), actual);
    }

    @Test
    public void testIsContainAccessTradeDomainShouldReturnTrueWhenFinalUrlContainGoogleAdsDomainNames() {
        // given
        String url = "https://www.accesstrade.co.id/";
        doReturn(ImmutableList.of("accesstra"))
                .when(underTest).getGoogleAdsScraperDomainNames();

        // when
        boolean actual = underTest.isContainAccessTradeDomain(url);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsContainAccessTradeDomainShouldReturnFalseWhenFinalUrlDoNotContainGoogleAdsDomainNames() {
        // given
        String url = "https://www.nasa.gov/";
        doReturn(Collections.emptyList())
                .when(underTest).getGoogleAdsScraperDomainNames();

        // when
        boolean actual = underTest.isContainAccessTradeDomain(url);

        // then
        assertFalse(actual);
    }

    private AdsScraper createAdScraper(Location location, boolean isMobile) {
        return new AdsScraper(COUNTRY_CODE, CAMPAIGN_ID, isMobile, SEARCH_KEYWORD,
                LANGUAGE, USER_AGENT, location, GOOGLE_URL);
    }

    @SuppressWarnings({"unchecked" })
    private BiConsumer<By, Exception> getBiConsumer() {
        return mock(BiConsumer.class);
    }
}
