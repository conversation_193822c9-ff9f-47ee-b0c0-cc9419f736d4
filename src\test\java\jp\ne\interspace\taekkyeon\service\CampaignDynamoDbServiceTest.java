/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.persist.aws.dynamodb.CampaignDynamoDbTable;

import static jp.ne.interspace.taekkyeon.model.CampaignStatus.RUNNING;
import static org.mockito.Mockito.verify;

/**
 * Unit test for {@link CampaignDynamoDbService}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class CampaignDynamoDbServiceTest {

    @InjectMocks
    private CampaignDynamoDbService underTest;

    @Mock
    private CampaignDynamoDbTable campaignDynamoDbTable;

    @Test
    public void testUpdateCampaignStatusShouldCallUpdateCampaignStatusMethodWhenCalled() {
        // given
        long campaignId = 1;

        // when
        underTest.updateCampaignStatus(campaignId, RUNNING);

        // then
        verify(campaignDynamoDbTable).updateCampaignStatus(campaignId, RUNNING);
    }
}
