/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.DeviceType.UNKNOWN;

/**
 * DTO for holding a click session data read from the DynamoDB.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class ClickSession {

    public static final ClickSession DEFAULT_CLICK_SESSION = new ClickSession(0L, 0L,
            EMPTY, EMPTY, EMPTY, LocalDateTime.MIN, UNKNOWN, new HashMap<>());

    private final long creativeId;
    private final long siteId;
    private final String clickIp;
    private final String language;
    private final String uuid;
    private final LocalDateTime clickTime;
    private final DeviceType deviceType;
    private final Map<String, String> additionalParameters;
}
