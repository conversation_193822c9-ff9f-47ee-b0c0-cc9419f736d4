/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.AffiliationStatus;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler.ValueEnumTypeHandler;

/**
 * MyBatis {@link TypeHandler} for {@link AffiliationStatus}.
 *
 * <AUTHOR>
 */
@MappedTypes(AffiliationStatus.class)
public class AffiliationStatusTypeHandler
        extends ValueEnumTypeHandler<AffiliationStatus> {

    public AffiliationStatusTypeHandler() {
        super(AffiliationStatus.class, AffiliationStatus.APPLYING);
    }
}
