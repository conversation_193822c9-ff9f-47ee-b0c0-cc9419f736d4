/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.ClickSession;
import jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable;

import static jp.ne.interspace.taekkyeon.model.DeviceType.ANDROID_TAB;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.ADDITIONAL_PARAMETERS_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.CLICK_DATE_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.DEVICE_TYPE_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.IP_ADDRESS_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.LANGUAGE_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.RK_COLUMN_NAME;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ClickSessionService}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class ClickSessionServiceTest {

    private static final long CAMPAIGN_ID = 11L;
    private static final int MAXIMUM_PARAMETER_LENGTH = 2048;
    private static final String CLICK_ID = "test click ID";

    private static final String UUID = "test uuid";
    private static final String RK = "0009IX00247C";
    private static final String CLICK_TIME = "2020-01-19 11:22:33";
    private static final String DEVICE_TYPE = "5";
    private static final String IP_ADDRESS = "127.0.0.1";
    private static final String LANGUAGE = "test language";
    private static final String PARAMETER_1 = "param1";
    private static final String PARAMETER_2 = "param2";
    private static final String PARAMETER_VALUE_1 = "value1";
    private static final String PARAMETER_VALUE_2 = "value2";
    private static final Map<String, AttributeValue> ADDITIONAL_PARAMETERS =
            ImmutableMap.of(PARAMETER_1, new AttributeValue(PARAMETER_VALUE_1),
                    PARAMETER_2, new AttributeValue(PARAMETER_VALUE_2));

    @InjectMocks @Spy
    private ClickSessionService underTest;

    @Mock
    private SessionDynamoDbTable sessionDynamoDbTable;

    @Mock
    private StringHelper stringHelper;

    @Test
    public void testFindClickSessionShouldReturnCorrectClickSessionWhenClickSessionIsFound() {
        doReturn(UUID).when(underTest).createSessionUuid(CAMPAIGN_ID, CLICK_ID);

        Optional<Map<String, AttributeValue>> session = Optional.of(
                Maps.newHashMap(new ImmutableMap.Builder<String, AttributeValue>()
                        .put(RK_COLUMN_NAME, new AttributeValue(RK))
                        .put(ADDITIONAL_PARAMETERS_COLUMN_NAME,
                                new AttributeValue().withM(ADDITIONAL_PARAMETERS))
                        .put(CLICK_DATE_COLUMN_NAME, new AttributeValue(CLICK_TIME))
                        .put(DEVICE_TYPE_COLUMN_NAME, new AttributeValue(DEVICE_TYPE))
                        .put(IP_ADDRESS_COLUMN_NAME, new AttributeValue(IP_ADDRESS))
                        .put(LANGUAGE_COLUMN_NAME, new AttributeValue(LANGUAGE))
                        .build()));
        when(sessionDynamoDbTable.fetchBy(UUID)).thenReturn(session);
        long creativeId = 12345L;
        long siteId = 98760L;
        doReturn(creativeId).when(underTest).getIdFrom(RK, 0, 6);
        doReturn(siteId).when(underTest).getIdFrom(RK, 6, 12);
        when(stringHelper.truncateToBytes(PARAMETER_VALUE_1, MAXIMUM_PARAMETER_LENGTH))
                .thenReturn(PARAMETER_VALUE_1);
        when(stringHelper.truncateToBytes(PARAMETER_VALUE_2, MAXIMUM_PARAMETER_LENGTH))
                .thenReturn(PARAMETER_VALUE_2);

        // when
        ClickSession actual = underTest.findClickSession(CAMPAIGN_ID, CLICK_ID);

        // then
        assertNotNull(actual);
        assertEquals(creativeId, actual.getCreativeId());
        assertEquals(siteId, actual.getSiteId());
        assertEquals(LocalDateTime.of(2020, 1, 19, 11, 22, 33), actual.getClickTime());
        assertEquals(ANDROID_TAB, actual.getDeviceType());
        Map<String, String> actualAdditionalParameters = actual.getAdditionalParameters();
        assertNotNull(actualAdditionalParameters);
        assertEquals(2, actualAdditionalParameters.size());
        assertTrue(actualAdditionalParameters.containsKey(PARAMETER_1));
        assertEquals(PARAMETER_VALUE_1, actualAdditionalParameters.get(PARAMETER_1));
        assertTrue(actualAdditionalParameters.containsKey(PARAMETER_2));
        assertEquals(PARAMETER_VALUE_2, actualAdditionalParameters.get(PARAMETER_2));
    }

    @Test
    public void testFindClickSessionShouldReturnDefaultClickSessionWhenClickSessionIsNotFound() {
        // given
        doReturn(UUID).when(underTest).createSessionUuid(CAMPAIGN_ID, CLICK_ID);

        Optional<Map<String, AttributeValue>> session = Optional.empty();
        when(sessionDynamoDbTable.fetchBy(UUID)).thenReturn(session);

        // when
        ClickSession actual = underTest.findClickSession(CAMPAIGN_ID, CLICK_ID);

        // then
        assertSame(ClickSession.DEFAULT_CLICK_SESSION, actual);
    }

    @Test
    public void testCreateSessionUuidShouldCorrectValueWhenCalled() {
        // when
        String actual = underTest.createSessionUuid(CAMPAIGN_ID, CLICK_ID);

        // then
        assertEquals("6512bd43d9caa6e02c990b0a82652dcatest click ID", actual);
    }

    @Test
    public void testGetIdFromShouldReturnCorrectIdWhenCalled() {
        // given
        String rk = "0016ho000dw0";

        // when
        long actual = underTest.getIdFrom(rk, 6, 12);

        // then
        assertEquals(18000, actual);
    }
}
