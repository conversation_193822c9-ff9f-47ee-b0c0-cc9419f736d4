/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core;

import java.math.BigDecimal;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import jp.ne.interspace.taekkyeon.model.CustomerSupport;

/**
 * MyBatis mapper for handling country database operations.
 *
 * <AUTHOR>
 */
public interface CountryMapper {

    String IS_CONVERSION_PROCESSING_BY_COUNTRY_CODE =
            "SELECT"
            + "    is_conversion_processing "
            + "FROM"
            + "    country "
            + "WHERE"
            + "    code = #{countryCode}";

    String UPDATE_CONVERSION_PROCESSING_FLAG_TO_ROCESSING_BY_COUNTRY_CODE =
            "UPDATE"
            + "    country "
            + "SET"
            + "    is_conversion_processing = 1 "
            + "WHERE"
            + "    is_conversion_processing = 0 "
            + "AND"
            + "    code = #{countryCode}";

    String UPDATE_CONVERSION_PROCESSING_FLAG_TO_NOT_ROCESSING_BY_COUNTRY_CODE =
            "UPDATE"
            + "    country "
            + "SET"
            + "    is_conversion_processing = 0 "
            + "WHERE"
            + "    is_conversion_processing = 1 "
            + "AND"
            + "    code = #{countryCode}";

    String SELECT_CUSTOMER_SUPPORT_BY_COUNTRY_CODE =
            "SELECT"
            + "    email_sender emailSender, "
            + "    accesstrade_url accessTradeUrl "
            + "FROM"
            + "    country "
            + "WHERE"
            + "    code = #{countryCode}";

    String SELECT_CURRENCY_BY_COUNTRY_CODE =
            "SELECT"
            + "    currency "
            + "FROM"
            + "    country "
            + "WHERE"
            + "    code = #{countryCode}";

    String SELECT_ZONE_ID_BY_COUNTRY_CODE =
            "SELECT"
            + "    zone_id "
            + "FROM"
            + "    country "
            + "WHERE"
            + "    code = #{countryCode}";

    String SELECT_LOCALE_BY_COUNTRY_CODE =
            "SELECT"
            + "    locale "
            + "FROM"
            + "    country "
            + "WHERE"
            + "    code = #{countryCode}";

    String SELECT_COUNTRY_NAME_BY_COUNTRY_CODE =
            "SELECT"
            + "    name "
            + "FROM"
            + "    country "
            + "WHERE"
            + "    code = #{countryCode}";

    String SELECT_HAS_PENDING_MERCHANT_PAYMENT_BY_COUNTRY_CODE =
            "SELECT"
            + "    has_pending_merchant_payment "
            + "FROM"
            + "    country "
            + "WHERE"
            + "    code = #{countryCode}";

    String SELECT_TAX_MIN_CALCULATION_BY_COUNTRY_CODE =
            "SELECT"
            + "    tax_min_calculation "
            + "FROM"
            + "    country "
            + "WHERE"
            + "    code = #{countryCode}";

    /**
     * Returns true, if conversion processing flag is one, otherwise false.
     *
     * @param countryCode
     *            the given country code
     * @return true, if conversion processing flag is one, otherwise false
     * @see #IS_CONVERSION_PROCESSING_BY_COUNTRY_CODE
     */
    @Select(IS_CONVERSION_PROCESSING_BY_COUNTRY_CODE)
    boolean isConversionProcessing(String countryCode);

    /**
     * Updates the conversion processing flag to processing by {@code countryCode}.
     *
     * @param countryCode
     *            the given country code
     * @return the number of updated count
     * @see #UPDATE_CONVERSION_PROCESSING_FLAG_TO_ROCESSING_BY_COUNTRY_CODE
     */
    @Update(UPDATE_CONVERSION_PROCESSING_FLAG_TO_ROCESSING_BY_COUNTRY_CODE)
    int updateConversionProcessingFlagToProcessing(String countryCode);

    /**
     * Updates the conversion processing flag to not processing by {@code countryCode}.
     *
     * @param countryCode
     *            the given country code
     * @return the number of updated count
     * @see #UPDATE_CONVERSION_PROCESSING_FLAG_TO_NOT_ROCESSING_BY_COUNTRY_CODE
     */
    @Update(UPDATE_CONVERSION_PROCESSING_FLAG_TO_NOT_ROCESSING_BY_COUNTRY_CODE)
    int updateConversionProcessingFlagToNotProcessing(String countryCode);

    /**
     * Returns the {@link CustomerSupport} by given country code.
     *
     * @param countryCode
     *            the given country code
     * @return the {@link CustomerSupport} by given country code
     * @see #SELECT_CUSTOMER_SUPPORT_BY_COUNTRY_CODE
     */
    @Select(SELECT_CUSTOMER_SUPPORT_BY_COUNTRY_CODE)
    @ConstructorArgs({ @Arg(column = "emailSender", javaType = String.class),
            @Arg(column = "accessTradeUrl", javaType = String.class) })
    CustomerSupport findCustomerSupportBy(String countryCode);

    /**
     * Returns the currency by given country code.
     *
     * @param countryCode
     *            the given country code
     * @return the currency by given country code
     * @see #SELECT_CURRENCY_BY_COUNTRY_CODE
     */
    @Select(SELECT_CURRENCY_BY_COUNTRY_CODE)
    String findCurrencyBy(String countryCode);

    /**
     * Returns the zone ID by given country code.
     *
     * @param countryCode
     *            the given country code
     * @return the zone ID by given country code
     * @see #SELECT_ZONE_ID_BY_COUNTRY_CODE
     */
    @Select(SELECT_ZONE_ID_BY_COUNTRY_CODE)
    String findZoneIdBy(String countryCode);

    /**
     * Returns the locale by given country code.
     *
     * @param countryCode
     *            the given country code
     * @return the locale by given country code
     * @see #SELECT_LOCALE_BY_COUNTRY_CODE
     */
    @Select(SELECT_LOCALE_BY_COUNTRY_CODE)
    String findLocaleBy(String countryCode);

    /**
     * Returns the country name by given country code.
     *
     * @param countryCode
     *            the given country code
     * @return the country name by given country code
     * @see #SELECT_COUNTRY_NAME_BY_COUNTRY_CODE
     */
    @Select(SELECT_COUNTRY_NAME_BY_COUNTRY_CODE)
    String findCountryNameBy(String countryCode);

    /**
     * Returns country has pending for merchant payment or not by given country code.
     *
     * @param countryCode
     *            the given country code
     * @return country has pending for merchant payment by given country code
     * @see #SELECT_HAS_PENDING_MERCHANT_PAYMENT_BY_COUNTRY_CODE
     */
    @Select(SELECT_HAS_PENDING_MERCHANT_PAYMENT_BY_COUNTRY_CODE)
    boolean isPendingForMerchantPayment(String countryCode);

    /**
     * Returns the tax min calculation by given country code.
     *
     * @param countryCode
     *            the given country code
     * @return the tax min calculation by given country code
     * @see #SELECT_TAX_MIN_CALCULATION_BY_COUNTRY_CODE
     */
    @Select(SELECT_TAX_MIN_CALCULATION_BY_COUNTRY_CODE)
    BigDecimal findTaxMinCalculationBy(String countryCode);
}
