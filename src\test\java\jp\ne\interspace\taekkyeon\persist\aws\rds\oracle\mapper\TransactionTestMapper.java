/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;

/**
 * Mapper for integration test.
 *
 * <AUTHOR> VAN NGUYEN
 */
public interface TransactionTestMapper {

    String INSERT_FIRST_VALID = "INSERT INTO TRANSACTION_TEST_TABLE VALUES(101, 'test name 101')";

    String INSERT_SECOND_VALID =
            "INSERT INTO TRANSACTION_TEST_TABLE VALUES(102, 'test name 102')";

    String INSERT_INVALID = "INSERT INTO TRANSACTION_TEST_TABLE VALUES('string', 123)";

    String COUNT_EXISTED_DATA = "SELECT COUNT(*) FROM TRANSACTION_TEST_TABLE";

    @Insert(INSERT_FIRST_VALID)
    int insertFirstValidData();

    @Insert(INSERT_SECOND_VALID)
    int insertSecondValidData();

    @Insert(INSERT_INVALID)
    int insertInvalidData();

    @Select(COUNT_EXISTED_DATA)
    int countExistedData();
}
