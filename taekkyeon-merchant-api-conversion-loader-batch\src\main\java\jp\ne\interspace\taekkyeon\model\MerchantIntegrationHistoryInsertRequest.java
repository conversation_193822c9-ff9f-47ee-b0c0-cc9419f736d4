/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding merchant integration history details.
 *
 * <AUTHOR> <PERSON>
 */
@Getter @AllArgsConstructor @EqualsAndHashCode(of = "key")
public class MerchantIntegrationHistoryInsertRequest {

    private final String key;
    private final long campaignId;
    private final LocalDateTime conversionOccursDate;
    private final String merchant;
    private final String data;
}
