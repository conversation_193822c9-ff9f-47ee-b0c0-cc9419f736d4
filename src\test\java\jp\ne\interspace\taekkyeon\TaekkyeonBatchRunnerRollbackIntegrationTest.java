/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon;

import com.google.inject.Inject;

import org.easybatch.core.job.Job;
import org.easybatch.core.job.JobReport;
import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.TransactionTestMapper;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for {@link TaekkyeonBatchRunner}.
 *
 * <AUTHOR> VAN NGUYEN
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonHsqldbOracleJunitModule.class,
        TaekkyeonPropertiesJunitModule.class, TransactionTestRollbackModule.class })
public class TaekkyeonBatchRunnerRollbackIntegrationTest {

    @Inject
    private TaekkyeonBatchRunner underTest;

    @Inject
    private TransactionTestMapper transactionTestMapper;

    @Test
    public void testExecuteJobShouldRollbackTheCurrentTransactionSessionWhenErrorOccurred() {
        // given
        Job job = underTest.buildJob();
        int expectedDataCount = 1;

        // when
        JobReport actual = underTest.executeJob(job);

        // then
        int actualDataCount = transactionTestMapper.countExistedData();
        assertNotNull(actual.getLastError());
        assertEquals(expectedDataCount, actualDataCount);
    }

}
