/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.secretsmanager;

import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.AWSSecretsManagerClient;
import com.amazonaws.services.secretsmanager.model.GetSecretValueRequest;
import com.amazonaws.services.secretsmanager.model.GetSecretValueResult;
import com.google.common.base.Strings;

import static com.google.common.base.Preconditions.checkArgument;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.AWS_CREDENTIALS_CHAIN;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.DEFAULT_AWS_REGION;

/**
 * Low-level client for Amazon Secrets Manager.
 *
 * <AUTHOR>
 * @see AWSSecretsManager
 */
public class SecretsManagerClient {

    private static SecretsManagerClient instance;
    private AWSSecretsManager secretsManager;

    /**
     * Returns single instance for {@link AWSSecretsManager}.
     *
     * @return single instance for {@link AWSSecretsManager}
     */
    public static SecretsManagerClient getInstance() {
        if (instance == null) {
            instance = new SecretsManagerClient();
            instance.createSecretsManager();
        }
        return instance;
    }

    /**
     * Returns json data of credentials.
     *
     * @param secretName
     *          the name of secrets
     * @return json data of credentials
     * @see AWSSecretsManager#getSecretValue(GetSecretValueRequest)
     */
    public String getSecretValue(String secretName) {
        checkParameter(secretName);
        GetSecretValueResult getSecretValueResult = secretsManager
                .getSecretValue(new GetSecretValueRequest().withSecretId(secretName));
        return getSecretValueResult.getSecretString();
    }

    private void createSecretsManager() {
        secretsManager = AWSSecretsManagerClient.builder()
                .withCredentials(AWS_CREDENTIALS_CHAIN)
                .withRegion(DEFAULT_AWS_REGION).build();
    }

    private void checkParameter(String parameter) {
        checkArgument(Strings.nullToEmpty(parameter).trim().length() > 0,
                "Invalid SecretsManager API request");
    }
}
