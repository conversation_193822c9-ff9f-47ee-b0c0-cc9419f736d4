/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.gson.JsonObject;

import jp.ne.interspace.taekkyeon.model.ClickConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;

import static com.google.common.base.Joiner.on;
import static com.google.common.base.Strings.isNullOrEmpty;
import static java.time.Instant.ofEpochSecond;
import static java.time.ZonedDateTime.of;
import static java.time.ZonedDateTime.ofInstant;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.COMMA;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER_YYYYMMDD;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.UNDERSCORE;

/**
 * Implements {@link AbstractShopeeService} for taiwan shopee.
 *
 * <AUTHOR> Van
 */
public class TaiwanShopeeService extends AbstractShopeeService {

    private static final int PRODUCT_RESULT_ID = 3;
    private static final int CATEGORY_RESULT_ID = 30;
    private static final String XTRA_COMM = "XTRAComm";
    private static final String SHOPEE_COMM = "ShopeeComm";

    @Override
    public List<ConversionRegistrationDetails> parse(String responseBody)
            throws Exception {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        if (!isNullOrEmpty(responseBody)) {
            JsonObject obj = getJsonParser().parse(responseBody).getAsJsonObject();
            ShopeeConversionReport conversionReport = parseConversionReport(obj);
            ZoneId zoneId = getZoneId();
            for (ShopeeNode shopeeNode : conversionReport.getNodes()) {
                String[] utmContent = shopeeNode.getUtmContent().split(HYPHEN);
                String clickId = getClickIdFrom(utmContent);
                String buyerType = shopeeNode.getBuyerType().toLowerCase();
                Instant instant = ofEpochSecond(shopeeNode.getPurchaseTime());
                ZonedDateTime conversionTime = ofInstant(instant, zoneId);
                String targetMd5CampaignId = hashMd5By(getCampaignId());
                if (isTargetCampaignId(utmContent, targetMd5CampaignId)) {
                    for (ShopeeOrder shopeeOrder : shopeeNode.getOrders()) {
                        String identifier = on(HYPHEN).join(
                                getCheckoutOrConversionId(shopeeNode),
                                shopeeOrder.getOrderId());
                        details.addAll(createConversionDetails(clickId, buyerType,
                                conversionTime, identifier, shopeeOrder));
                    }
                }
            }
        }
        return details;
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> createConversionDetails(String clickId,
            String buyerType, ZonedDateTime conversionTime, String identifier,
            ShopeeOrder shopeeOrder) throws UnsupportedEncodingException {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        List<String> products = new LinkedList<>();
        for (ShopeeItem shopeeItem : shopeeOrder.getItems()) {
            String customerType = shopeeOrder.getShopType().toLowerCase()
                    .replaceAll(UNDERSCORE, EMPTY);
            String sku = getSkuFrom(shopeeItem);
            String productId = getItemId(products, sku);
            products.add(sku);
            String category = shopeeItem.getGlobalCategoryLv1Name().replaceAll(COMMA, EMPTY);
            String conversionOccursDate = conversionTime.format(DATE_TIME_FORMATTER_YYYYMMDD);
            String modelId = shopeeItem.getModelId();
            String key = generateKey(identifier, shopeeOrder, shopeeItem,
                    conversionOccursDate, modelId);
            if (!isConversionUnique(key)) {
                continue;
            }

            BigDecimal productUnitPrice = calculateProductUnitPrice(
                    shopeeItem.getActualAmount(), conversionTime);
            boolean isAfterCutoff = validateDateTimeAfter(conversionTime,
                    of(2024, 8, 31, 23, 59, 59, 0, getZoneId()));

            String productIdShopeeComm = Stream.of(buyerType,
                            getProductId(shopeeItem, shopeeOrder.getOrderId(), productId,
                                    shopeeItem.getItemCommission(), SHOPEE_COMM))
                    .filter(Objects::nonNull).collect(Collectors.joining(HYPHEN));

            boolean isBrandItem = isBrandItem(shopeeItem.getShopId());

            List<ConversionRegistrationDetails> brandItemConversions =
                    processConversionByBrandItemRules(conversionTime, identifier,
                    CATEGORY_RESULT_ID, customerType, category, productIdShopeeComm,
                    productUnitPrice, clickId, shopeeItem.getShopId(), isBrandItem);
            String customerTypeExtraBonus = isAfterCutoff ? getNewCustomerTypeSuffix(
                    buyerType, shopeeItem.getAttributionType()) : buyerType;
            List<ConversionRegistrationDetails> bonusConversions = createExtraBonuses(
                    conversionTime, buyerType, PRODUCT_RESULT_ID, customerTypeExtraBonus,
                    category, productIdShopeeComm, clickId, shopeeItem, shopeeOrder,
                    brandItemConversions);
            details.addAll(brandItemConversions);
            details.addAll(bonusConversions);
            insertDataProceeded(createConversionProceeded(conversionTime,
                    brandItemConversions, key));
        }
        return details;
    }

    private void processExtraCommission(String clickId, String buyerType,
            ZonedDateTime conversionTime, String identifier, ShopeeOrder shopeeOrder,
            ShopeeItem shopeeItem, boolean isAfterCutoff, String productId,
            List<ConversionRegistrationDetails> brandItemConversions) {
        BigDecimal grossBrandCommission = Optional.ofNullable(shopeeItem.getItemSellerCommission())
                .filter(this::isValidCommission)
                .orElse(shopeeItem.getGrossBrandCommission());
        if (!isValidCommission(grossBrandCommission)) {
            return;
        }
        Optional<ConversionRegistrationDetails> subConversionItem =
                brandItemConversions.stream()
                        .filter(conversion ->
                                conversion instanceof ClickConversionRegistrationDetails)
                        .findFirst();

        if (!subConversionItem.isPresent()) {
            return;
        }
        String customerType = isAfterCutoff ? getNewCustomerTypeSuffix(buyerType,
                shopeeItem.getAttributionType()) : buyerType;
        String productIdExtraComm = getProductId(shopeeItem,
                shopeeOrder.getOrderId(), productId,
                shopeeItem.getItemSellerCommission(), XTRA_COMM);
        BigDecimal productUnitPrice = calculateProductUnitPrice(
                grossBrandCommission, conversionTime);
        ClickConversionRegistrationDetails subConversion =
                (ClickConversionRegistrationDetails) subConversionItem.get();
        if (!isBrandCampaign(subConversion.getSubCampaignId())) {
            Long subCampaignId = subConversion.getSubCampaignId();
            brandItemConversions.add(
                    new ClickConversionRegistrationDetails(conversionTime, identifier,
                            PRODUCT_RESULT_ID, customerType, null, productIdExtraComm,
                            productUnitPrice, clickId, subCampaignId));
        } else {
            brandItemConversions.add(
                    new ConversionRegistrationDetails(conversionTime, identifier,
                            PRODUCT_RESULT_ID, customerType, null, productIdExtraComm,
                            productUnitPrice, clickId));
        }
    }

    private String generateKey(String identifier, ShopeeOrder shopeeOrder,
            ShopeeItem shopeeItem, String conversionOccursDate, String modelId) {
        return Joiner.on(HYPHEN)
                .join(getCampaignId(), conversionOccursDate, identifier,
                        shopeeOrder.getOrderId(), shopeeItem.getItemId(), modelId);
    }
}
