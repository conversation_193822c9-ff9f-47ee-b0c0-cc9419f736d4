/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model.elasticsearch;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding the response of elasticsearch delete by query operation.
 *
 * <AUTHOR> Pachpind
 */
@AllArgsConstructor @Getter @EqualsAndHashCode
public class ElasticsearchDeleteByQueryOperationResponse {

    private final long total;
    private final long deleted;
    private final List<String> failures;
}
