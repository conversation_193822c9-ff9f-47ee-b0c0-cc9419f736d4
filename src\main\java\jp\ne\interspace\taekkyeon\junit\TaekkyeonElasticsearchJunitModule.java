/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.junit;

import java.net.MalformedURLException;
import java.net.URL;
import java.time.ZonedDateTime;

import javax.inject.Singleton;

import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;

import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;

import pl.allegro.tech.embeddedelasticsearch.EmbeddedElastic;

import jp.ne.interspace.taekkyeon.json.ElasticsearchResponseAdapter;
import jp.ne.interspace.taekkyeon.json.ProductFeedTypeAdapter;
import jp.ne.interspace.taekkyeon.json.ZonedDateTimeAdapter;
import jp.ne.interspace.taekkyeon.model.ProductFeedType;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchSearchResponse;

import static java.util.concurrent.TimeUnit.MILLISECONDS;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMBEDDED_DB_TIMEOUT_MILLISECOND;
import static org.apache.http.HttpHeaders.CONTENT_TYPE;
import static org.apache.http.entity.ContentType.APPLICATION_JSON;
import static pl.allegro.tech.embeddedelasticsearch.PopularProperties.TRANSPORT_TCP_PORT;

/**
 * Embedded elasticsearch data source module for JUnit tests.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonElasticsearchJunitModule extends AbstractModule {

    private static final String DOWNLOAD_URL = "https://s3-ap-southeast-1.amazonaws.com/accesstrade.resources/elasticsearch-7.1.0.zip";
    private static final Integer EMBEDDED_ELASTICSEARCH_TRANSPORT_TCP_PORT = 9300;

    @Override
    protected void configure() {
        install(new TaekkyeonPropertiesJunitModule());
    }

    @Provides @Singleton
    private RestClient provideRestClient() {
        return RestClient.builder(new HttpHost("localhost", 9200, "http"))
                .setDefaultHeaders(new Header[] {
                        new BasicHeader(CONTENT_TYPE, APPLICATION_JSON.getMimeType()) })
                .build();
    }

    @Provides @Singleton
    private EmbeddedElastic provideEmbeddedElastic() throws MalformedURLException {
        return EmbeddedElastic.builder()
                .withSetting("xpack.ml.enabled", "false")
                .withDownloadUrl(new URL(DOWNLOAD_URL))
                .withStartTimeout(EMBEDDED_DB_TIMEOUT_MILLISECOND, MILLISECONDS)
                .withSetting(TRANSPORT_TCP_PORT,
                        EMBEDDED_ELASTICSEARCH_TRANSPORT_TCP_PORT)
                .build();
    }

    @Provides @Singleton
    private Gson provideGson() {
        return new GsonBuilder().setLenient()
                .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
                .registerTypeAdapter(ZonedDateTime.class, new ZonedDateTimeAdapter())
                .registerTypeAdapter(ElasticsearchSearchResponse.class,
                        new ElasticsearchResponseAdapter())
                .registerTypeAdapter(ProductFeedType.class, new ProductFeedTypeAdapter())
                .create();
    }
}
