/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.util.List;

import javax.inject.Singleton;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.AffiliationRankHistoryMapper;

import static java.util.Objects.isNull;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.PIPE;

/**
 * Service layer for handling affiliation rank history data.
 *
 * <AUTHOR>
 */
@Singleton
public class AffiliationRankHistoryService {

    private static final int UNDEFINED_RANK = 0;

    @Inject
    private AffiliationRankHistoryMapper affiliationMapper;

    private LoadingCache<String, Integer> rankCache = CacheBuilder.newBuilder()
            .maximumSize(5000)
            .build(new CacheLoader<String, Integer>() {
                @Override
                public Integer load(String key) throws Exception {
                    return findRankBy(key);
                }
            });

    /**
     * Returns the rank of the campaign found by the given parameters.
     *
     * @param campaignId
     *          the identifiers of the given campaign
     * @param siteId
     *          the identifiers of the given site
     * @param conversionDate
     *          date of conversion
     * @return the rank of the campaign found by the given parameters
     */
    public Integer getRankBy(long campaignId, long siteId, LocalDateTime conversionDate) {
        return rankCache.getUnchecked(
                createRankKeyBy(campaignId, siteId, conversionDate));
    }

    private String createRankKeyBy(long campaignId, long siteId,
            LocalDateTime conversionDate) {
        return createCustomKey(campaignId, siteId, conversionDate);
    }

    private String createCustomKey(Object... objects) {
        return Joiner.on(PIPE).join(objects);
    }

    private int findRankBy(String key) {
        List<String> split = Splitter.on(PIPE).splitToList(key);
        long campaignId = Long.parseLong(split.get(0));
        long siteId = Long.parseLong(split.get(1));
        LocalDateTime conversionDate = LocalDateTime.parse(split.get(2));
        Integer rank = affiliationMapper.findRankBy(campaignId, siteId, conversionDate);
        if (!isNull(rank)) {
            return rank;
        }
        return UNDEFINED_RANK;
    }
}
