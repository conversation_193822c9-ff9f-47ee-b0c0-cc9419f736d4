/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding affiliation status.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum AffiliationStatus implements ValueEnum {

    NEW(0),
    APPLYING(1),
    APPROVED(2),
    REJECTED(3),
    CANCELED(4);

    private final int value;
}
