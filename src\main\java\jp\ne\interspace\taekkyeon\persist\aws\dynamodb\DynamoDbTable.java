/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.dynamodb;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Stream;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.AttributeValueUpdate;
import com.amazonaws.services.dynamodbv2.model.BatchWriteItemRequest;
import com.amazonaws.services.dynamodbv2.model.BatchWriteItemResult;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.DeleteItemResult;
import com.amazonaws.services.dynamodbv2.model.GetItemResult;
import com.amazonaws.services.dynamodbv2.model.PutItemResult;
import com.amazonaws.services.dynamodbv2.model.PutRequest;
import com.amazonaws.services.dynamodbv2.model.QueryRequest;
import com.amazonaws.services.dynamodbv2.model.QueryResult;
import com.amazonaws.services.dynamodbv2.model.UpdateItemRequest;
import com.amazonaws.services.dynamodbv2.model.UpdateItemResult;
import com.amazonaws.services.dynamodbv2.model.WriteRequest;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.RateLimiter;
import com.google.inject.Inject;
import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;

import static java.lang.System.getProperty;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EQUAL;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SPACE;
import static jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule.SYSTEM_PROPERTY_NAME_FOR_TEST_SEED;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;
import static lombok.AccessLevel.PACKAGE;

/**
 * Abstract data access layer representing a DynamoDB table.
 *
 * <AUTHOR> OBS DEV Team
 */
public abstract class DynamoDbTable {

    protected static final long DEFAULT_CAPACITY_UNITS = 5L;

    protected static final String STRING_TYPE = "S";
    protected static final String NUMERIC_TYPE = "N";

    @Inject @VisibleForTesting @Getter(value = PACKAGE)
    private DynamoDbClient dynamoDbClient;

    @Inject @DynamoDbNameResolver
    private UnaryOperator<String> nameResolver;

    public abstract String getKeyName();

    protected abstract String getPartialName();

    protected abstract Function<String, ImmutableMap<String, AttributeValue>>
            getKeyResolver();

    protected abstract CreateTableRequest getTestSchema();

    /**
     * Sets up a table for test uses with the name suffixed with a seed string.
     */
    public void setUpForTest() {
        if (isNotTestContext()) {
            throw new UnsupportedOperationException("Creating a table is not supported");
        }
        getDynamoDbClient().createTable(getTestSchema());
    }

    /**
     * Deletes the table for test uses corresponding by the given name.
     */
    public void tearDownForTest() {
        if (isNotTestContext()) {
            throw new UnsupportedOperationException("Deleting a table is not supported");
        }
        getDynamoDbClient().deleteTable(getTableName());
    }

    /**
     * Waits for the given DynamoDB table to become avaliable to use by polling the table
     * every 5 seconds. If polling crashes, reattempts will be made with the interval of
     * 2 seconds.
     */
    public void waitUntilAvailable() {
        RateLimiter interval = RateLimiter.create(0.5);
        getDynamoDbClient().waitUntilAvailableFor(getTableName(), interval);
    }

    /**
     * Retrieves the raw data corresponding to the given primary key and returns it as a
     * {@link Map} object wrapped with {@link Optional}.
     *
     * @param key
     *            primary key to search the data with
     * @return {@link Optional} object containing immutable raw map fetched from DynamoDB
     */
    public Optional<Map<String, AttributeValue>> fetchBy(final String key) {
        return Optional.ofNullable(getDynamoDbClient()
                .getItem(getTableName(), getMapOf(key)))
                .map(GetItemResult::getItem)
                .map(ImmutableMap::copyOf);
    }

    /**
     * Retrieves the raw data corresponding to the given primary key and returns it as a
     * {@link Map} object wrapped with {@link Optional}.
     *
     * @param key
     *            primary key to search the data with
     * @return {@link Optional} object containing immutable raw map fetched from DynamoDB
     */
    public Optional<Map<String, AttributeValue>> fetchBy(
            final Map<String, AttributeValue> key) {
        return Optional.ofNullable(getDynamoDbClient().getItem(getTableName(), key))
                .map(GetItemResult::getItem).map(ImmutableMap::copyOf);
    }

    /**
     * Queries the raw data by given query request and returns it as a {@link Map} object
     * wrapped with {@link Optional}.
     *
     * @param partitionKey contains name and value of partition key
     * @param valueExpression the given value expression
     * @param indexName the index name of table
     * @return {@link Optional} object containing immutable raw map fetched from DynamoDB
     */
    public Optional<Map<String, AttributeValue>> query(final String valueExpression,
            final Pair<String, AttributeValue> partitionKey, final String indexName) {
        Map<String, AttributeValue> expressionAttributeValues = ImmutableMap
                .of(valueExpression, partitionKey.getValue());
        QueryRequest request = new QueryRequest()
                .withTableName(getTableName())
                .withIndexName(indexName)
                .withKeyConditionExpression(
                        String.join(SPACE, partitionKey.getKey(), EQUAL, valueExpression))
                .withExpressionAttributeValues(expressionAttributeValues);
        QueryResult queryResult = getDynamoDbClient().queryItem(request);
        return Optional.ofNullable(queryResult)
                .map(result -> !result.getItems().isEmpty()
                        ? ImmutableMap.copyOf(result.getItems().get(0)) : null);
    }

    /**
     * Updates the DynamoDB table with the given key and items and returns a
     * {@link Optional} object containing a {@link PutItemResult} returned from DynamoDB
     * client.
     *
     * @param key
     *            primary key to update the data with
     * @param items
     *            a {@link Map} object consisting of {@link String} keys and
     *            {@link AttributeValue} values
     * @return {@link Optional} object containing a {@link PutItemResult} returned from
     *         DynamoDB client
     */
    public Optional<PutItemResult> update(final String key,
            final Map<String, AttributeValue> items) {
        return Optional.ofNullable(
                getDynamoDbClient().putItem(getTableName(), createItemWith(key, items)));
    }

    /**
     * Edits an existing item's attributes, or adds a new item to the table if it does not
     * already exist.
     *
     * @param key
     *            primary key to update the data with
     * @param updates
     *            a {@link Map} object consisting of {@link String} column and
     *            {@link AttributeValue} values
     * @return {@link Optional} object containing a {@link UpdateItemResult} returned from
     *         DynamoDB client
     */
    public Optional<UpdateItemResult> updateItem(final Map<String, AttributeValue> key,
            final Map<String, AttributeValueUpdate> updates) {
        return Optional.ofNullable(getDynamoDbClient().updateItem(
                new UpdateItemRequest(getTableName(), key, updates)));
    }

    /**
     * Batch-updates the DynamoDB table with the given keys and items and returns a
     * {@link Optional} object containing a {@link BatchWriteItemResult} returned from
     * DynamoDB client.
     *
     * @param keyItemMap a {@link Map} object consisting of {@link String} primary key
     *            to update the data with and a {@link Map} containing {@link String}
     *            keys and {@link AttributeValue} values
     * @return {@link Optional} object containing a {@link BatchWriteItemResult} returned
     *         from DynamoDB client
     */
    public Optional<BatchWriteItemResult> batchUpdateBy(
            final Map<String, Map<String, AttributeValue>> keyItemMap) {
        return createRequestsFrom(keyItemMap)
                .map(BatchWriteItemRequest::new)
                .map(request -> getDynamoDbClient().batchWriteItem(request));
    }

    /**
     * Deletes a single item in the DynamoDB table by the given keyand returns a
     * {@link Optional} object containing a {@link DeleteItemResult} returned from
     * DynamoDB client.
     *
     * @param key
     *            primary key to search the data with
     * @return {@link Optional} object containing a {@link DeleteItemResult} returned
     *         from DynamoDB client
     */
    public Optional<DeleteItemResult> deleteBy(final String key) {
        return Optional.ofNullable(getDynamoDbClient()
                .deleteItem(getTableName(), getMapOf(key)));
    }

    /**
     * Returns a {@link AttributeValue} containing the given numeric {@code value}.
     *
     * @param value
     *            a numeric value to wrap with {@link AttributeValue}
     * @return a {@link AttributeValue} containing the given numeric {@code value}
     */
    public static AttributeValue numberOf(final String value) {
        return new AttributeValue().withN(value);
    }

    /**
     * Returns a numeric string extracted from the given {@link AttributeValue}.
     *
     * @param numericAttribute
     *            {@link AttributeValue} containing a numeric string
     * @return a numeric string extracted from the given {@link AttributeValue}
     */
    public static String numberOf(final AttributeValue numericAttribute) {
        return Objects.nonNull(numericAttribute.getN()) ? numericAttribute.getN()
                : numericAttribute.getS();
    }

    /**
     * Returns a {@link AttributeValue} containing the given string {@code value}.
     *
     * @param value
     *            a string value to wrap with {@link AttributeValue}
     * @return a {@link AttributeValue} containing the given string {@code value}
     */
    public static AttributeValue stringOf(final String value) {
        return new AttributeValue().withS(value);
    }

    /**
     * Returns a {@link AttributeValue} containing the given {@code attributeMap}.
     *
     * @param attributeMap a {@link Map} consisting of {@link String} keys and
     *            {@link AttributeValue}s
     * @return a {@link AttributeValue} containing the given {@code attributeMap}
     */
    public static AttributeValue mapAttributeOf(
            final Map<String, AttributeValue> attributeMap) {
        return new AttributeValue().withM(attributeMap);
    }

    /**
     * Returns a {@link Map} consisting of {@link String} keys and
     * {@link AttributeValue}s extracted from the given {@code mapAttribute}.
     *
     * @param mapAttribute
     *            {@link AttributeValue} containing a map to extract
     * @return a {@link Map} consisting of {@link String} keys and
     *         {@link AttributeValue}s
     */
    public static Optional<Map<String, AttributeValue>> mapOf(
            final AttributeValue mapAttribute) {
        return Optional.ofNullable(mapAttribute)
                .map(AttributeValue::getM)
                .map(ImmutableMap::copyOf);
    }

    /**
     * Returns the {@code DynamoDB} table name.
     *
     * @return the {@code DynamoDB} table name
     */
    public String getTableName() {
        return nameResolver.apply(getPartialName()) + getTestSeedIfAvaliable();
    }

    private WriteRequest createWriteRequest(
            final Entry<String, Map<String, AttributeValue>> entry) {
        return new WriteRequest(new PutRequest(
                createItemWith(entry.getKey(), entry.getValue())));
    }

    @VisibleForTesting
    Optional<Map<String, List<WriteRequest>>> createRequestsFrom(
            final Map<String, Map<String, AttributeValue>> keyItemMap) {
        return Optional.ofNullable(keyItemMap)
                .filter(map -> map.size() > 0)
                .map(map -> map.entrySet().stream()
                        .map(this::createWriteRequest)
                        .collect(toList()))
                .map(list -> ImmutableMap.of(getTableName(), list));
    }

    @VisibleForTesting
    Map<String, AttributeValue> createItemWith(final String key,
            final Map<String, AttributeValue> items) {
        return Stream.of(items, getMapOf(key))
                .flatMap(map -> map.entrySet().stream())
                .collect(toMap(Entry::getKey, Entry::getValue));
    }

    @VisibleForTesting
    Map<String, AttributeValue> getMapOf(final String key) {
        return getKeyResolver().apply(key);
    }

    @VisibleForTesting
    String getTestSeedIfAvaliable() {
        String seed = getProperty(SYSTEM_PROPERTY_NAME_FOR_TEST_SEED);
        return getCurrentEnvironment() == DEV && !Strings.isNullOrEmpty(seed) ? seed
                : EMPTY;
    }

    private boolean isNotTestContext() {
        return "".equals(getTestSeedIfAvaliable());
    }
}
