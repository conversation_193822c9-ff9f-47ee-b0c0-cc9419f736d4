/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.junit;

import lombok.extern.slf4j.Slf4j;

import org.junit.runner.notification.RunNotifier;
import org.junit.runners.model.InitializationError;

import jp.ne.interspace.taekkyeon.persist.elasticsearch.EmbeddedElasticsearch;

/**
 * Embedded elasticsearch with HSQLDB initializer for JUnit tests.
 *
 * <AUTHOR>
 */
@Slf4j
public class TaekkyeonIntegrationTestHsqldbAndElasticsearchJunitRunner
        extends TaekkyeonIntegrationTestHsqldbJunitRunner {

    private static final long SHUTDOWN_DELAY_IN_MILLIS = 3000;

    private EmbeddedElasticsearch embeddedElasticsearch;

    /**
     * Constructor of {@link TaekkyeonIntegrationTestHsqldbAndElasticsearchJunitRunner} that
     * initializes an in-memory database instance for each JUnit integration test.
     */
    public TaekkyeonIntegrationTestHsqldbAndElasticsearchJunitRunner(Class<?> testClass)
            throws InitializationError {
        super(testClass);
        injector.injectMembers(this);
        embeddedElasticsearch = injector.getInstance(EmbeddedElasticsearch.class);
    }

    @Override
    public void run(RunNotifier notifier) {
        try {
            embeddedElasticsearch.setUp();
            super.run(notifier);
        } finally {
            embeddedElasticsearch.tearDown();
            delay(SHUTDOWN_DELAY_IN_MILLIS);
        }
    }

    private void delay(long delayMillisecond) {
        try {
            Thread.sleep(delayMillisecond);
        } catch (InterruptedException e) {
            log.error("Failed delay {} ms", delayMillisecond);
        }
    }
}
