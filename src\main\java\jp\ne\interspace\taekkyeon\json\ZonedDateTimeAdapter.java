/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.json;

import java.lang.reflect.Type;
import java.time.ZonedDateTime;

import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.JSON_DATE_TIME_FORMATTER;

/**
 * Json serializer for {@link ZonedDateTime}.
 *
 * <AUTHOR> Shin
 */
public class ZonedDateTimeAdapter implements JsonSerializer<ZonedDateTime> {

    @Override
    public JsonElement serialize(ZonedDateTime value, Type type,
            JsonSerializationContext context) {
        return new JsonPrimitive(value.format(JSON_DATE_TIME_FORMATTER));
    }

}
