/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.lang.annotation.Annotation;
import java.util.Collection;
import java.util.Set;

import javax.sql.DataSource;

import com.google.inject.Key;
import com.google.inject.PrivateModule;
import com.google.inject.Provider;

import org.apache.ibatis.io.ResolverUtil;
import org.apache.ibatis.io.ResolverUtil.Test;
import org.apache.ibatis.session.LocalCacheScope;
import org.apache.ibatis.session.SqlSessionManager;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.mybatis.guice.MyBatisModule;
import org.mybatis.guice.session.SqlSessionManagerProvider;

import static com.google.common.base.Joiner.on;
import static com.google.inject.Key.get;
import static com.google.inject.Scopes.SINGLETON;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SPACE;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Private Guice module for Mybatis mappers to load with the country-specific datasources.
 *
 * <AUTHOR> OBS DEV Team
 */
public class TaekkyeonGlobalRdsMapperModule extends PrivateModule {

    private static final String MYBATIS_ENVIRONMENT_ID = "Global ACCESSTRADE Datasource";

    private static final String MYBATIS_SQL_MAPPER_PACKAGE =
            "jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper";

    private static final String MYBATIS_TYPE_HANDLER_PACKAGE =
            "jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler";

    private static final String KEY_DISABLE_RDS_QUERY_CACHING = "disableRdsQueryCaching";

    private final Class<? extends Annotation> country;
    private final Provider<DataSource> datasource;
    private final String environmentId;

    /**
     * Constructor of {@link TaekkyeonGlobalRdsMapperModule}.
     *
     * @param country
     *            {@link Country} which country the datasource resides
     */
    public TaekkyeonGlobalRdsMapperModule(Country country) {
        this.country = country.getAnnotationClass();
        this.datasource = new TaekkyeonGlobalRdsDataSourceProvider(country,
                getCurrentEnvironment());
        this.environmentId = getEnvironmentIdFor(country);
    }

    @Override
    protected void configure() {
        install(new MyBatisModule() {

            @Override
            protected void initialize() {
                environmentId(environmentId);
                if (Boolean.getBoolean(KEY_DISABLE_RDS_QUERY_CACHING)) {
                    useCacheEnabled(false);
                    localCacheScope(LocalCacheScope.STATEMENT);
                }

                bindDataSourceProvider(datasource);
                bindTransactionFactoryType(JdbcTransactionFactory.class);

                addMapperClassesIn(MYBATIS_SQL_MAPPER_PACKAGE);
                addTypeHandlerClasses(MYBATIS_TYPE_HANDLER_PACKAGE);

                bindSessionManager();
            }

            private void bindSessionManager() {
                bind(SqlSessionManager.class).annotatedWith(country)
                        .toProvider(SqlSessionManagerProvider.class).in(SINGLETON);
                expose(SqlSessionManager.class).annotatedWith(country);
            }

            private void addMapperClassesIn(String packageName) {
                bind(getClassesIn(packageName));
            }

            private Set<Class<?>> getClassesIn(String packageName) {
                Test isObject = new ResolverUtil.IsA(Object.class);
                return new ResolverUtil<Object>().find(isObject, packageName)
                        .getClasses();
            }

            @SuppressWarnings({ "unchecked", "rawtypes" })
            private void bind(Collection<Class<?>> mappers) {
                for (Class<?> mapper : mappers) {
                    Key key = get(mapper, country);
                    bind(key).to(mapper);
                    addMapperClass(mapper);
                    expose(key);
                }
            }
        });
    }

    private String getEnvironmentIdFor(Country country) {
        return on(SPACE).join(MYBATIS_ENVIRONMENT_ID, country.name(),
                getCurrentEnvironment().name());
    }

}
