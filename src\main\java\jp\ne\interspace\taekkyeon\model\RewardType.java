/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding reward types.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum RewardType implements ValueEnum {

    CPC(0),
    CPA_FIXED(1),
    CPA_SALES(2);

    private final int value;
}
