/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.RewardType;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler.ValueEnumTypeHandler;

/**
 * MyBatis {@link TypeHandler} for {@link RewardType}.
 *
 * <AUTHOR>
 */
@MappedTypes(RewardType.class)
public class RewardTypeTypeHandler extends ValueEnumTypeHandler<RewardType> {

    public RewardTypeTypeHandler() {
        super(RewardType.class, RewardType.CPC);
    }
}
