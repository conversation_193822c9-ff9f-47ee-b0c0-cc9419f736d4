/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.s3;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.amazonaws.services.s3.model.CopyObjectResult;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.module.Environment;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link SimpleStorageServiceBucket}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SimpleStorageServiceBucketTest {

    @InjectMocks
    @Spy
    private SimpleStorageServiceBucket underTest;

    @Mock
    private SimpleStorageServiceClient s3Client;

    @Test
    public void testSetUpForTestShouldCallCreateBucketWhenCalled() {
        // given
        String bucketName = "bucketName";
        when(underTest.getBucketName()).thenReturn(bucketName);

        // when
        underTest.setUpForTest();

        // then
        verify(s3Client).createBucket(bucketName);
    }

    @Test
    public void testTearDownForTestShouldCallDeleteBucketWhenCalled() {
        // given
        String bucketName = "bucketName";
        when(underTest.getBucketName()).thenReturn(bucketName);

        // when
        underTest.tearDownForTest();

        // then
        verify(s3Client).deleteBucket(bucketName);
    }

    @Test
    public void testListObjectsShouldCallListObjectsWhenGivenPrefix() {
        // given
        List<S3ObjectSummary> expected = new ArrayList<>();
        String bucketName = "bucketName";
        String prefix = "imp";

        when(underTest.getBucketName()).thenReturn(bucketName);
        when(s3Client.listObjects(bucketName, prefix)).thenReturn(expected);

        // when
        List<S3ObjectSummary> actual = underTest.listObjects(prefix);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testListObjectsShouldCallListObjectsWhenNoPrefixIsGiven() {
        // given
        List<S3ObjectSummary> expected = new ArrayList<>();
        String bucketName = "bucketName";
        String logType = "imp";

        when(underTest.getBucketName()).thenReturn(bucketName);
        when(s3Client.listObjects(bucketName, logType)).thenReturn(expected);

        // when
        List<S3ObjectSummary> actual = underTest.listObjects();

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testListAllObjectsShouldGetAllObjectsListObjectsWhenCalled() {
        // given
        List<S3ObjectSummary> expected = new ArrayList<>();
        String bucketName = "bucketName";

        when(underTest.getBucketName()).thenReturn(bucketName);
        when(s3Client.listObjects(bucketName)).thenReturn(expected);

        // when
        List<S3ObjectSummary> actual = underTest.listAllObjects();

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testGetAllKeysShouldCallCorrectMethodWhenCalled() {
        // given
        List<String> expected = Arrays.asList("key1", "key2");
        String bucketName = "bucketName";
        String prefix = "prefix";
        when(underTest.getBucketName()).thenReturn(bucketName);
        when(s3Client.fetchAllKeys(bucketName, prefix)).thenReturn(expected);

        // when
        List<String> actual = underTest.getAllKeys(prefix);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test
    public void testGetObjectShouldCallGetObjectWhenCalled() {
        // given
        S3Object expected = new S3Object();
        String bucketName = "bucketName";
        String key = "key";

        when(underTest.getBucketName()).thenReturn(bucketName);
        when(s3Client.getObject(bucketName, key)).thenReturn(expected);

        // when
        S3Object actual = underTest.getObject(key);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testPutObjectShouldCallPutObjectWhenCalled() {
        // given
        PutObjectResult expected = new PutObjectResult();
        String bucketName = "bucketName";
        String key = "key";
        String content = "content";

        when(underTest.getBucketName()).thenReturn(bucketName);
        when(s3Client.putObject(bucketName, key, content)).thenReturn(expected);

        // when
        PutObjectResult actual = underTest.putObject(key, content);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testPutObjectShouldCallPutObjectWhenCalledWithFile() {
        // given
        PutObjectResult expected = new PutObjectResult();
        String bucketName = "bucketName";
        String key = "key";
        File file = mock(File.class);

        when(underTest.getBucketName()).thenReturn(bucketName);
        when(s3Client.putObject(bucketName, key, file)).thenReturn(expected);

        // when
        PutObjectResult actual = underTest.putObject(key, file);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testCopyObjectShouldCallCopyObjectWhenCalled() {
        // given
        CopyObjectResult expected = new CopyObjectResult();
        String bucketName = "bucketName";
        String sourceKey = "sourceKey";
        String destinationPath = "destinationPath";
        String destinationKey = "destinationKey";
        String destinationBucketName = "bucketName/destinationPath";

        when(underTest.getBucketName()).thenReturn(bucketName);
        when(s3Client.copyObject(bucketName, sourceKey, destinationBucketName,
                destinationKey)).thenReturn(expected);

        // when
        CopyObjectResult actual = underTest.copyObject(sourceKey, destinationPath,
                destinationKey);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testDeleteObjectShouldCallDeleteObjectWhenCalled() {
        // given
        String bucketName = "bucketName";
        String key = "key";
        when(underTest.getBucketName()).thenReturn(bucketName);

        // when
        underTest.deleteObject(key);

        // then
        verify(s3Client).deleteObject(bucketName, key);
    }

    @Test
    public void testGetBucketNameShouldReturnCorrectBucketNameWhenTestSeedIsNull() {
        // given
        String bucketName = "bucketName";
        when(underTest.getPartialBucketName()).thenReturn(bucketName);

        when(underTest.getTestSeedSystemProperty()).thenReturn(null);
        when(underTest.getCurrentEnvironment()).thenReturn(Environment.DEV);

        // when
        String actual = underTest.getBucketName();

        // then
        assertEquals(bucketName, actual);
    }

    @Test
    public void testGetBucketNameShouldReturnCorrectBucketNameWhenTestSeedIsEmptyString() {
        // given
        String bucketName = "bucketName";
        when(underTest.getPartialBucketName()).thenReturn(bucketName);

        when(underTest.getTestSeedSystemProperty()).thenReturn("");
        when(underTest.getCurrentEnvironment()).thenReturn(Environment.DEV);

        // when
        String actual = underTest.getBucketName();

        // then
        assertEquals(bucketName, actual);
    }

    @Test
    public void testDoesObjectExistShouldCallDoesObjectExistWhenCalled() {
        // given
        String bucketName = "bucketName";
        String sourceKey = "sourceKey";

        when(underTest.getBucketName()).thenReturn(bucketName);
        when(s3Client.doesObjectExist(bucketName, sourceKey)).thenReturn(true);

        // when
        boolean actual = underTest.doesObjectExist(sourceKey);

        // then
        assertTrue(actual);
    }

    @Test
    public void testUpdateBucketAsPublicShouldCallCorrectMethodWhenCalled() {
        // given
        String bucketName = "bucketName";
        when(underTest.getBucketName()).thenReturn(bucketName);

        // when
        underTest.updateBucketAsPublic();

        // then
        verify(s3Client).updateBucketAsPublic(bucketName);
    }
}
