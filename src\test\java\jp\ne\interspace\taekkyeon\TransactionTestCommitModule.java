/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon;

import com.google.inject.AbstractModule;
import com.google.inject.TypeLiteral;

import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.batch.processor.DummyRecordProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.DummyRecordReader;
import jp.ne.interspace.taekkyeon.module.MainRecordProcessorBinding;
import jp.ne.interspace.taekkyeon.module.MainRecordReaderBinding;
import jp.ne.interspace.taekkyeon.module.MainRecordWriterBinding;

/**
 * Module for transaction integration test.
 *
 * <AUTHOR> VAN NGUYEN
 */
public class TransactionTestCommitModule extends AbstractModule {

    @Override
    protected void configure() {
        bind(RecordReader.class).annotatedWith(MainRecordReaderBinding.class)
                .to(DummyRecordReader.class);
        bind(new TypeLiteral<RecordProcessor<? extends Record<?>, ? extends Record<?>>>() {
        }).annotatedWith(MainRecordProcessorBinding.class).to(DummyRecordProcessor.class);
        bind(RecordWriter.class).annotatedWith(MainRecordWriterBinding.class)
                .to(TransactionTestCommitWriter.class);
    }
}
