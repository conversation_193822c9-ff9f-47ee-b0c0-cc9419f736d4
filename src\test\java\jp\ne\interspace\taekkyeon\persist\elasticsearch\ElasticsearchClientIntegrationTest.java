/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.elasticsearch;

import java.util.List;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonElasticsearchJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonElasticsearchJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.model.elasticsearch.Document;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchBulkOperationIndex;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchBulkOperationResponse;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchDeleteByQueryOperationResponse;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchOperationResponse;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchOperationShard;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchSearchResponse;
import jp.ne.interspace.taekkyeon.module.ElasticsearchPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * Integration test for {@link ElasticsearchClient}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonElasticsearchJunitRunner.class)
@TaekkyeonModules({ TaekkyeonElasticsearchJunitModule.class,
        ElasticsearchPropertiesJunitModule.class })
public class ElasticsearchClientIntegrationTest {

    private static final String BULK_API_HEADER_FORMAT = "{ \"index\" : { \"_index\" : \"%s\", \"_type\" : \"products\", \"_id\" : \"%s\" } }";
    private static final String BULK_DELETE_BY_QUERY = "{\"query\": {\"match\": {\"campaignId\": \"[1, 2]\"} } }";
    private static final String BULK_UPDATE_BY_QUERY = "{\"script\":{\"source\":\"ctx._source.deleted_epoch_time=1633934568\"},\"query\":{\"bool\":{\"must\":[{\"terms\":{\"campaignId\":[1,3]}}]}}}";
    private static final String COUNT_BY_QUERY = "{\"query\":{ \"bool\": { \"must\": [ { \"exists\": { \"field\" : \"deleted_epoch_time\" } } ] } } }";

    @Inject
    private ElasticsearchClient underTest;

    @Test
    public void testIndexDocumentShouldReturnCorrectDataWhenCalled() throws Exception {
        // when
        ElasticsearchOperationResponse actual = underTest.indexDocument("index", "type",
                "0", "{\"name\":\"test\"}");

        // then
        assertEquals(new ElasticsearchOperationResponse("index", "type", "0", 1,
                "created", new ElasticsearchOperationShard(2, 1, 0), 0L, 1), actual);
    }

    @Test
    public void testFindDocumentByShouldReturnCorrectDataWhenCalled() throws Exception {
        // when
        ElasticsearchSearchResponse actual = underTest.findDocumentBy(
                "{\"query\":{\"bool\":{\"must\":"
                        + "[{\"match\":{\"name\":\"test-name\"}}]}}}");

        // then
        assertNotNull(actual);
        assertEquals(2L, actual.getCount());
        assertEquals(2, actual.getDocuments().size());
        assertFields(actual.getDocuments().get(0), "initial-test-data", "test", "0",
                0.4991763, "{\"name\":\"test-name1\",\"description\":\"test1\"}");
        assertFields(actual.getDocuments().get(1), "initial-test-data", "test", "1",
                0.4991763, "{\"name\":\"test-name2\",\"description\":\"test2\"}");
    }

    @Test
    public void testDeleteDocumentShouldReturnCorrectDataWhenCalled() throws Exception {
        // when
        ElasticsearchOperationResponse actual = underTest
                .deleteDocument("initial-test-data", "test", "2");

        // then
        assertEquals(
                new ElasticsearchOperationResponse("initial-test-data", "test", "2", 2,
                        "deleted", new ElasticsearchOperationShard(2, 1, 0), 3L, 1),
                actual);
    }

    @Test
    public void testIndexBulkDocumentsReturnCorrectDataWhenCalled() throws Exception {
        // given
        StringBuilder bulkApiBodies = new StringBuilder();
        bulkApiBodies.append(String.format(BULK_API_HEADER_FORMAT, "bulk_index", "0"));
        bulkApiBodies.append(System.lineSeparator());
        bulkApiBodies.append("{\"bulk-test0\":\"bulk-test0\"}");
        bulkApiBodies.append(System.lineSeparator());
        bulkApiBodies.append(String.format(BULK_API_HEADER_FORMAT, "bulk_index", "1"));
        bulkApiBodies.append(System.lineSeparator());
        bulkApiBodies.append("{\"bulk-test1\":\"bulk-test1\"}");
        bulkApiBodies.append(System.lineSeparator());

        // when
        ElasticsearchBulkOperationResponse actual = underTest
                .indexBulkDocuments(bulkApiBodies.toString());

        // then
        assertNotNull(actual);
        assertFalse(actual.isErrors());
        List<ElasticsearchBulkOperationIndex> actualItems = actual.getItems();
        assertEquals(2, actualItems.size());
        assertEquals(
                new ElasticsearchOperationResponse("bulk_index", "products", "0", 1,
                        "created", new ElasticsearchOperationShard(2, 1, 0), 0, 1),
                actualItems.get(0).getIndex());
        assertEquals(
                new ElasticsearchOperationResponse("bulk_index", "products", "1", 1,
                        "created", new ElasticsearchOperationShard(2, 1, 0), 1, 1),
                actualItems.get(1).getIndex());
    }

    @Test
    public void testDeleteShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        int expected = 4;
        int requestPerSecond = 10;

        // when
        ElasticsearchDeleteByQueryOperationResponse actual = underTest
                .delete("delete-test-data", BULK_DELETE_BY_QUERY, requestPerSecond);

        // then
        assertNotNull(actual);
        assertTrue(actual.getFailures().isEmpty());
        assertEquals(expected, actual.getDeleted());
        assertEquals(expected, actual.getTotal());
    }

    @Test
    public void testUpdateShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        int expected = 2;
        int requestPerSecond = 10;

        // when
        long actual = underTest.update("update-test-data", BULK_UPDATE_BY_QUERY,
                requestPerSecond);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCountShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        int expected = 2;

        // when
        long actual = underTest.count("count-test-data", COUNT_BY_QUERY);

        // then
        assertEquals(expected, actual);
    }

    private void assertFields(Document actual, String expectedIndex,
            String expectedType, String expectedId, double expectedScore,
            String expectedSource) {
        assertNotNull(actual);
        assertEquals(expectedIndex, actual.getIndex());
        assertEquals(expectedType, actual.getType());
        assertEquals(expectedId, actual.getId());
        assertEquals(expectedScore, actual.getScore().doubleValue(), 0);
        assertEquals(expectedSource, actual.getSource().toString());
    }
}
