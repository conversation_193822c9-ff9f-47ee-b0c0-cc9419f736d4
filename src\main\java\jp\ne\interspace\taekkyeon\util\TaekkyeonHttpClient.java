/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.util;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.Map.Entry;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import lombok.extern.slf4j.Slf4j;

import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.model.ResponseStatus;

import static java.lang.String.format;
import static java.nio.charset.StandardCharsets.UTF_8;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.FOUND;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.MOVED_PERMANENTLY;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.OK;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.TEMPORARY_REDIRECT;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.fromStatusCode;
import static org.apache.http.util.EntityUtils.consume;

/**
 * HTTP client connecting to the given endpoint.
 *
 * <AUTHOR> Shin
 */
@Slf4j
@Singleton
public class TaekkyeonHttpClient {

    private static final String SUCCESS_MESSAGE_TEMPLATE = "Successfully sent POST request "
            + "to [{}] with headers [{}] with request body [{}].";
    private static final String ERROR_MESSAGE_TEMPLATE = "A request has been sent to "
            + "[%s] but has failed - response status: [%s %s]";

    private final CloseableHttpClient httpClient;
    private final RequestConfig requestConfiguration;

    private final Gson lenientGson = new GsonBuilder().setLenient().create();

    /**
     * Creates a {@link TaekkyeonHttpClient} instance with the given parameters.
     *
     * @param httpClient
     *            {@link CloseableHttpClient } containing the settings for persistent
     *            client connections
     * @param requestConfiguration
     *            {@link RequestConfig} containing default timeout periods
     */
    public TaekkyeonHttpClient(CloseableHttpClient httpClient,
            RequestConfig requestConfiguration) {
        this.httpClient = httpClient;
        this.requestConfiguration = requestConfiguration;
    }

    /**
     * Returns a {@link String} representing the HTTP response entity corresponding to
     * the HTTP POST request made, or {@code null} if response contents found.
     *
     * @param url
     *          the API URI to make a HTTP POST request to
     * @param headers
     *          the API header
     * @param requestBody
     *          a POJO object representing the JSON to be sent
     * @return a {@link String} representing the HTTP response entity, or {@code null} if
     *         response contents found
     * @throws Exception
     *          when a problem occurs
     */
    public String post(String url, Map<String, String> headers, Object requestBody)
            throws Exception {
        HttpPost request = createPostRequestWith(url, headers, requestBody);
        CloseableHttpResponse response = getResponseOf(request);
        HttpEntity entity = response.getEntity();
        try {
            ResponseStatus responseStatus = getResponseStatusFrom(response);
            if (responseStatus == OK && entity != null) {
                String responseBody = getContentsFrom(entity);
                getLogger().info(SUCCESS_MESSAGE_TEMPLATE, url, headers, toJson(requestBody));
                return responseBody;
            }
            getLogger().error(generateErrorMessageWith(responseStatus, url));
        } finally {
            closeQuietly(response, entity);
        }
        return null;
    }

    /**
     * Send the get request and return redirect URL.
     *
     * @param url
     *            the given url
     * @param userAgent
     *            the given user agent
     * @return when response is 302 or 302 or 307, otherwise empty
     * @throws IOException
     *            when a problem occurs
     */
    public String redirect(String url, String userAgent) throws IOException {
        HttpGet request = createGetRequestWith(url, userAgent);
        try (CloseableHttpResponse response = getResponseOf(request);) {
            ResponseStatus responseStatus = getResponseStatusFrom(response);
            if (isRedirect(responseStatus)) {
                Header header = response.getFirstHeader("Location");
                return header == null ? EMPTY : header.getValue();
            }
            getLogger().warn(generateErrorMessageWith(responseStatus, url));
        }
        return EMPTY;
    }

    /**
     * Send the get request and return response status.
     *
     * @param url
     *            the given url
     * @return response status
     * @throws IOException
     *            when a problem occurs
     */
    public ResponseStatus getResponseStatus(String url) throws IOException {
        HttpGet request = new HttpGet(url);
        try (CloseableHttpResponse response = getResponseOf(request);) {
            return getResponseStatusFrom(response);
        }
    }

    /**
     * Send Get request.
     * @param url
     *          The given url
     * @param authorizationKey
     *          The given authorization key
     * @return response
     */
    public String getQuietly(String url, String authorizationKey) {
        try {
            return get(url, authorizationKey);
        } catch (Exception e) {
            getLogger().error("Error occurs while sending url: " + url, e);
        }
        return EMPTY;
    }

    @VisibleForTesting
    String get(String url, String authorizationKey) throws Exception {
        HttpGet request = new HttpGet(url);
        request.addHeader("Authorization", authorizationKey);
        request.setConfig(requestConfiguration);
        CloseableHttpResponse response = getResponseOf(request);
        HttpEntity entity = response.getEntity();
        try {
            ResponseStatus responseStatus = getResponseStatusFrom(response);
            if (responseStatus == OK && entity != null) {
                return getContentsFrom(entity);
            }
            getLogger().error(generateErrorMessageWith(responseStatus, url));
        } finally {
            closeQuietly(response, entity);
        }
        return EMPTY;
    }

    @VisibleForTesting
    HttpPost createPostRequestWith(String url, Map<String, String> headers,
            Object requestBody) throws UnsupportedEncodingException {
        HttpPost request = new HttpPost(url);
        if (headers != null && !headers.isEmpty()) {
            for (Entry<String, String> header : headers.entrySet()) {
                request.setHeader(header.getKey(), header.getValue());
            }
        }
        request.setConfig(requestConfiguration);
        StringEntity stringEntity = new StringEntity(toJson(requestBody), UTF_8);
        stringEntity.setContentType("application/json");
        request.setEntity(stringEntity);
        return request;
    }

    @VisibleForTesting
    HttpGet createGetRequestWith(String url, String userAgent) {
        HttpGet request = new HttpGet(url);
        request.addHeader("User-Agent", userAgent);
        request.setConfig(requestConfiguration);
        return request;
    }

    @VisibleForTesting
    CloseableHttpResponse getResponseOf(HttpPost request) throws IOException {
        return httpClient.execute(request);
    }

    @VisibleForTesting
    CloseableHttpResponse getResponseOf(HttpGet request) throws IOException {
        return httpClient.execute(request);
    }

    @VisibleForTesting
    ResponseStatus getResponseStatusFrom(HttpResponse response) {
        return fromStatusCode(response.getStatusLine().getStatusCode());
    }

    @VisibleForTesting
    String getContentsFrom(HttpEntity entity) throws IOException {
        return EntityUtils.toString(entity);
    }

    @VisibleForTesting
    void closeQuietly(CloseableHttpResponse response, HttpEntity entity) {
        try {
            consume(entity);
            response.close();
        } catch (IOException ex) {
            getLogger().error("Failed to close the response.", ex);
        }
    }

    @VisibleForTesting
    void closeQuietlyBy(CloseableHttpResponse response) {
        try {
            response.close();
        } catch (IOException ex) {
            getLogger().error("Failed to close the response.", ex);
        }
    }

    @VisibleForTesting
    boolean isRedirect(ResponseStatus responseStatus) {
        return responseStatus == MOVED_PERMANENTLY || responseStatus == FOUND
                || responseStatus == TEMPORARY_REDIRECT;
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    @VisibleForTesting
    String toJson(Object requestBody) {
        if (requestBody instanceof String) {
            return requestBody.toString();
        }
        return lenientGson.toJson(requestBody);
    }

    private String generateErrorMessageWith(ResponseStatus responseStatus, String url) {
        return format(ERROR_MESSAGE_TEMPLATE, url, responseStatus.getStatusCode(),
                responseStatus.getReason());
    }
}
