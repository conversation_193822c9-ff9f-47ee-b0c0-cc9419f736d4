/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.util.Locale;

import javax.inject.Singleton;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.model.CustomerSupport;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.CountryMapper;

/**
 * Service layer for handling country data.
 *
 * <AUTHOR>
 */
@Singleton
public class CountryService {

    @Inject
    private CountryMapper countryMapper;

    private LoadingCache<String, CustomerSupport> customerSupportCache = CacheBuilder.newBuilder()
            .maximumSize(10)
            .build(new CacheLoader<String, CustomerSupport>() {

                @Override
                public CustomerSupport load(String countryCode) {
                    return countryMapper.findCustomerSupportBy(countryCode);
                }
            });

    private LoadingCache<String, String> currencyCache = CacheBuilder.newBuilder()
            .maximumSize(10)
            .build(new CacheLoader<String, String>() {

                @Override
                public String load(String countryCode) {
                    return countryMapper.findCurrencyBy(countryCode);
                }
            });

    private LoadingCache<String, String> zoneIdCache = CacheBuilder.newBuilder()
            .maximumSize(10)
            .build(new CacheLoader<String, String>() {

                @Override
                public String load(String countryCode) {
                    return countryMapper.findZoneIdBy(countryCode);
                }
            });

    private LoadingCache<String, Locale> localeCache = CacheBuilder.newBuilder()
            .maximumSize(10).build(new CacheLoader<String, Locale>() {

                @Override
                public Locale load(String countryCode) {
                    return Locale.forLanguageTag(countryMapper.findLocaleBy(countryCode));
                }
            });

    private LoadingCache<String, String> countryNameCache = CacheBuilder.newBuilder()
            .maximumSize(10)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String countryCode) {
                    return countryMapper.findCountryNameBy(countryCode);
                }
            });

    private LoadingCache<String, Boolean> countryPendingForMerchantPaymentCache = CacheBuilder
            .newBuilder().maximumSize(10).build(new CacheLoader<String, Boolean>() {

                @Override
                public Boolean load(String countryCode) {
                    return countryMapper.isPendingForMerchantPayment(countryCode);
                }
            });

    private LoadingCache<String, BigDecimal> countryTaxMinCalculationCache = CacheBuilder
            .newBuilder().maximumSize(10).build(new CacheLoader<String, BigDecimal>() {

                @Override
                public BigDecimal load(String countryCode) {
                    return countryMapper.findTaxMinCalculationBy(countryCode);
                }
            });

    /**
     * Returns the country's {@link CustomerSupport} by given country code.
     *
     * @param countryCode
     *              the given country code
     * @return the country's {@link CustomerSupport} by given country code
     */
    public CustomerSupport findCustomerSupportBy(String countryCode) {
        try {
            return customerSupportCache.get(countryCode);
        } catch (Exception e) {
            return CustomerSupport.DEFAULT_CUSTOMER_SUPPORT;
        }
    }

    /**
     * Returns the country's currency by given country code.
     *
     * @param countryCode
     *              the given country code
     * @return the country's currency by given country code
     */
    public String findCurrencyBy(String countryCode) {
        return currencyCache.getUnchecked(countryCode);
    }

    /**
     * Returns the country's zone ID by given country code.
     *
     * @param countryCode
     *              the given country code
     * @return the country's zone ID by given country code
     */
    public String findZoneIdBy(String countryCode) {
        return zoneIdCache.getUnchecked(countryCode);
    }

    /**
     * Returns the country's locale by given country code.
     *
     * @param countryCode
     *              the given country code
     * @return the country's locale by given country code
     */
    public Locale findLocaleBy(String countryCode) {
        return localeCache.getUnchecked(countryCode);
    }

    /**
     * Returns the country name by {@code countryCode}.
     *
     * @param countryCode
     *            the given country code
     * @return the country name by {@code countryCode}
     */
    public String findCountryNameBy(String countryCode) {
        return countryNameCache.getUnchecked(countryCode);
    }

    /**
     * Returns the tax min calculation by given country code.
     *
     * @param countryCode
     *            the given country code
     * @return the tax min calculation by given country code
     */
    public BigDecimal findTaxMinCalculationBy(String countryCode) {
        return countryTaxMinCalculationCache.getUnchecked(countryCode);
    }

    /**
     * Returns country has pending for merchant payment or not by {@code countryCode}.
     *
     * @param countryCode
     *            the given country code
     * @return country has pending for merchant payment by {@code countryCode}
     */
    public boolean isPendingForMerchantPayment(String countryCode) {
        return countryPendingForMerchantPaymentCache.getUnchecked(countryCode);
    }
}
