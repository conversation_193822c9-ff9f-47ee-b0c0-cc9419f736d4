/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import com.google.cloud.translate.Detection;
import com.google.cloud.translate.Translate;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link GoogleTranslationService}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class GoogleTranslationServiceTest {

    @InjectMocks
    private GoogleTranslationService underTest;

    @Mock
    private Translate translate;

    @Test
    public void testDetectLauguageShouldReturnCorrectDataWhenCalled() {
        // given
        String text = "desk";
        String language = "en";
        Detection detection = mock(Detection.class);
        when(detection.getLanguage()).thenReturn(language);
        when(translate.detect(text)).thenReturn(detection);

        // when
        String actual = underTest.detectLauguage("desk");

        // then
        assertEquals(actual, language);
    }
}
