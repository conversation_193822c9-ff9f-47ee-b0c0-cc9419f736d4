/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding all the different duplication cut target.
 *
 * <AUTHOR> May<PERSON>
 */
@Getter @AllArgsConstructor
public enum DuplicationCutTarget implements ValueEnum {

    DAILY(0),
    PERMANENT(1);

    private final int value;
}
