/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

/**
 * Integration test for {@link MerchantAccountMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class MerchantAccountMapperTest {

    @Inject
    private MerchantAccountMapper underTest;

    @Test
    public void testFindCountryCodeByShouldReturnCorrectCountryCodeWhenGivenCampaignExists() {
        // given
        long campaignId = 1;
        String expected = "ID";

        // when
        String actual = underTest.findCountryCodeBy(campaignId);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testFindCountryCodeByShouldReturnNullWhenGivenCampaignIsNotExisting() {
        // given
        long campaignId = 50;

        // when
        String actual = underTest.findCountryCodeBy(campaignId);

        // then
        assertNull(actual);
    }
}
