/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.time.ZonedDateTime;

import javax.inject.Singleton;
import javax.net.ssl.HostnameVerifier;

import com.google.common.collect.ImmutableMap;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;

import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.conn.ssl.DefaultHostnameVerifier;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.util.PublicSuffixMatcherLoader;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder.HttpClientConfigCallback;
import org.elasticsearch.client.RestClientBuilder.RequestConfigCallback;

import jp.ne.interspace.taekkyeon.json.ElasticsearchResponseAdapter;
import jp.ne.interspace.taekkyeon.json.ProductFeedTypeAdapter;
import jp.ne.interspace.taekkyeon.json.ZonedDateTimeAdapter;
import jp.ne.interspace.taekkyeon.model.ProductFeedType;
import jp.ne.interspace.taekkyeon.model.elasticsearch.ElasticsearchSearchResponse;

import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static org.apache.http.HttpHeaders.CONTENT_TYPE;
import static org.apache.http.entity.ContentType.APPLICATION_JSON;
import static org.elasticsearch.client.RestClientBuilder.DEFAULT_MAX_CONN_PER_ROUTE;
import static org.elasticsearch.client.RestClientBuilder.DEFAULT_SOCKET_TIMEOUT_MILLIS;

/**
 * Taekkyeon module for accessing elasticsearch on AWS.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonElasticsearchModule extends AbstractModule {

    private static final int DEFAULT_CONNECT_TIMEOUT_MILLIS = 1500;
    private static final int DEFAULT_MAX_CONNECTION_TOTAL = 250;
    private static final HostnameVerifier DEFAULT_HOSTNAME_VERIFIER = new DefaultHostnameVerifier(
            PublicSuffixMatcherLoader.getDefault());
    private static final ImmutableMap<Environment, String> ELASTICSEARCH_HOSTS = ImmutableMap.of(
            DEV, "localhost",
            STAGING, "vpc-global-accesstrade-staging-zuvxooqqxn23uyjpxpp6u5ephm"
                    + ".ap-southeast-1.es.amazonaws.com",
            PRODUCTION, "vpc-global-accesstrade-prod-tqigcebmlugi7pysr4efbfl56i"
                    + ".ap-southeast-1.es.amazonaws.com");
    private static final ImmutableMap<Environment, Integer> ELASTICSEARCH_PORTS = ImmutableMap
            .of(DEV, 9200, STAGING, 443, PRODUCTION, 443);
    private static final ImmutableMap<Environment, HostnameVerifier> ELASTICSEARCH_HOSTNAME_VERIFIERS = ImmutableMap
            .of(DEV, NoopHostnameVerifier.INSTANCE,
                    STAGING, DEFAULT_HOSTNAME_VERIFIER,
                    PRODUCTION, DEFAULT_HOSTNAME_VERIFIER);
    private static final String ELASTICSEARCH_SOCKET_TIMEOUT_MILLIS_VM_ARGUMENT = "elasticsearchSocketTimeoutMillis";
    private static final String ELASTICSEARCH_CONNECT_TIMEOUT_MILLIS_VM_ARGUMENT = "elasticsearchConnectTimeoutMillis";

    @Override
    protected void configure() {
        // do nothing.
    }

    @Provides @Singleton
    private RestClient provideRestClient() {
        Environment environment = Environment.getCurrentEnvironment();
        return RestClient
                .builder(new HttpHost(ELASTICSEARCH_HOSTS.get(environment),
                        ELASTICSEARCH_PORTS.get(environment), "https"))
                .setDefaultHeaders(new Header[] {
                        new BasicHeader(CONTENT_TYPE, APPLICATION_JSON.getMimeType()) })
                .setRequestConfigCallback(new RequestConfigCallback() {
                    @Override
                    public Builder customizeRequestConfig(Builder requestConfigBuilder) {
                        return requestConfigBuilder
                                .setConnectTimeout(getElasticsearchConnectTimeout())
                                .setSocketTimeout(getElasticsearchSocketTimeout());
                    }
                })
                .setHttpClientConfigCallback(new HttpClientConfigCallback() {
                    @Override
                    public HttpAsyncClientBuilder customizeHttpClient(
                            HttpAsyncClientBuilder httpClientBuilder) {
                        return httpClientBuilder
                                .setSSLHostnameVerifier(
                                        ELASTICSEARCH_HOSTNAME_VERIFIERS.get(environment))
                                .setMaxConnPerRoute(DEFAULT_MAX_CONN_PER_ROUTE)
                                .setMaxConnTotal(DEFAULT_MAX_CONNECTION_TOTAL);
                    }
                }).build();
    }

    @Provides @Singleton
    private Gson provideGson() {
        return new GsonBuilder().setLenient()
                .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
                .registerTypeAdapter(ZonedDateTime.class, new ZonedDateTimeAdapter())
                .registerTypeAdapter(ElasticsearchSearchResponse.class,
                        new ElasticsearchResponseAdapter())
                .registerTypeAdapter(ProductFeedType.class, new ProductFeedTypeAdapter())
                .create();
    }

    private int getElasticsearchSocketTimeout() {
        try {
            return Integer.parseInt(
                    getProperty(ELASTICSEARCH_SOCKET_TIMEOUT_MILLIS_VM_ARGUMENT));
        } catch (Exception e) {
            return DEFAULT_SOCKET_TIMEOUT_MILLIS;
        }
    }

    private int getElasticsearchConnectTimeout() {
        try {
            return Integer.parseInt(
                    getProperty(ELASTICSEARCH_CONNECT_TIMEOUT_MILLIS_VM_ARGUMENT));
        } catch (Exception e) {
            return DEFAULT_CONNECT_TIMEOUT_MILLIS;
        }
    }
}
