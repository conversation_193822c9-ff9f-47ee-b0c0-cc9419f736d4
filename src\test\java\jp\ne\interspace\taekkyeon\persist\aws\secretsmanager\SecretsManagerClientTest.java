/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.secretsmanager;

import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.model.GetSecretValueRequest;
import com.amazonaws.services.secretsmanager.model.GetSecretValueResult;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link SecretsManagerClient}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SecretsManagerClientTest {

    @InjectMocks
    private SecretsManagerClient underTest;

    @Mock
    private AWSSecretsManager secretsManager;

    @Test(expected = IllegalArgumentException.class)
    public void testGetSecretValueShouldThrowIllegalArgumentExceptionWhenGivenNull() {
        // given
        String secretName = null;

        // when
        underTest.getSecretValue(secretName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetSecretValueShouldThrowIllegalArgumentExceptionWhenGivenEmpty() {
        // given
        String secretName = "";

        // when
        underTest.getSecretValue(secretName);
    }

    @Test
    public void testGetSecretValueShouldReturnCorrectDataWhenGivenCorrectSecretName() {
        // given
        ArgumentCaptor<GetSecretValueRequest> captor = ArgumentCaptor
                .forClass(GetSecretValueRequest.class);
        String secretName = "secretName";
        String expected = "secretValue";
        GetSecretValueResult getSecretValueResult = new GetSecretValueResult()
                .withSecretString(expected);
        when(secretsManager.getSecretValue(captor.capture()))
                .thenReturn(getSecretValueResult);

        // when
        String actual = underTest.getSecretValue(secretName);

        // then
        assertSame(expected, actual);
        GetSecretValueRequest expectedRequest = captor.getValue();
        assertNotNull(expectedRequest);
        assertSame(expectedRequest.getSecretId(), secretName);
    }
}
