/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model.elasticsearch;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding the result of operation.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @EqualsAndHashCode
public class ElasticsearchOperationShard {

    private final int total;
    private final int successful;
    private final int failed;
}
