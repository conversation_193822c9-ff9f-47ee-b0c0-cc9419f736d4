/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

import org.junit.Test;

import static java.time.ZoneOffset.UTC;
import static org.junit.Assert.assertEquals;

/**
 * Unit test for {@link DateUtils}.
 *
 * <AUTHOR>
 */
public class DateUtilsTest {

    private DateUtils dateUtils = new DateUtils();

    @Test
    public void testCreateDateFromShouldReturnCorrectDateWhenCalled() {
        // given
        ZonedDateTime dateTime = ZonedDateTime.of(1970, 01, 01, 0, 0, 0, 0, UTC);
        Date expected = new Date(0);

        // when
        Date actual = dateUtils.createDateFrom(dateTime);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testConvertByTimeZoneShouldReturnCorrectDateWhenCalled() {
        // given
        LocalDateTime dateTime = LocalDateTime.of(2017, 12, 6, 18, 3, 31);
        String zoneId = "Asia/Kuala_Lumpur";
        ZonedDateTime expected = ZonedDateTime.of(2017, 12, 6, 19, 3, 31, 0,
                ZoneId.of(zoneId));

        // when
        ZonedDateTime actual = dateUtils.convertByTimeZone(dateTime, zoneId);

        // then
        assertEquals(expected, actual);
    }
}
