/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import javax.inject.Singleton;

import com.google.common.base.Joiner;

import static com.google.common.base.Strings.padStart;
import static java.lang.Character.MAX_RADIX;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;

/**
 * Convenience class for creative operations.
 *
 * <AUTHOR>
 */
@Singleton
public class CreativeHelper {

    private static final int PAD_SIZE = 6;
    private static final char PAD_CHARACTER = '0';

    /**
     * Returns the rk by {@code creativeId} and {@code siteId}.
     *
     * @param creativeId
     *            the ID of given creative
     * @param siteId
     *            the ID of given site
     * @return the rk by {@code creativeId} and {@code siteId}
     */
    public String createRk(long creativeId, long siteId) {
        return Joiner.on(EMPTY).join(
                padStart(Long.toString(creativeId, MAX_RADIX), PAD_SIZE, PAD_CHARACTER),
                padStart(Long.toString(siteId, MAX_RADIX), PAD_SIZE, PAD_CHARACTER));
    }
}
