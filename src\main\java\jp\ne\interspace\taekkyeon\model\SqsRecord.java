/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import org.easybatch.core.record.GenericRecord;
import org.easybatch.core.record.Header;
import org.easybatch.core.record.Record;

/**
 * {@link Record} implementation for holding a raw message received from an SQS queue.
 *
 * <AUTHOR> Varga
 */
public class SqsRecord extends GenericRecord<SqsRecordPayload> {

    public SqsRecord(Header header, SqsRecordPayload payload) {
        super(header, payload);
    }
}
