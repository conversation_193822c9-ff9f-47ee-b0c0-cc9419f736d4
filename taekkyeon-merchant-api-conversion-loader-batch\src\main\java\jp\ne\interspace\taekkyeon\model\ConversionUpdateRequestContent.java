/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO holding the information of mass conversions status update content.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public class ConversionUpdateRequestContent {

    private final String type;
    private final String details;
    private final long staffId;
}
