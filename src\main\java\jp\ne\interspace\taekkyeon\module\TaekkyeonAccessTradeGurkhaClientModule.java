/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import javax.inject.Singleton;

import com.google.common.collect.ImmutableTable;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.name.Named;

import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Country.VIETNAM;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Taekkyeon module for accessing gurkha.
 *
 * <AUTHOR>
 */
public class TaekkyeonAccessTradeGurkhaClientModule extends AbstractModule {

    public static final String BIND_KEY_GURKHA_URL = "gurkha.url";

    private static final ImmutableTable<Country, Environment, String> GURKHA_URLS =
            new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "http://localhost:8338")
            .put(INDONESIA, STAGING, "https://gurkha-staging.accesstrade.co.id")
            .put(INDONESIA, PRODUCTION, "https://gurkha.accesstrade.co.id")
            .put(THAILAND, DEV, "http://localhost:8338")
            .put(THAILAND, STAGING, "https://gurkha-staging.accesstrade.in.th")
            .put(THAILAND, PRODUCTION, "https://gurkha.accesstrade.in.th")
            .put(VIETNAM, DEV, "http://localhost:8338")
            .put(VIETNAM, STAGING, "https://gurkha-staging.accesstrade.vn")
            .put(VIETNAM, PRODUCTION, "https://gurkha.accesstrade.vn")
            .build();

    @Override
    protected void configure() {
        // do nothing.
    }

    @Provides @Singleton @Named(BIND_KEY_GURKHA_URL)
    private String provideGurkhaUrl() {
        return GURKHA_URLS.get(getCurrentCountry(), getCurrentEnvironment());
    }
}
