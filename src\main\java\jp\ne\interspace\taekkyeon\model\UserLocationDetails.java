/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO holding the user's country and city.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @EqualsAndHashCode
public class UserLocationDetails {

    public static final UserLocationDetails DEFAULT_USER_LOCATION_DETAILS = new UserLocationDetails("unknown", "unknown");

    private final String country;
    private final String city;
}
