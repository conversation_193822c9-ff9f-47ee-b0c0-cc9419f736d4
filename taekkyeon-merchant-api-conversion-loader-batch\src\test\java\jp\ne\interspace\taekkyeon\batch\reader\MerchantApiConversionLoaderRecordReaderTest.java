/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.reader;

import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.easybatch.core.record.Header;
import org.easybatch.core.record.Record;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionsWithClickIdDetails;
import jp.ne.interspace.taekkyeon.service.MerchantApiConversionService;

import static com.google.common.collect.Lists.partition;
import static java.util.Collections.emptyIterator;
import static java.util.Collections.emptyList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link MerchantApiConversionLoaderRecordReader}.
 *
 * <AUTHOR> Shin
 */
@RunWith(MockitoJUnitRunner.class)
public class MerchantApiConversionLoaderRecordReaderTest {

    private static final Date DATE = Date.from(Instant.ofEpochSecond(1010101010));

    @InjectMocks @Spy
    private MerchantApiConversionLoaderRecordReader underTest;

    @Mock
    private MerchantApiConversionService apiService;

    @Test
    public void testOpenShouldSetCorrectIteratorWhenCalled() throws Exception {
        // given
        List<ConversionRegistrationDetails> details1 = Arrays
                .asList(mock(ConversionRegistrationDetails.class));
        List<ConversionRegistrationDetails> details2 = Arrays
                .asList(mock(ConversionRegistrationDetails.class));
        ConversionsWithClickIdDetails expectedDetails = mock(
                ConversionsWithClickIdDetails.class);
        List<ConversionsWithClickIdDetails> expectedDetailsIterator = Arrays.asList(
                expectedDetails);
        List<ConversionRegistrationDetails> details = Stream.of(details1, details2)
                .flatMap(x -> x.stream()).collect(Collectors.toList());
        doReturn(expectedDetailsIterator).when(underTest)
                .getConversionRegistrationDetails(details);
        when(apiService.getConversionData()).thenReturn(details);

        // when
        underTest.open();

        // then
        Iterator<ConversionsWithClickIdDetails> actualIterator = underTest.getIterator();
        ConversionsWithClickIdDetails actualDetails = actualIterator.next();
        assertSame(expectedDetails, actualDetails);
        assertFalse(actualIterator.hasNext());
    }

    @Test
    public void testOpenShouldNotSetIteratorWhenDetailsEmpty() throws Exception {
        // given
        when(apiService.getConversionData()).thenReturn(emptyList());

        // when
        underTest.open();

        // then
        verify(underTest, never()).getConversionRegistrationDetails(anyList());
    }

    @Test
    public void testReadRecordShouldReturnCorrectDataWhenIteratorHasData()
            throws Exception {
        // given
        ConversionsWithClickIdDetails details = mock(ConversionsWithClickIdDetails.class);
        doReturn(Arrays.asList(details).iterator()).when(underTest).getIterator();
        doReturn(DATE).when(underTest).getCurrentDate();

        // when
        Record<ConversionsWithClickIdDetails> actual = underTest.readRecord();

        // then
        assertNotNull(actual);
        assertEquals(new Header(1L, null, DATE).toString(),
                actual.getHeader().toString());
        assertSame(details, actual.getPayload());
    }

    @Test
    public void testReadRecordShouldReturnNullWhenIteratorHasNotData() throws Exception {
        // given
        doReturn(emptyIterator()).when(underTest).getIterator();

        // when
        Record<ConversionsWithClickIdDetails> actual = underTest.readRecord();

        // then
        assertNull(actual);
    }

    @Test
    public void testGetConversionRegistrationDetailsShouldReturnCorrectDataWhenDetailsSizeIsGreaterThanPartitionConversionCount() throws Exception {
        // given
        int partitionConversionCount = 2;
        when(underTest.getPartitionConversionCount())
                .thenReturn(partitionConversionCount);

        ConversionRegistrationDetails conversionRegistrationDetails1 =
                mock(ConversionRegistrationDetails.class);
        ConversionRegistrationDetails conversionRegistrationDetails2 =
                mock(ConversionRegistrationDetails.class);
        ConversionRegistrationDetails conversionRegistrationDetails3 =
                mock(ConversionRegistrationDetails.class);
        List<ConversionRegistrationDetails> details = Arrays.asList(
                conversionRegistrationDetails1, conversionRegistrationDetails2,
                conversionRegistrationDetails3);

        ConversionsWithClickIdDetails conversionsWithClickIdDetails1 =
                mock(ConversionsWithClickIdDetails.class);
        ConversionsWithClickIdDetails conversionsWithClickIdDetails2 =
                mock(ConversionsWithClickIdDetails.class);
        ConversionsWithClickIdDetails conversionsWithClickIdDetails3 =
                mock(ConversionsWithClickIdDetails.class);
        List<List<ConversionRegistrationDetails>> partitionConversionRegistrations =
                partition(details, partitionConversionCount);
        when(apiService.createConversionsWithClickIdDetailsBy(
                partitionConversionRegistrations.get(0))).thenReturn(
                Arrays.asList(conversionsWithClickIdDetails1, conversionsWithClickIdDetails2));

        when(apiService.createConversionsWithClickIdDetailsBy(
                partitionConversionRegistrations.get(1))).thenReturn(
                Arrays.asList(conversionsWithClickIdDetails3));

        List<ConversionsWithClickIdDetails> expectedDetails = Arrays.asList(
                conversionsWithClickIdDetails1, conversionsWithClickIdDetails2,
                conversionsWithClickIdDetails3);

        // when
        List<ConversionsWithClickIdDetails> actual =
                underTest.getConversionRegistrationDetails(details);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        assertEquals(expectedDetails, actual);

        verify(apiService).createConversionsWithClickIdDetailsBy(
                partitionConversionRegistrations.get(0));
        verify(apiService).createConversionsWithClickIdDetailsBy(
                partitionConversionRegistrations.get(1));
    }

    @Test
    public void testGetConversionRegistrationDetailsShouldReturnCorrectDataWhenConversionsWithClickIdDetailsSizeIsLessThanPartitionConversionCount() throws Exception {
        // given
        int partitionConversionCount = 5;
        when(underTest.getPartitionConversionCount())
                .thenReturn(partitionConversionCount);

        ConversionRegistrationDetails conversionRegistrationDetails1 =
                mock(ConversionRegistrationDetails.class);
        ConversionRegistrationDetails conversionRegistrationDetails2 =
                mock(ConversionRegistrationDetails.class);
        ConversionRegistrationDetails conversionRegistrationDetails3 =
                mock(ConversionRegistrationDetails.class);
        List<ConversionRegistrationDetails> details = Arrays.asList(
                conversionRegistrationDetails1, conversionRegistrationDetails2,
                conversionRegistrationDetails3);

        ConversionsWithClickIdDetails conversionsWithClickIdDetails1 =
                mock(ConversionsWithClickIdDetails.class);
        ConversionsWithClickIdDetails conversionsWithClickIdDetails2 =
                mock(ConversionsWithClickIdDetails.class);

        List<ConversionsWithClickIdDetails> expectedDetails = Arrays.asList(
                conversionsWithClickIdDetails1, conversionsWithClickIdDetails2);
        when(apiService.createConversionsWithClickIdDetailsBy(details))
                .thenReturn(expectedDetails);

        // when
        List<ConversionsWithClickIdDetails> actual =
                underTest.getConversionRegistrationDetails(details);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertEquals(expectedDetails, actual);
        verify(apiService).createConversionsWithClickIdDetailsBy(details);
    }

    @Test
    public void testGetConversionRegistrationDetailsShouldReturnEmptyWhenApiServiceReturnsEmpty()
            throws Exception {
        // given
        int partitionConversionCount = 3;
        when(underTest.getPartitionConversionCount())
                .thenReturn(partitionConversionCount);

        ConversionRegistrationDetails conversionRegistrationDetails1 =
                mock(ConversionRegistrationDetails.class);
        List<ConversionRegistrationDetails> details = Arrays.asList(
                conversionRegistrationDetails1);

        when(apiService.createConversionsWithClickIdDetailsBy(details))
                .thenReturn(emptyList());

        // when
        List<ConversionsWithClickIdDetails> actual = underTest
                .getConversionRegistrationDetails(details);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
    }
}
