/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.Reader;

import javax.inject.Singleton;
import javax.sql.DataSource;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.io.CharSource;
import com.google.inject.Inject;

import lombok.Getter;

import org.apache.ibatis.jdbc.ScriptRunner;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.module.OracleResolver;

import static lombok.AccessLevel.PACKAGE;

/**
 * Service layer for database script.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class DatabaseScriptService {

    @VisibleForTesting @Getter(PACKAGE)
    private final ScriptRunner runner;

    /**
     * Guice-injection compatible constructor that creates a {@ScriptRunner} instance
     * with the auto-commit off.
     *
     * @param dataSource a {@link DataSource} instance injected by MyBatisGuice
     * @throws TaekkyeonException when failed to create a {@link ScriptRunner}
     */
    @Inject
    public DatabaseScriptService(@OracleResolver DataSource dataSource) throws Exception {
        runner = new ScriptRunner(dataSource.getConnection());
        runner.setAutoCommit(false);
    }

    /**
     * Executes the given {@code String} script.
     *
     * @param script database SQL script
     * @param isMaterialView if the script is material view SQL or not
     * @throws Exception when failed to execute the script
     */
    public void runScript(String script, boolean isMaterialView) throws Exception {
        runScript(toReaderFrom(script), isMaterialView);
    }

    /**
     * Executes the script read from the given {@code Reader}.
     *
     * @param script {@link Reader} object containing the database SQL script
     * @param isMaterialView if the script is material view SQL or not
     * @throws Exception when failed to execute the script
     */
    public void runScript(Reader script, boolean isMaterialView) throws Exception {
        if (isMaterialView) {
            getRunner().setDelimiter("/");
        }
        getRunner().runScript(script);
    }

    /**
     * Closes the connection of {@link ScriptRunner}.
     */
    public void closeConnection() {
        getRunner().closeConnection();
    }

    @VisibleForTesting
    Reader toReaderFrom(String script) throws Exception {
        return CharSource.wrap(script).openStream();
    }
}
