/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.time.LocalDateTime;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.Merchant;
import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryDetailTest;
import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryInsertRequest;

import static jp.ne.interspace.taekkyeon.model.Merchant.SHOPEE;
import static org.junit.Assert.assertEquals;

/**
 * Integration test for {@link MerchantIntegrationHistoryMapper}.
 *
 * <AUTHOR> Van
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class MerchantIntegrationHistoryMapperTest {

    @Inject
    private MerchantIntegrationHistoryMapper underTest;

    @Inject
    private TestMerchantIntegrationHistoryMapper testMerchantIntegrationHistoryMapper;

    private static final Merchant MERCHANT = SHOPEE;

    @Test
    public void testFindKeyExistingByShouldReturnOneWhenFoundData() {
        // given
        String key = "testKey";
        Integer expected = 1;

        // when
        Integer actual = underTest.findKeyExistingBy(key);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testFindKeyExistingByShouldReturnZeroWhenNotFoundData() {
        // given
        String key = "testKeyy";
        Integer expected = 0;

        // when
        Integer actual = underTest.findKeyExistingBy(key);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testInsertShouldAddCorrectDataWhenCalled() {
        // given
        String key = "testKey10";
        Long campaignId = 100L;
        LocalDateTime conversionOccursDate = LocalDateTime.of(2023,12,12,0,0,0);
        String data = "testData";
        MerchantIntegrationHistoryInsertRequest request
                = new MerchantIntegrationHistoryInsertRequest(key, campaignId,
                conversionOccursDate, MERCHANT.name(), data);

        // when
        underTest.insert(request);

        // then
        MerchantIntegrationHistoryDetailTest actualMerchantIntegrationHistory
                = testMerchantIntegrationHistoryMapper
                .findMerchantIntegrationHistory(key);
        assertEquals(key, actualMerchantIntegrationHistory.getKey());
        assertEquals(campaignId, actualMerchantIntegrationHistory.getCampaignId());
        assertEquals(conversionOccursDate,
                actualMerchantIntegrationHistory.getConversionOccursDate());
        assertEquals(data, actualMerchantIntegrationHistory.getData());
        assertEquals(MERCHANT.name(), actualMerchantIntegrationHistory.getMerchant());
    }
}
