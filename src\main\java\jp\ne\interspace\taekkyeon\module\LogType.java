/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.util.Objects;
import java.util.Optional;

import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import static java.lang.String.join;
import static java.util.Arrays.stream;

/**
 * Log Type settings for Taekkyeon runtime.
 *
 * <AUTHOR> OBS DEV Team
 */
@RequiredArgsConstructor
public enum LogType {

    /**
     * Impression, which should be specified as a VM argument <i>-DlogType=imp</i>.
     */
    IMPRESSION("imp"),

    /**
     * Click, which should be specified as a VM argument <i>-DlogType=cl</i>.
     */
    CLICK("cl"),

    /**
     * Conversion, which should be specified as a VM argument <i>-DlogType=cv</i>.
     */
    CONVERSION("cv");

    public static final String VM_ARG_NAME_FOR_LOG_TYPE = "logType";

    @NonNull @Getter private String code;

    /**
     * Get the current {@link LogType} setting corresponding to the given VM argument
     * {@code -DlogType}.
     *
     * @return {@link LogType} setting
     */
    public static LogType getCurrentLogType() {
        return Optional.of(VM_ARG_NAME_FOR_LOG_TYPE)
                .map(System::getProperty)
                .filter(Objects::nonNull)
                .map(LogType::match)
                .get();
    }

    private static LogType match(final String property) {
        return stream(values())
                .filter(env -> env.getCode().equalsIgnoreCase(property))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(
                        join(property, "No enum constant [", "] found")));
    }
}
