/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding shopee item.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @ToString
public class ShopeeItem {

    private final String itemId;
    private final String shopId;
    private final String itemName;
    private final int qty;
    private final BigDecimal itemPrice;
    private final BigDecimal actualAmount;
    private final BigDecimal itemCommission;
    private final BigDecimal grossBrandCommission;
    private final BigDecimal itemSellerCommission;
    private final String modelId;
    private final String globalCategoryLv1Name;
    private final String globalCategoryLv2Name;
    private final String globalCategoryLv3Name;
    private final String attributionType;
}
