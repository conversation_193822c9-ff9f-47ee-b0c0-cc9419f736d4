/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.sqs;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.CreateQueueResult;
import com.amazonaws.services.sqs.model.DeleteMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.DeleteQueueResult;
import com.amazonaws.services.sqs.model.GetQueueUrlResult;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import com.amazonaws.services.sqs.model.SendMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.SendMessageBatchResult;
import com.amazonaws.services.sqs.model.SendMessageResult;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link SimpleQueueServiceClient}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class SimpleQueueServiceClientTest {

    private static final String QUEUE_URL = "queueUrl";
    private static final String QUEUE_NAME = "queueName";
    private static final String NOT_FOUND_QUEUE_URL_MESSAGE = "Not found queue url.";
    private static final List<DeleteMessageBatchRequestEntry> MESSAGES_TO_DELETE = Arrays
            .asList(mock(DeleteMessageBatchRequestEntry.class));

    @InjectMocks
    private SimpleQueueServiceClient underTest;

    @Mock
    private AmazonSQS sqs;

    @Test
    public void testReceiveMessageShouldReturnCorrectResultWhenRequestIsNotNull() {
        // given
        ReceiveMessageRequest request = new ReceiveMessageRequest();
        ReceiveMessageResult expectedResult = new ReceiveMessageResult();
        when(sqs.receiveMessage(request)).thenReturn(expectedResult);

        // when
        ReceiveMessageResult actual = underTest.receiveMessage(request);

        // then
        assertSame(expectedResult, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testReceiveMessageShouldThrowIllegalArgumentExceptionWhenGivenRequestIsNull() {
        // when
        underTest.receiveMessage(null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMessageBatchShouldThrowIllegalArgumentExceptionWhenGivenQueueUrlIsNull() {
        // given
        List<SendMessageBatchRequestEntry> entries =
                Arrays.asList(new SendMessageBatchRequestEntry());

        // when
        underTest.sendMessageBatch(null, entries);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMessageBatchShouldThrowIllegalArgumentExceptionWhenGivenEntriesIsNull() {
        // when
        underTest.sendMessageBatch(QUEUE_URL, null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMessageBatchShouldThrowIllegalArgumentExceptionWhenGivenEntriesIsEmpty() {
        // given
        List<SendMessageBatchRequestEntry> entries = new LinkedList<>();

        // when
        underTest.sendMessageBatch(QUEUE_URL, entries);
    }

    @Test
    public void testSendMessageBatchShouldReturnSendMessageBatchResultWhenValidParameters() {
        // given
        List<SendMessageBatchRequestEntry> entries = Arrays
                .asList(new SendMessageBatchRequestEntry());
        SendMessageBatchResult expected = new SendMessageBatchResult();

        when(sqs.sendMessageBatch(QUEUE_URL, entries))
                .thenReturn(expected);

        // when
        SendMessageBatchResult actual = underTest.sendMessageBatch(QUEUE_URL,
                entries);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMessageShouldThrowIllegalArgumentExceptionWhenGivenMessageIsNull() {
        // when
        underTest.sendMessage(QUEUE_URL, null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMessageShouldThrowIllegalArgumentExceptionWhenGivenMessageIsEmpty() {
        // when
        underTest.sendMessage(null, "");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSendMessageShouldThrowIllegalArgumentExceptionWhenGivenQueueUrlIsNull() {
        // when
        underTest.sendMessage(null, "message");
    }

    @Test
    public void testSendMessageShouldReturnSendMessageResultWhenMessageAndQueueUrlAreNotNull() {
        // given
        String message = "message";

        SendMessageResult result = mock(SendMessageResult.class);
        when(sqs.sendMessage(QUEUE_URL, message)).thenReturn(result);

        // when
        SendMessageResult actual = underTest.sendMessage(QUEUE_URL, message);

        // then
        assertSame(result, actual);
    }

    @Test
    public void testDeleteMessageBatchShouldDeleteMessagesFromQueueWhenParametersAreValid() {
        // when
        underTest.deleteMessageBatch(QUEUE_URL, MESSAGES_TO_DELETE);

        // then
        verify(sqs).deleteMessageBatch(QUEUE_URL, MESSAGES_TO_DELETE);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteMessageBatchShouldThrowExceptionWhenQueueUrlIsNull() {
        // when
        underTest.deleteMessageBatch(null, MESSAGES_TO_DELETE);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteMessageBatchShouldThrowExceptionWhenMessagesToDeleteIsNull() {
        // when
        underTest.deleteMessageBatch(QUEUE_URL, null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteMessageBatchShouldThrowExceptionWhenMessagesToDeleteIsEmpty() {
        // when
        underTest.deleteMessageBatch(QUEUE_URL, new LinkedList<>());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetQueueUrlShouldThrowIllegalArgumentExceptionWhenGivenParameterIsNull() {
        // when
        underTest.getQueueUrl(null);
    }

    @Test
    public void testGetQueueUrlShouldReturnQueueUrlWhenGivenValidParameter() {
        // given
        GetQueueUrlResult expected = new GetQueueUrlResult().withQueueUrl(QUEUE_URL);

        when(sqs.getQueueUrl(QUEUE_NAME)).thenReturn(expected);

        // when
        String actual = underTest.getQueueUrl(QUEUE_NAME);

        // then
        assertEquals(QUEUE_URL, actual);
    }

    @Test
    public void testGetQueueUrlShouldThrowExceptionWhenGotQueueUrlResultIsNull() {
        // given
        when(sqs.getQueueUrl(QUEUE_NAME)).thenReturn(null);

        // when
        try {
            underTest.getQueueUrl(QUEUE_NAME);

            // then
            fail();
        } catch (TaekkyeonException ex) {
            assertEquals(NOT_FOUND_QUEUE_URL_MESSAGE, ex.getMessage());
        }
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCreateQueueShouldThrowIllegalArgumentExceptionWhenGivenNull() {
        // when
        underTest.createQueue(null);
    }

    @Test
    public void testCreateQueueShouldCallCreateQueueWhenCalled() {
        // given
        String queueName = "queueName";
        CreateQueueResult expected = mock(CreateQueueResult.class);
        when(sqs.createQueue(queueName)).thenReturn(expected);

        // when
        CreateQueueResult actual = underTest.createQueue(queueName);

        // then
        verify(sqs).createQueue(queueName);
        assertEquals(expected, actual);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDeleteQueueShouldThrowIllegalArgumentExceptionWhenGivenNull() {
        // when
        underTest.deleteQueue(null);
    }

    @Test
    public void testDeleteQueueShouldCallDeleteQueueWhenCalled() {
        // given
        String queueUrl = "queueUrl";
        DeleteQueueResult expected = mock(DeleteQueueResult.class);
        when(sqs.deleteQueue(queueUrl)).thenReturn(expected);

        // when
        DeleteQueueResult actual = underTest.deleteQueue(queueUrl);

        // then
        verify(sqs).deleteQueue(queueUrl);
        assertEquals(expected, actual);
    }
}
