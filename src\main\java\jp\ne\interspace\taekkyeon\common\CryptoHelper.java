/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.inject.Singleton;

import com.google.common.base.Strings;
import com.google.inject.Inject;

/**
 * Convenience class for string encrypt and decrypt.
 *
 * <AUTHOR>
 */
@Singleton
public class CryptoHelper {

    private static final String AES_CIPHER_KEY = "ZWxfc2VjcmV0aXRvX21pbw==";
    private static final String AES = "AES";

    @Inject
    private StringHelper stringHelper;

    /**
     * Returns the encrypted {@code Base64} string by the given message,
     * If given message is null or empty, returns given message.
     *
     * @param message
     *          the string to encrypted {@code Base64}
     * @return the encrypted {@code Base64} string by the given message
     *          If given message is null or empty, returns given message.
     * @throws Exception when a problem occurs
     */
    public String base64Encrypt(String message) throws Exception {
        if (!Strings.isNullOrEmpty(message)) {
            return Base64.getEncoder().encodeToString(stringHelper.getBytesFrom(message));
        }
        return message;
    }

    /**
     * Returns the decrypted {@code Base64} string by the given encrypted message,
     * If given message is null or empty, returns given message.
     *
     * @param message
     *          the encrypted string by {@code Base64}
     * @return the decrypted {@code Base64} string by the given message
     *          If given message is null or empty, returns given message
     */
    public String base64Decrypt(String message) {
        if (!Strings.isNullOrEmpty(message)) {
            return new String(Base64.getDecoder().decode(message));
        }
        return message;
    }

    /**
     * Returns the encrypted {@code AES256} string by the given message
     * If given message is null or empty, returns given message.
     *
     * @param message
     *          the string to encrypted {@code AES256}
     * @return the encrypted {@code AES256} string by the given message
     *          If given message is null or empty, returns given message
     * @throws Exception when a problem occurs
     */
    public String aes256Encrypt(String message) throws Exception {
        if (!Strings.isNullOrEmpty(message)) {
            Cipher cipher = getCipher(Cipher.ENCRYPT_MODE);
            return Base64.getEncoder()
                    .encodeToString(cipher.doFinal(stringHelper.getBytesFrom(message)));
        }
        return message;
    }

    /**
     * Returns the decrypted {@code AES256} string by the given encrypted message
     * If given message is null or empty, returns given message.
     *
     * @param message
     *          the encrypted string by {@code AES256}
     * @return the decrypted {@code AES256} string by the given encrypted message
     *          If given message is null or empty, returns given message
     * @throws Exception when a problem occurs
     */
    public String aes256Decrypt(String message) throws Exception {
        if (!Strings.isNullOrEmpty(message)) {
            Cipher cipher = getCipher(Cipher.DECRYPT_MODE);
            return new String(cipher.doFinal(Base64.getDecoder().decode(message)));
        }
        return message;
    }

    private Cipher getCipher(int cipherMode) throws Exception {
        Cipher cipher = Cipher.getInstance(AES);
        cipher.init(cipherMode, getAes256SecretKeySpec());
        return cipher;
    }

    private String getAes256CipherKey() {
        return base64Decrypt(AES_CIPHER_KEY);
    }

    private SecretKeySpec getAes256SecretKeySpec() throws Exception {
        return new SecretKeySpec(
                stringHelper.getBytesFrom(getAes256CipherKey()), AES);
    }
}
