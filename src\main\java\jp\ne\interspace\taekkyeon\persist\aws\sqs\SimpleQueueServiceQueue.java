/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.sqs;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.amazonaws.services.sqs.model.BatchResultErrorEntry;
import com.amazonaws.services.sqs.model.DeleteMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import com.amazonaws.services.sqs.model.SendMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.SendMessageBatchResult;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.google.inject.Inject;

import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.model.Pair;
import jp.ne.interspace.taekkyeon.service.SlackService;

import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule.SYSTEM_PROPERTY_NAME_FOR_TEST_SEED;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Data access layer representing an SQS queue.
 *
 * <AUTHOR> Shin
 */
@Slf4j
public abstract class SimpleQueueServiceQueue {

    public static final int SQS_MESSAGE_BATCH_MAX_SIZE = 10;

    private static final int RETRY_INTERVAL_IN_MILLIS = 1000;
    private static final int MAXIMUM_LIMIT_OF_FAILURES = 3;
    private static final String RETRY_FORMAT = "Retry ({}) times to send the SQS message {}";

    @Inject
    private SimpleQueueServiceClient sqsClient;

    @Inject(optional = true)
    private SlackService slackService;

    /**
     * Sets up a queue for test uses with the queue name suffixed with a seed string.
     */
    public void setUpForTest() {
        if (isNotTestContext()) {
            throw new UnsupportedOperationException("Creating a queue is not supported");
        }
        sqsClient.createQueue(getQueueName());
    }

    /**
     * Deletes the queue for test uses corresponding by the given queue url,
     * reset test seed for using different queue.
     */
    public void tearDownForTest() {
        tearDownForTestWithoutTestSeedReset();
        System.setProperty(SYSTEM_PROPERTY_NAME_FOR_TEST_SEED, generateSeed(32));
    }

    /**
     * Deletes the queue for test uses corresponding by the given queue url.
     */
    public void tearDownForTestWithoutTestSeedReset() {
        if (isNotTestContext()) {
            throw new UnsupportedOperationException("Deleting a queue is not supported");
        }
        sqsClient.deleteQueue(getQueueUrl());
    }

    /**
     * Receive message based on the given {@link ReceiveMessageRequest}.
     *
     * @param request
     *            {@link ReceiveMessageRequest} specifying the request attributes
     * @return {@link ReceiveMessageResult} containing the received messages
     */
    public ReceiveMessageResult receiveMessage(ReceiveMessageRequest request) {
        return sqsClient.receiveMessage(request);
    }

    /**
     * Reattempts will be made with the interval of 2 seconds.
     */
    public void waitUntilAvailable() {
        RateLimiter interval = RateLimiter.create(2);
        interval.acquire();
    }

    /**
     * Returns the url of the Amazon SQS queue by the given {@code queueName}.
     *
     * @return the url of the Amazon SQS queue by the given {@code queueName}
     */
    public String getQueueUrl() {
        return sqsClient.getQueueUrl(getQueueName());
    }

    /**
     * Sends messages to the specified queue.
     *
     * @param entries
     *            {@link SendMessageBatchRequestEntry} specifying the messages to send
     * @return a {@link SendMessageBatchResult} for each batch where sending to SQS failed
     *         more than the allowed number of times, or an empty {@link List} when all
     *         messages were sent to SQS successfully
     * @throws InterruptedException
     *             if resending any failed messages is interrupted
     */
    public List<SendMessageBatchResult> send(List<SendMessageBatchRequestEntry> entries)
            throws InterruptedException {
        return send(entries, null);
    }

    /**
     * Sends messages to the specified queue.
     *
     * @param entries
     *            {@link SendMessageBatchRequestEntry} specifying the messages to send
     * @param logFormat
     *            left : successful message, 1 parameter for showing message.
     *              log4j format<br />
     *              ex) Send SQS message of {} successfully<br />
     *            right : failure message, 2 parameter for showing country
     *              code and message, notify to slack<br />
     *              string format<br />
     *              ex) Failed to send [%s] CV log to SQS %s
     * @return a {@link SendMessageBatchResult} for each batch where sending to SQS failed
     *         more than the allowed number of times, or an empty {@link List} when all
     *         messages were sent to SQS successfully
     * @throws InterruptedException
     *             if resending any failed messages is interrupted
     */
    public List<SendMessageBatchResult> send(List<SendMessageBatchRequestEntry> entries,
            Pair<String, String> logFormat) throws InterruptedException {
        String queueUrl = getQueueUrl();
        List<SendMessageBatchResult> results = sendBatches(queueUrl, entries, logFormat);
        return resendFailedMessages(queueUrl, results, logFormat);
    }

    /**
     * Sends message to the specified queue.
     *
     * @param message
     *         the message to send
     */
    public void send(String message) {
        sqsClient.sendMessage(getQueueUrl(), message);
    }

    /**
     * Deletes messages from the specified queue.
     *
     * @param receiptHandles
     *            the unique IDs of the current receival of the messages to be deleted
     */
    public void delete(List<String> receiptHandles) {
        if (!receiptHandles.isEmpty()) {
            List<DeleteMessageBatchRequestEntry> deleteRequests = new LinkedList<>();
            int requestId = 0;
            for (String receiptHandle : receiptHandles) {
                deleteRequests.add(new DeleteMessageBatchRequestEntry(
                        String.valueOf(requestId), receiptHandle));
                requestId++;
            }

            String queueUrl = getQueueUrl();
            List<List<DeleteMessageBatchRequestEntry>> partitionedRequests = Lists
                    .partition(deleteRequests, SQS_MESSAGE_BATCH_MAX_SIZE);

            for (List<DeleteMessageBatchRequestEntry> partition : partitionedRequests) {
                sqsClient.deleteMessageBatch(queueUrl, partition);
            }
        }
    }

    /**
     * Returns the core part of the name of the SQS queue.
     *
     * @return the core part of the name of the SQS queue
     */
    protected abstract String getPartialQueueName();

    @VisibleForTesting
    String getQueueName() {
        return getPartialQueueName() + getTestSeedIfAvaliable();
    }

    @VisibleForTesting
    List<SendMessageBatchResult> sendBatches(String queueUrl,
            List<SendMessageBatchRequestEntry> entries, Pair<String, String> logFormat) {
        List<List<SendMessageBatchRequestEntry>> partitionedEntries = Lists
                .partition(entries, SQS_MESSAGE_BATCH_MAX_SIZE);

        List<SendMessageBatchResult> results = new LinkedList<>();
        for (List<SendMessageBatchRequestEntry> partition : partitionedEntries) {
            SendMessageBatchResult result = sqsClient.sendMessageBatch(queueUrl,
                    partition);
            results.add(result);
            if (logFormat != null && !result.getSuccessful().isEmpty()) {
                Map<String, String> messageCache = entries.stream()
                        .collect(Collectors.toMap(SendMessageBatchRequestEntry::getId,
                                SendMessageBatchRequestEntry::getMessageBody));
                String successResults = result.getSuccessful().stream()
                        .map(successEntry -> messageCache.get(successEntry.getId()))
                        .collect(Collectors.joining(System.lineSeparator()));
                getLogger().info(logFormat.getLeft(), successResults);
            }
        }
        return results;
    }

    @VisibleForTesting
    List<SendMessageBatchResult> resendFailedMessages(String queueUrl,
            List<SendMessageBatchResult> results, Pair<String, String> logFormat)
            throws InterruptedException {
        int failureCount = 0;
        while (hasFailure(results)) {
            List<SendMessageBatchRequestEntry> retries = createRequestsFrom(results);
            if (failureCount >= MAXIMUM_LIMIT_OF_FAILURES) {
                if (logFormat != null) {
                    slackService.send(logFormat.getRight(), getCurrentCountry().getCode(),
                            getContentsFrom(retries));
                }
                return results;
            } else {
                failureCount++;
                if (logFormat != null) {
                    for (SendMessageBatchRequestEntry retryEntry : retries) {
                        getLogger().warn(RETRY_FORMAT, failureCount,
                                retryEntry.getMessageBody());
                    }
                }
                Thread.sleep(RETRY_INTERVAL_IN_MILLIS);
                results = sendBatches(queueUrl, retries, logFormat);
            }
        }
        return new LinkedList<>();
    }

    @VisibleForTesting
    boolean hasFailure(List<SendMessageBatchResult> results) {
        for (SendMessageBatchResult result : results) {
            if (result.getFailed().size() > 0) {
                return true;
            }
        }
        return false;
    }

    @VisibleForTesting
    List<SendMessageBatchRequestEntry> createRequestsFrom(
            List<SendMessageBatchResult> results) {
        List<SendMessageBatchRequestEntry> requestEntries = new LinkedList<>();
        for (SendMessageBatchResult result : results) {
            for (BatchResultErrorEntry errorEntries : result.getFailed()) {
                requestEntries.add(new SendMessageBatchRequestEntry(errorEntries.getId(),
                        errorEntries.getMessage()));
            }
        }
        return requestEntries;
    }

    @VisibleForTesting
    String getContentsFrom(List<SendMessageBatchRequestEntry> requests) {
        StringBuilder builder = new StringBuilder();
        for (SendMessageBatchRequestEntry request : requests) {
            builder.append(request.getMessageBody()).append(System.lineSeparator());
        }
        return builder.toString();
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    private String getTestSeedIfAvaliable() {
        String seed = getProperty(SYSTEM_PROPERTY_NAME_FOR_TEST_SEED);
        return getCurrentEnvironment() == DEV && !Strings.isNullOrEmpty(seed) ? seed
                : EMPTY;
    }

    private boolean isNotTestContext() {
        return EMPTY.equals(getTestSeedIfAvaliable());
    }

    private String generateSeed(int length) {
        return "_" + new StringHelper().generateRandomAlphanumericString(length);
    }
}
