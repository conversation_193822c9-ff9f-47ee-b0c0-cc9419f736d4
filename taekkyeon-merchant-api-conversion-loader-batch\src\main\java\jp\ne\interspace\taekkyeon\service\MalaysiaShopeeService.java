/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.gson.JsonObject;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;

import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;

import static com.google.common.base.Strings.isNullOrEmpty;
import static java.lang.String.format;
import static java.math.BigDecimal.ZERO;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.COMMA;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER_YYYYMMDD;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_CAMPAIGN_ID;
import static lombok.AccessLevel.PACKAGE;

/**
 * Implements {@link AbstractShopeeService} for Malaysia shopee.
 *
 * <AUTHOR>
 */
public class MalaysiaShopeeService extends AbstractShopeeService {

    private static final int PRODUCT_RESULT_ID = 3;
    private static final int CATEGORY_RESULT_ID = 30;
    private static final String MD5_CAMPAIGN_ID_982 = "fec8d47d412bcbeece3d9128ae855a7a";
    private static final String CATEGORY_ID_BRAND_COMMISSION = "Brand Commission";
    private static final String SHOPEE_COMM = "ShopeeComm";

    @Inject @Named(BIND_KEY_CAMPAIGN_ID) @Getter(PACKAGE)
    private long campaignId;

    @Override
    public List<ConversionRegistrationDetails> parse(String responseBody)
            throws Exception {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        if (!isNullOrEmpty(responseBody)) {
            JsonObject obj = getJsonParser().parse(responseBody).getAsJsonObject();
            ShopeeConversionReport conversionReport = parseConversionReport(obj);
            ZoneId zoneId = getZoneId();
            for (ShopeeNode shopeeNode : conversionReport.getNodes()) {
                String[] utmContents = shopeeNode.getUtmContent().split(HYPHEN);
                String targetMd5CampaignId = hashMd5By(getCampaignId());
                if (isTargetCampaignId(utmContents, targetMd5CampaignId)) {
                    String clickId = getClickIdFrom(utmContents);
                    String customerType = shopeeNode.getBuyerType().toLowerCase();
                    Instant instant = Instant.ofEpochSecond(shopeeNode.getPurchaseTime());
                    ZonedDateTime conversionTime = ZonedDateTime.ofInstant(instant,
                            zoneId);
                    for (ShopeeOrder shopeeOrder : shopeeNode.getOrders()) {
                        String identifier = targetMd5CampaignId
                                .equals(MD5_CAMPAIGN_ID_982)
                                        ? shopeeNode.getCheckoutId()
                                        : format("%s-%s", shopeeNode.getCheckoutId(),
                                shopeeOrder.getOrderId());
                        details.addAll(createConversionDetails(clickId, customerType,
                                conversionTime, identifier, shopeeOrder, targetMd5CampaignId));
                    }
                }
            }
        }
        return details;
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> createConversionDetails(String clickId,
            String customerType, ZonedDateTime conversionTime, String identifier,
            ShopeeOrder shopeeOrder, String targetMd5CampaignId)
            throws UnsupportedEncodingException {
        List<ConversionRegistrationDetails> details = new LinkedList<>();
        List<String> products = new LinkedList<>();
        for (ShopeeItem shopeeItem : shopeeOrder.getItems()) {
            products.add(shopeeItem.getItemId());
            String sku = getSkuFrom(shopeeItem);
            String productId = getItemId(products, sku);
            products.add(sku);
            String categoryId = shopeeItem.getGlobalCategoryLv1Name()
                    .replaceAll(COMMA, EMPTY);

            String conversionOccursDate = conversionTime.format(
                    DATE_TIME_FORMATTER_YYYYMMDD);
            String modelId = shopeeItem.getModelId();
            String key = Joiner.on(HYPHEN)
                    .join(getCampaignId(), conversionOccursDate, identifier,
                            shopeeOrder.getOrderId(), shopeeItem.getItemId(), modelId);
            if (isConversionUnique(key)) {
                String newCustomerType = validateDateTimeAfter(conversionTime)
                        ? getNewCustomerTypeSuffix(customerType,
                        shopeeItem.getAttributionType()) : customerType;

                String productIdShopeeComm = getProductId(shopeeItem,
                        shopeeOrder.getOrderId(), productId, shopeeItem.getItemCommission(),
                        SHOPEE_COMM);

                boolean isBrandItem = isBrandItem(shopeeItem.getShopId());

                List<ConversionRegistrationDetails> brandItemConversions =
                        processConversionByBrandItemRules(conversionTime, identifier,
                                getResultIdBy(targetMd5CampaignId), newCustomerType,
                                categoryId, productIdShopeeComm, shopeeItem.getActualAmount(),
                                clickId, shopeeItem.getShopId(), isBrandItem);

                if (!targetMd5CampaignId.equals(MD5_CAMPAIGN_ID_982)) {
                    List<ConversionRegistrationDetails> bonusConversions = createExtraBonuses(
                            conversionTime, identifier, PRODUCT_RESULT_ID,
                            newCustomerType, CATEGORY_ID_BRAND_COMMISSION, productId,
                            clickId, shopeeItem, shopeeOrder, brandItemConversions);
                    brandItemConversions.addAll(bonusConversions);
                }
                insertDataProceeded(createConversionProceeded(conversionTime,
                        brandItemConversions, key));
                details.addAll(brandItemConversions);
            }

        }
        if (targetMd5CampaignId.equals(MD5_CAMPAIGN_ID_982)) {
            return mappingConversionRegistrationDetails(
                    getResultIdBy(targetMd5CampaignId), details);
        }

        return details;
    }

    @VisibleForTesting
    List<ConversionRegistrationDetails> mappingConversionRegistrationDetails(
            int resultId, List<ConversionRegistrationDetails> details) {
        List<ConversionRegistrationDetails> result = new LinkedList<>();
        Map<String, List<ConversionRegistrationDetails>> goods = details.stream()
                .collect(groupingBy(
                        ConversionRegistrationDetails::getTransactionId, toList()));
        goods.forEach((identifier, conversions) -> {
            BigDecimal totalValue = conversions.stream()
                    .map(ConversionRegistrationDetails::getProductUnitPrice)
                    .reduce(ZERO, BigDecimal::add);
            result.addAll(details.stream()
                    .map(item -> new ConversionRegistrationDetails(
                            item.getConversionTime(), item.getTransactionId(), resultId,
                            item.getCustomerType(), item.getProductCategoryId(),
                            item.getProductId(), totalValue, item.getClickId()))
                    .collect(toList()));
        });
        return result;
    }

    @VisibleForTesting
    int getResultIdBy(String targetMd5CampaignId) {
        return targetMd5CampaignId.equals(MD5_CAMPAIGN_ID_982) ? 100 : CATEGORY_RESULT_ID;
    }

    @VisibleForTesting
    String getClickIdFrom(String[] utmContent) {
        if (utmContent.length >= 2 && !isNullOrEmpty(utmContent[1])) {
            return utmContent[1].length() > 7 ? utmContent[1] : utmContent[0];
        }
        return EMPTY;
    }
}
