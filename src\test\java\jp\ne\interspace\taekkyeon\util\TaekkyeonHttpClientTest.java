/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.util;

import java.io.IOException;
import java.util.Map;

import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.model.ResponseStatus;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.FOUND;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.INTERNAL_SERVER_ERROR;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.MOVED_PERMANENTLY;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.OK;
import static jp.ne.interspace.taekkyeon.model.ResponseStatus.TEMPORARY_REDIRECT;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link TaekkyeonHttpClient}.
 *
 * <AUTHOR> Shin
 */
@RunWith(MockitoJUnitRunner.class)
public class TaekkyeonHttpClientTest {

    private static final String URL = "http://interspace.co.jp";
    private static final String USER_AGENT = "userAgent";

    @InjectMocks @Spy
    private TaekkyeonHttpClient underTest;

    @Mock
    private HttpPost request;

    @Mock
    private CloseableHttpResponse response;

    @Mock
    private HttpEntity entity;

    @Mock
    private Map<String, String> headers;

    @Mock
    private Object requestBody;

    @Mock
    private Logger logger;

    @Test
    public void testPostShouldReturnNullThenCloseEntityWhenResponseStatusIsNotOk()
            throws Exception {
        // given
        doReturn(request).when(underTest).createPostRequestWith(URL, headers, requestBody);
        doReturn(response).when(underTest).getResponseOf(request);
        when(response.getEntity()).thenReturn(entity);
        doReturn(INTERNAL_SERVER_ERROR).when(underTest).getResponseStatusFrom(response);
        doReturn(logger).when(underTest).getLogger();

        // when
        String actual = underTest.post(URL, headers, requestBody);

        // then
        assertNull(actual);
        verify(underTest).closeQuietly(response, entity);
        verify(logger).error("A request has been sent to [http://interspace.co.jp]"
                + " but has failed - response status: [500 Internal Server Error]");
        verify(logger, never()).info(anyString(), anyString(), anyMap(), anyString());
    }

    @Test
    public void testPostShouldReturnNullThenCloseEntityWhenResponseStatusIsOkButEntityIsNull()
            throws Exception {
        // given
        HttpEntity emptyEntity = null;

        doReturn(request).when(underTest).createPostRequestWith(URL, headers,
                requestBody);
        doReturn(response).when(underTest).getResponseOf(request);
        when(response.getEntity()).thenReturn(emptyEntity);
        doReturn(OK).when(underTest).getResponseStatusFrom(response);
        doReturn(logger).when(underTest).getLogger();

        // when
        String actual = underTest.post(URL, headers, requestBody);

        // then
        assertNull(actual);
        verify(underTest).closeQuietly(response, emptyEntity);
        verify(logger).error("A request has been sent to [http://interspace.co.jp]"
                + " but has failed - response status: [200 OK]");
        verify(logger, never()).info(anyString(), anyString(), anyMap(), anyString());
    }

    @Test
    public void testPostShouldReturnContentsThenCloseEntityWhenResponseStatusIsOkAndEntityHaveContents()
            throws Exception {
        // given
        String expected = "some_contents";
        String requestBodyJson = "requestBodyJson";

        doReturn(request).when(underTest).createPostRequestWith(URL, headers, requestBody);
        doReturn(response).when(underTest).getResponseOf(request);
        when(response.getEntity()).thenReturn(entity);
        doReturn(OK).when(underTest).getResponseStatusFrom(response);
        doReturn(expected).when(underTest).getContentsFrom(entity);
        doReturn(logger).when(underTest).getLogger();
        doReturn(requestBodyJson).when(underTest).toJson(requestBody);

        // when
        String actual = underTest.post(URL, headers, requestBody);

        // then
        assertNotNull(actual);
        assertEquals(expected, actual);
        verify(underTest).closeQuietly(response, entity);
        verify(logger).info(
                "Successfully sent POST request to [{}] with "
                + "headers [{}] with request body [{}].", URL, headers, requestBodyJson);
        verify(logger, never()).error(anyString());
    }

    @Test
    public void testRedirectShouldReturnEmptyWhenRedirectIsNotSuccess()
            throws IOException {
        // given
        HttpGet httpGet = mock(HttpGet.class);
        CloseableHttpResponse response = mock(CloseableHttpResponse.class);

        doReturn(httpGet).when(underTest).createGetRequestWith(URL, USER_AGENT);
        doReturn(response).when(underTest).getResponseOf(httpGet);
        doReturn(OK).when(underTest).getResponseStatusFrom(response);
        doReturn(false).when(underTest).isRedirect(OK);

        // when
        String actual = underTest.redirect(URL, USER_AGENT);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testRedirectShouldReturnEmptyWhenRedirectIsSuccessAndResponseHeaderIsNull()
            throws IOException {
        // given
        HttpGet httpGet = mock(HttpGet.class);
        CloseableHttpResponse response = mock(CloseableHttpResponse.class);

        doReturn(httpGet).when(underTest).createGetRequestWith(URL, USER_AGENT);
        doReturn(response).when(underTest).getResponseOf(httpGet);
        doReturn(MOVED_PERMANENTLY).when(underTest).getResponseStatusFrom(response);
        doReturn(true).when(underTest).isRedirect(MOVED_PERMANENTLY);
        when(response.getFirstHeader("Location")).thenReturn(null);

        // when
        String actual = underTest.redirect(URL, USER_AGENT);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testRedirectShouldReturnCorrectUrlWhenRedirectIsSuccessAndResponseHeaderIsNotNull()
            throws IOException {
        // given
        String redirectUrl = "https://www.test.com/test";
        HttpGet httpGet = mock(HttpGet.class);
        CloseableHttpResponse response = mock(CloseableHttpResponse.class);
        Header header = mock(Header.class);

        doReturn(httpGet).when(underTest).createGetRequestWith(URL, USER_AGENT);
        doReturn(response).when(underTest).getResponseOf(httpGet);
        doReturn(MOVED_PERMANENTLY).when(underTest).getResponseStatusFrom(response);
        doReturn(true).when(underTest).isRedirect(MOVED_PERMANENTLY);
        when(response.getFirstHeader("Location")).thenReturn(header);
        when(header.getValue()).thenReturn(redirectUrl);

        // when
        String actual = underTest.redirect(URL, USER_AGENT);

        // then
        assertSame(redirectUrl, actual);
    }

    @Test
    public void testGetResponseStatusShouldReturnCorrectResponseWhenCalled()
            throws IOException {
        // given
        ArgumentCaptor<HttpGet> captor = ArgumentCaptor.forClass(HttpGet.class);
        doReturn(response).when(underTest).getResponseOf(captor.capture());
        doReturn(OK).when(underTest).getResponseStatusFrom(response);

        // when
        ResponseStatus actual = underTest.getResponseStatus(URL);

        // then
        assertEquals(OK, actual);

        HttpGet request = captor.getValue();
        assertNotNull(request);
        assertEquals(URL, request.getURI().toString());
    }

    @Test
    public void testIsRedirectShouldReturnTrueWhenResponseStatusIsMovedPermanently() {
        // when
        boolean actual = underTest.isRedirect(MOVED_PERMANENTLY);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsRedirectShouldReturnTrueWhenResponseStatusIsFound() {
        // when
        boolean actual = underTest.isRedirect(FOUND);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsRedirectShouldReturnTrueWhenResponseStatusIsTemporaryRedirect() {
        // when
        boolean actual = underTest.isRedirect(TEMPORARY_REDIRECT);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsRedirectShouldReturnFalseWhenResponseStatusIsNotMovedPermanentlyAndFoundAndTemporaryRedirect() {
        // when
        boolean actual = underTest.isRedirect(OK);

        // then
        assertFalse(actual);
    }
}
