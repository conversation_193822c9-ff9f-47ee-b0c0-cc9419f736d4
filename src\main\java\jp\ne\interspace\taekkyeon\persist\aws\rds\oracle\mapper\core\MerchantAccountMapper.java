/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core;

import org.apache.ibatis.annotations.Select;

/**
 * MyBatis mapper for handling merchant-account-related database operations.
 *
 * <AUTHOR>
 */
public interface MerchantAccountMapper {

    String SELECT_COUNTRY_CODE_BY_CAMPAIGN_ID =
            "SELECT"
            + "    ma.country_code "
            + "FROM "
            + "    merchant_account ma "
            + "INNER JOIN "
            + "    merchant_campaign mc "
            + "ON "
            + "    ma.account_no = mc.account_no "
            + "WHERE "
            + "    mc.campaign_no = #{campaignId}";

    /**
     * Returns the country code of merchant by the given campaign ID.
     *
     * @param campaignId
     *          ID of the given campaign
     * @return the country code of merchant by the given campaign ID
     * @see #SELECT_COUNTRY_CODE_BY_CAMPAIGN_ID
     */
    @Select(SELECT_COUNTRY_CODE_BY_CAMPAIGN_ID)
    String findCountryCodeBy(long campaignId);
}
