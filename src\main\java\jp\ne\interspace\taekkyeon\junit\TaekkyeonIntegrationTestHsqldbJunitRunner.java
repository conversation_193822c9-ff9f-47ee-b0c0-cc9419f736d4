/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.junit;

import java.io.IOException;
import java.lang.annotation.Annotation;
import java.sql.SQLException;

import org.apache.ibatis.io.Resources;
import org.apache.ibatis.jdbc.ScriptRunner;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.runners.model.InitializationError;

import static com.google.inject.Key.get;

/**
 * HSQLDB initializer for Integration JUnit tests.
 *
 * <AUTHOR>
 */
public class TaekkyeonIntegrationTestHsqldbJunitRunner extends TaekkyeonJunitRunner {

    /**
     * Initializes an in-memory database instance for integration tests.
     *
     * @param testClass
     *            JUnit test class to be run
     * @throws InitializationError
     *             when test setup or Guice setup or DB setup fails
     */
    public TaekkyeonIntegrationTestHsqldbJunitRunner(Class<?> testClass)
            throws InitializationError {
        super(testClass);

        for (Annotation annotation : getAnnotations(testClass)) {
            initializeEmbeddedHsqlDbInstance(annotation);
        }
    }

    // this initialization could not be embedded into TaekkyeonOracleHsqldbJunitModule
    // due to the access restriction of the Injector reference
    private void initializeEmbeddedHsqlDbInstance(Annotation annotation)
            throws InitializationError {
        try {
            SqlSessionFactory sqlSessionFactory = getInjector()
                    .getInstance(get(SqlSessionFactory.class, annotation));
            if (sqlSessionFactory != null) {
                HsqldbSqlFilePath filePath = getInjector()
                        .getInstance(get(HsqldbSqlFilePath.class, annotation));
                Environment environment = sqlSessionFactory.getConfiguration()
                        .getEnvironment();
                ScriptRunner runner = new ScriptRunner(
                        environment.getDataSource().getConnection());
                runner.setLogWriter(null); // gotta set null to keep script init quiet
                runner.setAutoCommit(true);
                runner.setStopOnError(true);
                runner.runScript(Resources
                        .getResourceAsReader(filePath.getIntegrationTestSchema()));
                runner.runScript(
                        Resources.getResourceAsReader(filePath.getIntegrationTestData()));
                runner.closeConnection();
            }
        } catch (SQLException | IOException e) {
            throw new InitializationError(e);
        }
    }

}
