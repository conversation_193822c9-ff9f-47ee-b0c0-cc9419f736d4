/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.lang.annotation.Annotation;
import java.util.Objects;
import java.util.stream.Stream;

import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import static java.lang.String.join;
import static java.util.Arrays.stream;

/**
 * Country settings for Taekkyeon runtime.
 *
 * <AUTHOR> OBS DEV Team
 */
@RequiredArgsConstructor
public enum Country {

    /**
     * Indonesia, which should be specified as a VM argument <i>-Dcountry=ID</i>
     * or <i>-Denvironment=id</i>.
     */
    INDONESIA("ID", "in", Indonesia.class, "indonesia"),

    /**
     * Thailand, which should be specified as a VM argument <i>-Dcountry=TH</i>
     * or <i>-Denvironment=th</i>.
     */
    THAILAND("TH", "th", Thailand.class, "thailand"),

    /**
     * Vietnam, which should be specified as a VM argument <i>-Dcountry=VN</i>
     * or <i>-Denvironment=vn</i>.
     */
    VIETNAM("VN", "vi", Vietnam.class, "vietnam"),

    /**
     * Malaysia and Singapore is for specifying country of database.
     */
    MALAYSIA("MY", "", Indonesia.class, "indonesia"),
    SINGAPORE("SG", "", Indonesia.class, "indonesia"),
    PHILIPPINES("PH", "", Indonesia.class, "indonesia"),
    TAIWAN("TW", "", Indonesia.class, "indonesia");

    private static final String VM_ARG_NAME_FOR_COUNTRY = "country";

    @NonNull @Getter private String code;
    @NonNull @Getter private String language;
    @NonNull @Getter private Class<? extends Annotation> annotationClass;

    /**
     * Returns country prefix for the current country.
     */
    @NonNull @Getter private String countryPrefix;

    /**
     * Get the current {@link Country} setting corresponding to the given VM argument
     * {@code -Dcountry}.
     *
     * @return {@link Country} setting
     */
    public static Country getCurrentCountry() {
        return Stream.of(VM_ARG_NAME_FOR_COUNTRY)
                .map(System::getProperty)
                .filter(Objects::nonNull)
                .map(Country::match)
                .findFirst().get();
    }

    /**
     * Get the {@link Country} by the given country code.
     *
     * @param countryCode
     *          short code by the given country
     * @return {@link Country} setting
     */
    public static Country getCountryBy(String countryCode) {
        return Stream.of(countryCode)
                .map(Country::match)
                .findFirst().get();
    }

    private static Country match(final String property) {
        return stream(values())
                .filter(env -> env.getCode().equalsIgnoreCase(property))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(
                        join(property, "No enum constant [", "] found")));
    }
}
