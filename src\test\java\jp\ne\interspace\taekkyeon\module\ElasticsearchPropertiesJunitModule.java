/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import com.google.common.collect.ImmutableTable;
import com.google.inject.AbstractModule;
import com.google.inject.TypeLiteral;

import static com.google.inject.name.Names.named;
import static jp.ne.interspace.taekkyeon.persist.elasticsearch.EmbeddedElasticsearch.BIND_KEY_ELASTICSEARCH_TEST_DATA_PATH;

/**
 * Guice module for the various batch-specific system properties in JUnit tests.
 *
 * <AUTHOR> Shin
 */
public class ElasticsearchPropertiesJunitModule extends AbstractModule {

    private static final ImmutableTable<String, String, String> EMBEDDED_ELASTICSEARCH_TEST_DATA_PATH = new ImmutableTable.Builder<String, String, String>()
            .put("initial-test-data", "test",
                    "database/elasticsearch/elasticsearch-initial-test-data.json")
            .put("delete-test-data", "test",
                    "database/elasticsearch/elasticsearch-delete-test-data.json")
            .put("update-test-data", "test",
                    "database/elasticsearch/elasticsearch-update-test-data.json")
            .put("count-test-data", "test",
                    "database/elasticsearch/elasticsearch-count-test-data.json")
            .build();

    @Override
    protected void configure() {
        bind(new TypeLiteral<ImmutableTable<String, String, String>>() {
        }).annotatedWith(named(BIND_KEY_ELASTICSEARCH_TEST_DATA_PATH))
                .toInstance(EMBEDDED_ELASTICSEARCH_TEST_DATA_PATH);
    }

}
