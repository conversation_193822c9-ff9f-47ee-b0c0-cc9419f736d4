/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import static java.math.BigDecimal.ZERO;

/**
 * DTO holding the location data of ads scraper.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @ToString
public class Location {

    public static final Location DEFAULT_LOCATION = new Location(ZERO, ZERO);

    private final BigDecimal latitude;
    private final BigDecimal longitude;
}
