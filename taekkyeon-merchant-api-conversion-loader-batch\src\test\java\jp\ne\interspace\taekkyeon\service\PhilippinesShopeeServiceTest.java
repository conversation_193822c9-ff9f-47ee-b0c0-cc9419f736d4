/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

import com.google.gson.JsonParser;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import jp.ne.interspace.taekkyeon.model.CampaignAdvType;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.Merchant;
import jp.ne.interspace.taekkyeon.model.MerchantIntegrationHistoryInsertRequest;
import jp.ne.interspace.taekkyeon.model.ShopeeConversionReport;
import jp.ne.interspace.taekkyeon.model.ShopeeItem;
import jp.ne.interspace.taekkyeon.model.ShopeeNode;
import jp.ne.interspace.taekkyeon.model.ShopeeOrder;

import static java.math.BigDecimal.ZERO;
import static java.time.ZoneId.of;
import static java.time.ZonedDateTime.of;
import static java.util.Arrays.asList;
import static jp.ne.interspace.taekkyeon.model.Merchant.SHOPEE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

/**
 * Unit test for {@link PhilippinesShopeeService}.
 *
 * <AUTHOR> Van
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class PhilippinesShopeeServiceTest {

    private static final ZoneId ZONE_ID = of("Asia/Manila");
    private static final int PRODUCT_RESULT_ID = 3;
    private static final int CATEGORY_RESULT_ID = 30;
    private static final String CUSTOMER_TYPE = "customerType";
    private static final String CLICK_ID = "clickId";
    private static final String CHECKOUT_ID = "checkoutId";
    private static final String CONVERSION_ID = "conversionId";
    private static final BigDecimal GROSS_COMMISSION = new BigDecimal("400");
    private static final long PURCHASE_TIME2 = 1589613100;
    private static final ZonedDateTime TIME2 = of(2020, 5, 16, 15, 11, 40, 0, ZONE_ID);
    private static final BigDecimal CAPPED_COMMISSION = new BigDecimal("410");
    private static final BigDecimal TOTAL_BRAND_COMMISSION = new BigDecimal("420");
    private static final BigDecimal ESTIMATED_TOTAL_COMMISSION = new BigDecimal("430");
    private static final BigDecimal ITEM_PRICE = new BigDecimal("100");
    private static final BigDecimal ACTUAL_AMOUNT = new BigDecimal("110");
    private static final BigDecimal ITEM_COMMISSION = new BigDecimal("10");
    private static final BigDecimal GROSS_BRAND_COMMISSION = new BigDecimal("20");
    private static final String MODEL_ID = "modelId";
    private static final String GLOBAL_CATEGORY_LV1_NAME = "category, name";
    private static final String BUYER_TYPE = "EXISTING";
    private static final String UTM_CONTENT = "12345-RKVALUEQQQ-url";
    private static final String UTM_CONTENT_954 = "12345-RKVALUEQQQ-url-ca75910166da03ff9d4655a0338e6b09";
    private static final String MD5_HASHED_CAMPAIGN_ID_954 = "ca75910166da03ff9d4655a0338e6b09";
    private static final String ITEM_ID = "id";
    private static final long CAMPAIGN_ID_954 = 954;
    private static final String CLICK_ID_RESULT = "RKVALUEQQQ";
    private static final String EXISTING_RESULT = "existing";
    private static final String IDENTIFIER_RESULT = "checkoutId-orderId";
    private static final String ORDER_ID = "orderId";
    private static final String SHOP_TYPE = "shopType";
    private static final String CATEGORY_NAME = "category name";
    private static final Merchant SHOPEE_MERCHANT = SHOPEE;
    private static final String ORDERED_IN_SAME_SHOP = "ORDERED_IN_SAME_SHOP";
    private static final String NEW_CUSTOMER_TYPE = "customerType-direct";
    private static final ZonedDateTime CONVERSION_TIME = of(2024, 10, 1, 0, 0, 20, 0, ZONE_ID);
    private static final BigDecimal ITEM_SELLER_COMMISSION = new BigDecimal("20");

    @InjectMocks @Spy
    private PhilippinesShopeeService underTest;

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenGrossBrandCommissionIsGreaterThanZero()
            throws Exception {
        // give
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, null, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, GROSS_BRAND_COMMISSION, null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder(ORDER_ID, SHOP_TYPE, asList(shopeeItem));
        ConversionRegistrationDetails expected1 = new ConversionRegistrationDetails(
                CONVERSION_TIME, CHECKOUT_ID, CATEGORY_RESULT_ID, NEW_CUSTOMER_TYPE,
                CATEGORY_NAME, "id_modelId", ACTUAL_AMOUNT, CLICK_ID);
        ConversionRegistrationDetails expected2 = new ConversionRegistrationDetails(
                CONVERSION_TIME, CHECKOUT_ID, PRODUCT_RESULT_ID, NEW_CUSTOMER_TYPE, null,
                "id_modelId-XTRAComm", GROSS_BRAND_COMMISSION, CLICK_ID);
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(ACTUAL_AMOUNT).when(underTest).calculateProductUnitPrice(ACTUAL_AMOUNT,
                CONVERSION_TIME);
        doReturn(GROSS_BRAND_COMMISSION).when(underTest)
                .calculateProductUnitPrice(GROSS_BRAND_COMMISSION, CONVERSION_TIME);
        doReturn(ZONE_ID).when(underTest).getZoneId();
        doReturn(0L).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandItem(anyString());
        doReturn(CampaignAdvType.REGULAR).when(underTest).findCampaignType(anyLong());
        doReturn("id_modelId-XTRAComm").when(underTest)
                .getProductId(any(), any(), any(), any(), any());

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, CONVERSION_TIME, CHECKOUT_ID, shopeeOrder);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        ConversionRegistrationDetails expected3 = new ConversionRegistrationDetails(
                CONVERSION_TIME, CHECKOUT_ID, PRODUCT_RESULT_ID, NEW_CUSTOMER_TYPE, null,
                "id_modelId-XTRAComm", GROSS_BRAND_COMMISSION, CLICK_ID);
        assertEquals(expected1, actual.get(0));
        assertEquals(expected2, actual.get(1));
        assertEquals(expected3, actual.get(2));
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenGrossBrandCommissionIsZero()
            throws Exception {
        // give
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, null, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, ZERO, null, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder(ORDER_ID, SHOP_TYPE, asList(shopeeItem));
        ConversionRegistrationDetails expected = new ConversionRegistrationDetails(
                CONVERSION_TIME, CHECKOUT_ID, CATEGORY_RESULT_ID, NEW_CUSTOMER_TYPE,
                CATEGORY_NAME, "id_modelId", ACTUAL_AMOUNT, CLICK_ID);
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(ACTUAL_AMOUNT).when(underTest).calculateProductUnitPrice(ACTUAL_AMOUNT,
                CONVERSION_TIME);
        doReturn(ZONE_ID).when(underTest).getZoneId();
        doReturn(0L).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandItem(anyString());
        doReturn(CampaignAdvType.REGULAR).when(underTest).findCampaignType(anyLong());

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, CONVERSION_TIME, CHECKOUT_ID, shopeeOrder);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertEquals(asList(expected), actual);
    }

    @Test
    public void testCreateConversionDetailsShouldNotReturnDataDuplicateWhenIsDuplicateConversionCheckEnabledAndDuplicateConversion()
            throws Exception {
        // give
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, null, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder(ORDER_ID, SHOP_TYPE, asList(shopeeItem));
        MerchantIntegrationHistoryInsertRequest request = mock(
                MerchantIntegrationHistoryInsertRequest.class);
        String key = "0-20200516-checkoutId-orderId-id-modelId";
        doReturn(true).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(true).when(underTest).isDuplicateConversion(key);
        doReturn(0L).when(underTest).getCampaignId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, TIME2, CHECKOUT_ID, shopeeOrder);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.size());
        verify(underTest, never()).insertDataProceeded(request);
    }

    @Test
    public void testParseShouldCallCreateConversionDetailsMethodWhenIsTargetCampaignIdTrue()
            throws Exception {
        // given
        ShopeeOrder shopeeOrder = new ShopeeOrder(ORDER_ID, SHOP_TYPE, null);
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION, PURCHASE_TIME2, CAPPED_COMMISSION,
                TOTAL_BRAND_COMMISSION, ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE,
                UTM_CONTENT_954, null, null, asList(shopeeOrder));
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(
                asList(shopeeNode), null);
        String responseBody = "{}";
        doReturn(conversionReport).when(underTest).parseConversionReport(
                new JsonParser().parse(responseBody).getAsJsonObject());
        List<ConversionRegistrationDetails> details = asList(
                mock(ConversionRegistrationDetails.class));

        doReturn(CAMPAIGN_ID_954).when(underTest).getCampaignId();
        doReturn(MD5_HASHED_CAMPAIGN_ID_954).when(underTest).hashMd5By(CAMPAIGN_ID_954);
        doReturn(details).when(underTest).createConversionDetails(CLICK_ID_RESULT,
                EXISTING_RESULT, TIME2, IDENTIFIER_RESULT, shopeeOrder);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(responseBody);

        // then
        assertNotNull(actual);
        assertEquals(details, actual);
        verify(underTest).createConversionDetails(CLICK_ID_RESULT, EXISTING_RESULT, TIME2,
                IDENTIFIER_RESULT, shopeeOrder);
    }

    @Test
    public void testParseShouldCallCreateConversionDetailsMethodWhenIsTargetCampaignIdFalse()
            throws Exception {
        // given
        ShopeeOrder shopeeOrder = new ShopeeOrder(ORDER_ID, SHOP_TYPE, null);
        ShopeeNode shopeeNode = new ShopeeNode(CHECKOUT_ID, CONVERSION_ID,
                GROSS_COMMISSION, PURCHASE_TIME2, CAPPED_COMMISSION,
                TOTAL_BRAND_COMMISSION, ESTIMATED_TOTAL_COMMISSION, BUYER_TYPE,
                UTM_CONTENT, null, null, asList(shopeeOrder));
        ShopeeConversionReport conversionReport = new ShopeeConversionReport(
                asList(shopeeNode), null);
        String responseBody = "{}";
        doReturn(conversionReport).when(underTest).parseConversionReport(
                new JsonParser().parse(responseBody).getAsJsonObject());

        doReturn(CAMPAIGN_ID_954).when(underTest).getCampaignId();
        doReturn(MD5_HASHED_CAMPAIGN_ID_954).when(underTest).hashMd5By(CAMPAIGN_ID_954);
        doReturn(ZONE_ID).when(underTest).getZoneId();

        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(responseBody);

        // then
        assertNotNull(actual);
        verify(underTest, never()).createConversionDetails(CLICK_ID_RESULT,
                EXISTING_RESULT, TIME2, IDENTIFIER_RESULT, shopeeOrder);
    }

    @Test
    public void testCreateConversionDetailsShouldReturnCorrectDataWhenItemSellerCommissionNotNullAndGreaterThanZero()
            throws Exception {
        // give
        ShopeeItem shopeeItem = new ShopeeItem(ITEM_ID, null, null, 1, ITEM_PRICE,
                ACTUAL_AMOUNT, ITEM_COMMISSION, null, ITEM_SELLER_COMMISSION, MODEL_ID,
                GLOBAL_CATEGORY_LV1_NAME, null, null, ORDERED_IN_SAME_SHOP);
        ShopeeOrder shopeeOrder = new ShopeeOrder(ORDER_ID, SHOP_TYPE, asList(shopeeItem));
        ConversionRegistrationDetails expected1 = new ConversionRegistrationDetails(
                CONVERSION_TIME, CHECKOUT_ID, CATEGORY_RESULT_ID, NEW_CUSTOMER_TYPE,
                CATEGORY_NAME, "id_modelId", ACTUAL_AMOUNT, CLICK_ID);
        ConversionRegistrationDetails expected2 = new ConversionRegistrationDetails(
                CONVERSION_TIME, CHECKOUT_ID, PRODUCT_RESULT_ID, NEW_CUSTOMER_TYPE, null,
                "id_modelId-XTRAComm", GROSS_BRAND_COMMISSION, CLICK_ID);
        doReturn(false).when(underTest).isDuplicateConversionCheckEnabled();
        doReturn(SHOPEE_MERCHANT).when(underTest).getMerchant();
        doReturn(ACTUAL_AMOUNT).when(underTest).calculateProductUnitPrice(ACTUAL_AMOUNT,
                CONVERSION_TIME);
        doReturn(GROSS_BRAND_COMMISSION).when(underTest)
                .calculateProductUnitPrice(GROSS_BRAND_COMMISSION, CONVERSION_TIME);
        doReturn(ZONE_ID).when(underTest).getZoneId();
        doReturn(0L).when(underTest).getCampaignId();
        doReturn(false).when(underTest).isBrandItem(anyString());
        doReturn(CampaignAdvType.REGULAR).when(underTest).findCampaignType(anyLong());
        doReturn("id_modelId-XTRAComm").when(underTest)
                .getProductId(any(), any(), any(), any(), any());

        // when
        List<ConversionRegistrationDetails> actual = underTest.createConversionDetails(
                CLICK_ID, CUSTOMER_TYPE, CONVERSION_TIME, CHECKOUT_ID, shopeeOrder);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        ConversionRegistrationDetails expected3 = new ConversionRegistrationDetails(
                CONVERSION_TIME, CHECKOUT_ID, PRODUCT_RESULT_ID, NEW_CUSTOMER_TYPE, null,
                "id_modelId-XTRAComm", GROSS_BRAND_COMMISSION, CLICK_ID);
        assertEquals(expected1, actual.get(0));
        assertEquals(expected2, actual.get(1));
        assertEquals(expected3, actual.get(2));
    }

    @Test
    public void testParseShouldReturnEmptyListWhenResponseBodyIsNull() throws Exception {
        // when
        List<ConversionRegistrationDetails> actual = underTest.parse(null);

        // then
        assertTrue(actual.isEmpty());
    }
}
