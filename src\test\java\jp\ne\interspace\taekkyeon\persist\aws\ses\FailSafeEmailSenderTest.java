/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.ses;

import java.io.IOException;
import java.util.Locale;
import java.util.Map;

import javax.mail.MessagingException;

import com.google.common.collect.ImmutableMap;

import freemarker.template.TemplateException;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link FailSafeEmailSender}.
 *
 * <AUTHOR> Varga
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class FailSafeEmailSenderTest {

    private static final String SENDER = "sender";
    private static final String RECIPIENT = "recipient";
    private static final String SUBJECT = "subject";
    private static final String BODY = "body";
    private static final String BODY_TEMPLATE_FILE_NAME = "bodyTemplateFileName";
    private static final Object TEMPLATE_PARAMETERS = new Object();
    private static final String ATTACHMENT_PATH = "attachmentPath";
    private static final Locale LOCALE = Locale.JAPAN;
    private static final Map<String, String> TAGS = ImmutableMap.of("key", "value");

    private static final String CALL_SEND_MESSAGE = "Call send";
    private static final String CALL_SEND_WITH_CUSTOM_TAGS_MESSAGE = "Call sendWithCustomTags";
    private static final String CALL_SEND_WITH_ATTACHMENT = "Call sendWithAttachment";
    private static final String EMAIL_TEMPLATE_DETAIL = "sender: {} - recipient: {} - subject: {} - template: {} - parameters: {} - locale: {}";
    private static final String EMAIL_NON_TEMPLATE_DETAIL = "sender: {} - recipient: {} - subject: {} - messageTags: {}";
    private static final String EMAIL_TEMPLATE_DETAIL_WITH_ATTACHMENT = "sender: {} - recipient: {} - subject: {} - template: {} - parameters: {} - attachmentPath: {} - locale: {}";
    private static final String EMAIL_TEMPLATE_SENDING_SUCCESS_MESSAGE = "Email has been sent: "
            + EMAIL_TEMPLATE_DETAIL;
    private static final String EMAIL_TEMPLATE_SENDING_ERROR_MESSAGE = "Could not send email: "
            + EMAIL_TEMPLATE_DETAIL;
    private static final String EMAIL_NON_TEMPLATE_SENDING_SUCCESS_MESSAGE = "Email has been sent: "
            + EMAIL_NON_TEMPLATE_DETAIL;
    private static final String EMAIL_NON_TEMPLATE_SENDING_ERROR_MESSAGE = "Could not send email: "
            + EMAIL_NON_TEMPLATE_DETAIL;
    private static final String EMAIL_TEMPLATE_WITH_ATTACHMENT_SENDING_SUCCESS_MESSAGE = "Email has been sent: "
            + EMAIL_TEMPLATE_DETAIL_WITH_ATTACHMENT;
    private static final String EMAIL_TEMPLATE_WITH_ATTACHMENT_SENDING_ERROR_MESSAGE = "Could not send email: "
            + EMAIL_TEMPLATE_DETAIL_WITH_ATTACHMENT;

    private static final String EMAIL_SENDING_EXCEPTION_ERROR_MESSAGE = "Exception during email sending:";

    @InjectMocks @Spy
    private FailSafeEmailSender underTest;

    @Mock
    private TemplateEmailSender emailSender;

    @Mock
    private Logger logger;

    @Test
    public void testSendShouldNotThrowExceptionWhenEmailSenderDoesNotThrowExceptionAndEmailSendingIsEnabled() {
        // given
        doReturn(logger).when(underTest).getLogger();
        when(underTest.isEmailSendingEnabled()).thenReturn(true);

        // when
        underTest.send(SENDER, RECIPIENT, SUBJECT, BODY_TEMPLATE_FILE_NAME,
                TEMPLATE_PARAMETERS, LOCALE);

        // then
        verify(logger).info(CALL_SEND_MESSAGE);
        verify(logger).info(EMAIL_TEMPLATE_SENDING_SUCCESS_MESSAGE, SENDER, RECIPIENT,
                SUBJECT, BODY_TEMPLATE_FILE_NAME, TEMPLATE_PARAMETERS, LOCALE);

        verify(logger, never()).error(anyString(), anyString(), anyString(), anyString(),
                anyString(), any(), anyString());
        verify(logger, never()).error(anyString(), any(Exception.class));
    }

    @Test
    public void testSendShouldNotThrowExceptionWhenEmailSenderThrowsExceptionAndEmailSendingIsEnabled()
            throws IOException, TemplateException {
        // given
        doReturn(logger).when(underTest).getLogger();
        when(underTest.isEmailSendingEnabled()).thenReturn(true);

        RuntimeException runtimeException = new RuntimeException();
        doThrow(runtimeException).when(emailSender).send(SENDER, RECIPIENT, SUBJECT,
                BODY_TEMPLATE_FILE_NAME, TEMPLATE_PARAMETERS, LOCALE);

        // when
        underTest.send(SENDER, RECIPIENT, SUBJECT, BODY_TEMPLATE_FILE_NAME,
                TEMPLATE_PARAMETERS, LOCALE);

        // then
        verify(logger).info(CALL_SEND_MESSAGE);
        verify(logger).error(EMAIL_TEMPLATE_SENDING_ERROR_MESSAGE, SENDER, RECIPIENT,
                SUBJECT, BODY_TEMPLATE_FILE_NAME, TEMPLATE_PARAMETERS, LOCALE);
        verify(logger).error(EMAIL_SENDING_EXCEPTION_ERROR_MESSAGE, runtimeException);

        verify(logger, never()).info(anyString(), anyString(), anyString(), anyString(),
                anyString(), any(), anyString());
    }

    @Test
    public void testSendShouldNotSendEmailWhenEmailSendingIsDisabled()
            throws IOException, TemplateException {
        // given
        doReturn(logger).when(underTest).getLogger();
        when(underTest.isEmailSendingEnabled()).thenReturn(false);

        // when
        underTest.send(SENDER, RECIPIENT, SUBJECT, BODY_TEMPLATE_FILE_NAME,
                TEMPLATE_PARAMETERS, LOCALE);

        // then
        verify(logger).info(CALL_SEND_MESSAGE);

        verify(emailSender, never()).send(anyString(), anyString(), anyString(),
                anyString(), any(), any());
        verify(logger, never()).info(anyString(), anyString(), anyString(), anyString(),
                anyString(), any(), anyString());
        verify(logger, never()).error(anyString(), anyString(), anyString(), anyString(),
                anyString(), any(), anyString());
        verify(logger, never()).error(anyString(), any(Exception.class));
    }

    @Test
    public void testSendWithCustomTagsShouldNotThrowExceptionWhenEmailSenderDoesNotThrowExceptionAndEmailSendingIsEnabled() {
        // given
        doReturn(logger).when(underTest).getLogger();
        when(underTest.isEmailSendingEnabled()).thenReturn(true);

        // when
        underTest.sendWithCustomTags(SENDER, RECIPIENT, SUBJECT, BODY, TAGS);

        // then
        verify(logger).info(CALL_SEND_WITH_CUSTOM_TAGS_MESSAGE);
        verify(logger).info(EMAIL_NON_TEMPLATE_SENDING_SUCCESS_MESSAGE, SENDER, RECIPIENT,
                SUBJECT, TAGS);

        verify(logger, never()).error(anyString(), anyString(), anyString(), anyString(),
                any());
        verify(logger, never()).error(anyString(), any(Exception.class));
    }

    @Test
    public void testSendWithCustomTagsShouldNotThrowExceptionWhenEmailSenderThrowsExceptionAndEmailSendingIsEnabled()
            throws IOException, TemplateException {
        // given
        doReturn(logger).when(underTest).getLogger();
        when(underTest.isEmailSendingEnabled()).thenReturn(true);

        RuntimeException runtimeException = new RuntimeException();
        doThrow(runtimeException).when(emailSender).sendWithCustomTags(SENDER,
                RECIPIENT, SUBJECT, BODY, TAGS);

        // when
        underTest.sendWithCustomTags(SENDER, RECIPIENT, SUBJECT, BODY, TAGS);

        // then
        verify(logger).info(CALL_SEND_WITH_CUSTOM_TAGS_MESSAGE);
        verify(logger).error(EMAIL_NON_TEMPLATE_SENDING_ERROR_MESSAGE, SENDER, RECIPIENT,
                SUBJECT, TAGS);
        verify(logger).error(EMAIL_SENDING_EXCEPTION_ERROR_MESSAGE, runtimeException);
        verify(logger, never()).info(anyString(), anyString(), anyString(), anyString(),
                any());
    }

    @Test
    public void testSendWithCustomTagsShouldNotSendEmailWhenEmailSendingIsDisabled()
            throws IOException, TemplateException {
        // given
        doReturn(logger).when(underTest).getLogger();
        when(underTest.isEmailSendingEnabled()).thenReturn(false);

        // when
        underTest.sendWithCustomTags(SENDER, RECIPIENT, SUBJECT, BODY, TAGS);

        // then
        verify(logger).info(CALL_SEND_WITH_CUSTOM_TAGS_MESSAGE);
        verify(emailSender, never()).sendWithCustomTags(anyString(), anyString(),
                anyString(), anyString(), any());
        verify(logger, never()).info(anyString(), anyString(), anyString(), anyString(),
                any());
        verify(logger, never()).error(anyString(), anyString(), anyString(), anyString(),
                any());
        verify(logger, never()).error(anyString(), any(Exception.class));
    }

    @Test
    public void testSendWithAttachmentShouldNotThrowExceptionWhenEmailSenderDoesNotThrowExceptionAndEmailSendingIsEnabled() {
        // given
        doReturn(logger).when(underTest).getLogger();
        when(underTest.isEmailSendingEnabled()).thenReturn(true);

        // when
        underTest.sendWithAttachment(SENDER, RECIPIENT, SUBJECT, BODY_TEMPLATE_FILE_NAME,
                TEMPLATE_PARAMETERS, ATTACHMENT_PATH, LOCALE);

        // then
        verify(logger).info(CALL_SEND_WITH_ATTACHMENT);
        verify(logger).info(EMAIL_TEMPLATE_WITH_ATTACHMENT_SENDING_SUCCESS_MESSAGE,
                SENDER, RECIPIENT, SUBJECT, BODY_TEMPLATE_FILE_NAME, TEMPLATE_PARAMETERS,
                ATTACHMENT_PATH, LOCALE);

        verify(logger, never()).error(anyString(), anyString(), anyString(), anyString(),
                anyString(), any(), anyString(), any());
        verify(logger, never()).error(anyString(), any(Exception.class));
    }

    @Test
    public void testSendWithAttachmentShouldNotThrowExceptionWhenEmailSenderThrowsExceptionAndEmailSendingIsEnabled()
            throws IOException, TemplateException, MessagingException {
        // given
        doReturn(logger).when(underTest).getLogger();
        when(underTest.isEmailSendingEnabled()).thenReturn(true);

        RuntimeException runtimeException = new RuntimeException();
        doThrow(runtimeException).when(emailSender).sendWithAttachmentFile(SENDER,
                RECIPIENT, SUBJECT, BODY_TEMPLATE_FILE_NAME, TEMPLATE_PARAMETERS,
                ATTACHMENT_PATH, LOCALE);

        // when
        underTest.sendWithAttachment(SENDER, RECIPIENT, SUBJECT, BODY_TEMPLATE_FILE_NAME,
                TEMPLATE_PARAMETERS, ATTACHMENT_PATH, LOCALE);

        // then
        verify(logger).info(CALL_SEND_WITH_ATTACHMENT);
        verify(logger).error(EMAIL_TEMPLATE_WITH_ATTACHMENT_SENDING_ERROR_MESSAGE, SENDER,
                RECIPIENT, SUBJECT, BODY_TEMPLATE_FILE_NAME, TEMPLATE_PARAMETERS,
                ATTACHMENT_PATH, LOCALE);
        verify(logger).error(EMAIL_SENDING_EXCEPTION_ERROR_MESSAGE, runtimeException);

        verify(logger, never()).info(anyString(), anyString(), anyString(), anyString(),
                anyString(), any(), anyString(), any());
    }

    @Test
    public void testSendWithAttachmentShouldNotSendEmailWhenEmailSendingIsDisabled()
            throws IOException, TemplateException, MessagingException {
        // given
        doReturn(logger).when(underTest).getLogger();
        when(underTest.isEmailSendingEnabled()).thenReturn(false);

        // when
        underTest.sendWithAttachment(SENDER, RECIPIENT, SUBJECT, BODY_TEMPLATE_FILE_NAME,
                TEMPLATE_PARAMETERS, ATTACHMENT_PATH, LOCALE);

        // then
        verify(logger).info(CALL_SEND_WITH_ATTACHMENT);

        verify(emailSender, never()).sendWithAttachmentFile(anyString(), anyString(),
                anyString(), anyString(), any(), anyString(), any());
        verify(logger, never()).info(anyString(), anyString(), anyString(), anyString(),
                anyString(), any(), anyString(), any());
        verify(logger, never()).error(anyString(), anyString(), anyString(), anyString(),
                anyString(), any(), anyString(), any());
        verify(logger, never()).error(anyString(), any(Exception.class));
    }
}
