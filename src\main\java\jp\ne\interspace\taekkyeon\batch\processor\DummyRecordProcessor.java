/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.processor;

import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.record.Record;

/**
 * {@link RecordProcessor} implementation returning the incoming record without any
 * processing to be injected when a {@link RecordProcessor} is not necessary.
 *
 * <AUTHOR> Varga
 */
public class DummyRecordProcessor implements RecordProcessor<Record<?>, Record<?>> {

    @Override
    public Record<?> processRecord(Record<?> record) throws Exception {
        return record;
    }
}
