/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding the email types.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum EmailType implements ValueEnum {

    PAYMENT_GENERATION(1);

    private final int value;
}
