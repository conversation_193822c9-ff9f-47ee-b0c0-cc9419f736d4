/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.util.Map;
import java.util.Set;
import java.util.function.UnaryOperator;

import javax.inject.Singleton;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.ImmutableTable;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.name.Named;

import freemarker.template.Configuration;

import jp.ne.interspace.taekkyeon.model.PublisherAccountType;
import jp.ne.interspace.taekkyeon.persist.aws.dynamodb.DynamoDbNameResolver;

import static freemarker.template.Configuration.VERSION_2_3_23;
import static freemarker.template.TemplateExceptionHandler.RETHROW_HANDLER;
import static java.lang.Integer.parseInt;
import static java.lang.String.join;
import static java.lang.System.getProperty;
import static java.nio.charset.StandardCharsets.UTF_8;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.getPropertyBy;
import static jp.ne.interspace.taekkyeon.model.PublisherAccountType.INFLUENCER;
import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.MALAYSIA;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Country.VIETNAM;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.getSecretValues;

/**
 * Taekkyeon module for properties.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonPropertiesModule extends AbstractModule {

    public static final String BIND_KEY_NOREPLY_SENDER_EMAIL = "no-reply.sender.email";
    public static final String BIND_KEY_ISTOOLS_URL = "istools.url";
    public static final String BIND_KEY_FACEBOOK_URL = "facebook.url";
    public static final String BIND_KEY_TWITTER_URL = "twitter.url";
    public static final String BIND_KEY_GOOGLE_PLUS_URL = "google.plus.url";
    public static final String BIND_KEY_GLOBAL_PUBLISHER_SITE_IDS = "global.publisher.site.ids";
    public static final String EMAIL_SENDING_ENABLED_VM_ARGUMENT = "emailSendingEnabled";
    public static final String BIND_KEY_GURKHA_V2_API_AUTHORIZATION_KEY = "gurkha.v2.api.authorization.key";
    public static final String BIND_KEY_CAPTURED_SCREENSHOT = "is.captured.screenshot";
    public static final String BIND_KEY_CAPTURED_SCREENSHOT_ONLY_WHEN_SEARCHING = "is.captured.screenshot.only.when.searching";
    public static final String BIND_KEY_IS_BATCH_TRANSACTION = "is.batch.transaction";

    private static final String BATCH_NAME_VM_ARGUMENT = "batchName";
    private static final String NO_REPLY_SENDER_EMAIL_ADDRESS = "<EMAIL>";
    private static final String MAX_WAIT_FOR_LOG_PROCESSING_TIMES_VM_ARGUMENT = "maxWaitForLogProcessingTimes";
    public static final String IS_BATCH_TRANSACTION_VM_ARGUMENT = "isBatchTransaction";

    private static final String ID = INDONESIA.getCode();
    private static final String VN = VIETNAM.getCode();
    private static final String TH = THAILAND.getCode();
    private static final String MY = MALAYSIA.getCode();
    private static final String MAX_WAIT_FOR_LOG_PROCESSING_TIMES_DEFAULT = "20";
    private static final String GURKHA_V2_API_AUTHORIZATION_KEY_SECRET_NAME = "V2_API_AUTHORIZATION_KEY";

    private static final ImmutableMap<Country, String> ISTOOLS_URLS = new ImmutableMap.Builder<Country, String>()
            .put(THAILAND, "https://istools.accesstrade.in.th/")
            .put(VIETNAM, "https://istools.accesstrade.vn/")
            .put(INDONESIA, "https://istools.accesstrade.co.id/")
            .build();

    private static final ImmutableMap<Country, String> FACEBOOK_URLS = new ImmutableMap.Builder<Country, String>()
            .put(THAILAND, "").put(VIETNAM, "")
            .put(INDONESIA, "https://www.facebook.com/accesstradeID")
            .build();

    private static final ImmutableMap<Country, String> TWITTER_URLS = new ImmutableMap.Builder<Country, String>()
            .put(THAILAND, "").put(VIETNAM, "")
            .put(INDONESIA, "https://www.twitter.com/ACCESSTRADEID")
            .build();

    private static final ImmutableMap<Country, String> GOOGLE_PLUS_URLS = new ImmutableMap.Builder<Country, String>()
            .put(THAILAND, "").put(VIETNAM, "")
            .put(INDONESIA, "https://plus.google.com/u/0/+ACCESSTRADEINDONESIA/posts")
            .build();

    private static final ImmutableTable<Country, Environment, String> CONVERSION_UPDATE_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-conversion-update")
            .put(INDONESIA, STAGING, "indonesia-staging-conversion-update")
            .put(INDONESIA, PRODUCTION, "indonesia-production-conversion-update")
            .put(THAILAND, DEV, "thailand-dev-conversion-update")
            .put(THAILAND, STAGING, "thailand-staging-conversion-update")
            .put(THAILAND, PRODUCTION, "thailand-production-conversion-update")
            .put(VIETNAM, DEV, "vietnam-dev-conversion-update")
            .put(VIETNAM, STAGING, "vietnam-staging-conversion-update")
            .put(VIETNAM, PRODUCTION, "vietnam-production-conversion-update")
            .build();

    private static final ImmutableTable<Country, Environment, String> CREATIVE_ACCESS_LOG_UPDATE_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-creative-access-log-update")
            .put(INDONESIA, STAGING, "indonesia-staging-creative-access-log-update")
            .put(INDONESIA, PRODUCTION, "indonesia-production-creative-access-log-update")
            .put(THAILAND, DEV, "thailand-dev-creative-access-log-update")
            .put(THAILAND, STAGING, "thailand-staging-creative-access-log-update")
            .put(THAILAND, PRODUCTION, "thailand-production-creative-access-log-update")
            .put(VIETNAM, DEV, "vietnam-dev-creative-access-log-update")
            .put(VIETNAM, STAGING, "vietnam-staging-creative-access-log-update")
            .put(VIETNAM, PRODUCTION, "vietnam-production-creative-access-log-update")
            .build();

    private static final ImmutableTable<Country, Environment, String> CONVERSION_INSERTION_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-conversion-insertion")
            .put(INDONESIA, STAGING, "indonesia-staging-conversion-insertion")
            .put(INDONESIA, PRODUCTION, "indonesia-production-conversion-insertion")
            .put(THAILAND, DEV, "thailand-dev-conversion-insertion")
            .put(THAILAND, STAGING, "thailand-staging-conversion-insertion")
            .put(THAILAND, PRODUCTION, "thailand-production-conversion-insertion")
            .put(VIETNAM, DEV, "vietnam-dev-conversion-insertion")
            .put(VIETNAM, STAGING, "vietnam-staging-conversion-insertion")
            .put(VIETNAM, PRODUCTION, "vietnam-production-conversion-insertion")
            .build();

    private static final ImmutableTable<Country, Environment, String> EMAIL_REPORT_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-email-events")
            .put(INDONESIA, STAGING, "indonesia-staging-email-events")
            .put(INDONESIA, PRODUCTION, "indonesia-production-email-events")
            .put(THAILAND, DEV, "thailand-dev-email-events")
            .put(THAILAND, STAGING, "thailand-staging-email-events")
            .put(THAILAND, PRODUCTION, "thailand-production-email-events")
            .put(VIETNAM, DEV, "vietnam-dev-email-events")
            .put(VIETNAM, STAGING, "vietnam-staging-email-events")
            .put(VIETNAM, PRODUCTION, "vietnam-production-email-events").build();

    private static final ImmutableTable<Country, Environment, String> EMAIL_EVENTS_CONFIGURATION_SET_NAME = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-email-events")
            .put(INDONESIA, STAGING, "indonesia-staging-email-events")
            .put(INDONESIA, PRODUCTION, "indonesia-production-email-events")
            .put(THAILAND, DEV, "thailand-dev-email-events")
            .put(THAILAND, STAGING, "thailand-staging-email-events")
            .put(THAILAND, PRODUCTION, "thailand-production-email-events")
            .put(VIETNAM, DEV, "vietnam-dev-email-events")
            .put(VIETNAM, STAGING, "vietnam-staging-email-events")
            .put(VIETNAM, PRODUCTION, "vietnam-production-email-events").build();

    private static final ImmutableTable<Country, Environment, String> REPORT_EXPORT_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-report-export")
            .put(INDONESIA, STAGING, "indonesia-staging-report-export")
            .put(INDONESIA, PRODUCTION, "indonesia-production-report-export")
            .put(THAILAND, DEV, "thailand-dev-report-export")
            .put(THAILAND, STAGING, "thailand-staging-report-export")
            .put(THAILAND, PRODUCTION, "thailand-production-report-export")
            .put(VIETNAM, DEV, "vietnam-dev-report-export")
            .put(VIETNAM, STAGING, "vietnam-staging-report-export")
            .put(VIETNAM, PRODUCTION, "vietnam-production-report-export")
            .build();

    private static final ImmutableTable<Country, Environment, String> GOOGLE_KEYWORD_ANALYTICS_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-google-keyword-analytics")
            .put(INDONESIA, STAGING, "indonesia-staging-google-keyword-analytics")
            .put(INDONESIA, PRODUCTION, "indonesia-production-google-keyword-analytics")
            .put(THAILAND, DEV, "thailand-dev-google-keyword-analytics")
            .put(THAILAND, STAGING, "thailand-staging-google-keyword-analytics")
            .put(THAILAND, PRODUCTION, "thailand-production-google-keyword-analytics")
            .put(VIETNAM, DEV, "vietnam-dev-google-keyword-analytics")
            .put(VIETNAM, STAGING, "vietnam-staging-google-keyword-analytics")
            .put(VIETNAM, PRODUCTION, "vietnam-production-google-keyword-analytics")
            .build();

    private static final ImmutableMap<Environment, ImmutableMap<String, Long>> GLOBAL_PUBLISHER_SITE_IDS = ImmutableMap
            .of(DEV, ImmutableMap.of(ID, 14L, TH, 14L, VN, 14L, MY, 999L),
                STAGING, ImmutableMap.of(ID, 14L, TH, 14L, VN, 14L, MY, 999L),
                PRODUCTION, ImmutableMap.of(ID, 29310L, TH, 34544L, VN, 507448L, MY, 29312L));

    private static final ImmutableSet<PublisherAccountType> EMAIL_SENDING_DISABLED_PUBLISHER_TYPES = ImmutableSet.of(INFLUENCER);

    private static final ImmutableMap<Environment, String> MMDB_BUCKET_URL =
            new ImmutableMap.Builder<Environment, String>()
                    .put(DEV, "images.accesstrade.dev")
                    .put(STAGING, "images.accesstrade.dev")
                    .put(PRODUCTION, "accesstrade.ai.optimizer")
                    .build();

    private static final ImmutableTable<Country, Environment, String> CLICK_LOG_OPTIMIZER_LOADER_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-click-log-optimizer")
            .put(INDONESIA, STAGING, "indonesia-staging-click-log-optimizer")
            .put(INDONESIA, PRODUCTION, "indonesia-production-click-log-optimizer")
            .put(THAILAND, DEV, "thailand-dev-click-log-optimizer")
            .put(THAILAND, STAGING, "thailand-staging-click-log-optimizer")
            .put(THAILAND, PRODUCTION, "thailand-production-click-log-optimizer")
            .put(VIETNAM, DEV, "vietnam-dev-click-log-optimizer")
            .put(VIETNAM, STAGING, "vietnam-staging-click-log-optimizer")
            .put(VIETNAM, PRODUCTION, "vietnam-production-click-log-optimizer").build();

    private static final ImmutableTable<Country, Environment, String> ADS_SCRAPER_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-ads-scraper")
            .put(INDONESIA, STAGING, "indonesia-staging-ads-scraper")
            .put(INDONESIA, PRODUCTION, "indonesia-production-ads-scraper")
            .put(THAILAND, DEV, "thailand-dev-ads-scraper")
            .put(THAILAND, STAGING, "thailand-staging-ads-scraper")
            .put(THAILAND, PRODUCTION, "thailand-production-ads-scraper")
            .put(VIETNAM, DEV, "vietnam-dev-ads-scraper")
            .put(VIETNAM, STAGING, "vietnam-staging-ads-scraper")
            .put(VIETNAM, PRODUCTION, "vietnam-production-ads-scraper").build();

    private static final ImmutableTable<Country, Environment, String> SYSTEM_MONITORING_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-system-monitoring")
            .put(INDONESIA, STAGING, "indonesia-staging-system-monitoring")
            .put(INDONESIA, PRODUCTION, "indonesia-production-system-monitoring")
            .put(THAILAND, DEV, "thailand-dev-system-monitoring")
            .put(THAILAND, STAGING, "thailand-staging-system-monitoring")
            .put(THAILAND, PRODUCTION, "thailand-production-system-monitoring")
            .put(VIETNAM, DEV, "vietnam-dev-system-monitoring")
            .put(VIETNAM, STAGING, "vietnam-staging-system-monitoring")
            .put(VIETNAM, PRODUCTION, "vietnam-production-system-monitoring").build();

    private static final ImmutableTable<Country, Environment, String> SITE_AFFILIATED_CAMPAIGN_IDS_SYNCHRONIZE_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV,
                    "indonesia-dev-site-affiliated-campaign-ids-dynamodb-synchronize")
            .put(INDONESIA, STAGING,
                    "indonesia-staging-site-affiliated-campaign-ids-dynamodb-synchronize")
            .put(INDONESIA, PRODUCTION,
                    "indonesia-production-site-affiliated-campaign-ids-dynamodb-synchronize")
            .put(THAILAND, DEV,
                    "thailand-dev-site-affiliated-campaign-ids-dynamodb-synchronize")
            .put(THAILAND, STAGING,
                    "thailand-staging-site-affiliated-campaign-ids-dynamodb-synchronize")
            .put(THAILAND, PRODUCTION,
                    "thailand-production-site-affiliated-campaign-ids-dynamodb-synchronize")
            .put(VIETNAM, DEV,
                    "vietnam-dev-site-affiliated-campaign-ids-dynamodb-synchronize")
            .put(VIETNAM, STAGING,
                    "vietnam-staging-site-affiliated-campaign-ids-dynamodb-synchronize")
            .put(VIETNAM, PRODUCTION,
                    "vietnam-production-site-affiliated-campaign-ids-dynamodb-synchronize")
            .build();

    private static final ImmutableTable<Country, Environment, String> CAMPAIGN_CLOSURE_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-campaign-closure")
            .put(INDONESIA, STAGING, "indonesia-staging-campaign-closure")
            .put(INDONESIA, PRODUCTION, "indonesia-production-campaign-closure")
            .put(THAILAND, DEV, "thailand-dev-campaign-closure")
            .put(THAILAND, STAGING, "thailand-staging-campaign-closure")
            .put(THAILAND, PRODUCTION, "thailand-production-campaign-closure")
            .put(VIETNAM, DEV, "vietnam-dev-campaign-closure")
            .put(VIETNAM, STAGING, "vietnam-staging-campaign-closure")
            .put(VIETNAM, PRODUCTION, "vietnam-production-campaign-closure").build();

    private static final ImmutableTable<Country, Environment, String> TRACKING_DATA_FORECAST_PROVIDER_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-tracking-data-forecast-provider")
            .put(INDONESIA, STAGING, "indonesia-staging-tracking-data-forecast-provider")
            .put(INDONESIA, PRODUCTION,
                    "indonesia-production-tracking-data-forecast-provider")
            .put(THAILAND, DEV, "thailand-dev-tracking-data-forecast-provider")
            .put(THAILAND, STAGING, "thailand-staging-tracking-data-forecast-provider")
            .put(THAILAND, PRODUCTION,
                    "thailand-production-tracking-data-forecast-provider")
            .put(VIETNAM, DEV, "vietnam-dev-tracking-data-forecast-provider")
            .put(VIETNAM, STAGING, "vietnam-staging-tracking-data-forecast-provider")
            .put(VIETNAM, PRODUCTION,
                    "vietnam-production-tracking-data-forecast-provider")
            .build();

    private static final ImmutableTable<Country, Environment, String> PUBLISHER_TAX_REFUND_PROVIDER_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-publisher-tax-refund")
            .put(INDONESIA, STAGING, "indonesia-staging-publisher-tax-refund")
            .put(INDONESIA, PRODUCTION, "indonesia-production-publisher-tax-refund")
            .put(THAILAND, DEV, "thailand-dev-publisher-tax-refund")
            .put(THAILAND, STAGING, "thailand-staging-publisher-tax-refund")
            .put(THAILAND, PRODUCTION, "thailand-production-publisher-tax-refund")
            .put(VIETNAM, DEV, "vietnam-dev-publisher-tax-refund")
            .put(VIETNAM, STAGING, "vietnam-staging-publisher-tax-refund")
            .put(VIETNAM, PRODUCTION, "vietnam-production-publisher-tax-refund")
            .build();

    private static final ImmutableTable<Country, Environment, String> CONVERSION_RANK_AUTO_UPDATE_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-conversion-rank-auto-update")
            .put(INDONESIA, STAGING, "indonesia-staging-conversion-rank-auto-update")
            .put(INDONESIA, PRODUCTION, "indonesia-production-conversion-rank-auto-update")
            .put(THAILAND, DEV, "thailand-dev-conversion-rank-auto-update")
            .put(THAILAND, STAGING, "thailand-staging-conversion-rank-auto-update")
            .put(THAILAND, PRODUCTION, "thailand-production-conversion-rank-auto-update")
            .put(VIETNAM, DEV, "vietnam-dev-conversion-rank-auto-update")
            .put(VIETNAM, STAGING, "vietnam-staging-conversion-rank-auto-update")
            .put(VIETNAM, PRODUCTION, "vietnam-production-conversion-rank-auto-update").build();

    private static final ImmutableMap<Environment, String> UPSERTED_CONVERSIONS_QUEUE_NAMES = new ImmutableMap.Builder<Environment, String>()
            .put(DEV, "upserted-conversions-dev-queue")
            .put(STAGING, "upserted-conversions-staging-queue")
            .put(PRODUCTION, "PROD_JP_CONVERSION_QUEUE")
            .build();

    private static final ImmutableMap<Environment, String> UPSERTED_CONVERSIONS_VIP_QUEUE_NAMES = new ImmutableMap.Builder<Environment, String>()
            .put(DEV, "upserted-conversions-dev-vip-queue")
            .put(STAGING, "upserted-conversions-staging-vip-queue")
            .put(PRODUCTION, "PROD_JP_CONVERSION_VIP_QUEUE").build();

    private static final ImmutableMap<Environment, String> NEWLY_CREATED_CONVERSIONS_QUEUE_NAMES = new ImmutableMap.Builder<Environment, String>()
            .put(DEV, "newly-created-conversions-dev-queue")
            .put(STAGING, "newly-created-conversions-staging-queue")
            .put(PRODUCTION, "PROD_JP_CONVERSION_INS_QUEUE")
            .build();

    private static final ImmutableMap<Environment, String> NEWLY_CREATED_CONVERSIONS_VIP_QUEUE_NAMES = new ImmutableMap.Builder<Environment, String>()
            .put(DEV, "newly-created-conversions-dev-vip-queue")
            .put(STAGING, "newly-created-conversions-staging-vip-queue")
            .put(PRODUCTION, "PROD_JP_CONVERSION_INS_VIP_QUEUE").build();

    private static final ImmutableTable<Country, Environment, String> CONVERSION_CITY_DETECTION_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-conversion-city-detection")
            .put(INDONESIA, STAGING, "indonesia-staging-conversion-city-detection")
            .put(INDONESIA, PRODUCTION, "indonesia-production-conversion-city-detection")
            .put(THAILAND, DEV, "thailand-dev-conversion-city-detection")
            .put(THAILAND, STAGING, "thailand-staging-conversion-city-detection")
            .put(THAILAND, PRODUCTION, "thailand-production-conversion-city-detection")
            .put(VIETNAM, DEV, "vietnam-dev-conversion-city-detection")
            .put(VIETNAM, STAGING, "vietnam-staging-conversion-city-detection")
            .put(VIETNAM, PRODUCTION, "vietnam-production-conversion-city-detection")
            .build();

    private static final ImmutableTable<Country, Environment, String> PAYMENT_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-payment-data-notifier")
            .put(INDONESIA, STAGING, "indonesia-staging-payment-data-notifier")
            .put(INDONESIA, PRODUCTION, "indonesia-production-payment-data-notifier")
            .put(THAILAND, DEV, "thailand-dev-payment-data-notifier")
            .put(THAILAND, STAGING, "thailand-staging-payment-data-notifier")
            .put(THAILAND, PRODUCTION, "thailand-production-payment-data-notifier")
            .put(VIETNAM, DEV, "vietnam-dev-payment-data-notifier")
            .put(VIETNAM, STAGING, "DEV_JP_PAYMENT_QUEUE")
            .put(VIETNAM, PRODUCTION, "PROD_JP_PAYMENT_QUEUE")
            .build();

    private static final ImmutableTable<Country, Environment, String> PUBLISHER_TRAFFIC_SOURCE_DATA_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-publisher-traffic-source-data")
            .put(INDONESIA, STAGING, "indonesia-staging-publisher-traffic-source-data")
            .put(INDONESIA, PRODUCTION,
                    "indonesia-production-publisher-traffic-source-data")
            .put(THAILAND, DEV, "thailand-dev-publisher-traffic-source-data")
            .put(THAILAND, STAGING, "thailand-staging-publisher-traffic-source-data")
            .put(THAILAND, PRODUCTION,
                    "thailand-production-publisher-traffic-source-data")
            .put(VIETNAM, DEV, "vietnam-dev-publisher-traffic-source-data")
            .put(VIETNAM, STAGING, "vietnam-staging-publisher-traffic-source-data")
            .put(VIETNAM, PRODUCTION, "vietnam-production-publisher-traffic-source-data")
            .build();

    private static final ImmutableTable<Country, Environment, String> POSTBACK_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-postback-urls")
            .put(INDONESIA, STAGING, "indonesia-staging-postback-urls")
            .put(INDONESIA, PRODUCTION, "indonesia-production-postback-urls")
            .put(VIETNAM, DEV, "vietnam-dev-postback-urls")
            .put(VIETNAM, STAGING, "vietnam-staging-postback-urls")
            .put(VIETNAM, PRODUCTION, "vietnam-production-postback-urls")
            .put(THAILAND, DEV, "thailand-dev-postback-urls")
            .put(THAILAND, STAGING, "thailand-staging-postback-urls")
            .put(THAILAND, PRODUCTION, "thailand-production-postback-urls").build();

    private static final ImmutableTable<Country, Environment, String> PUBLISHER_FUNNEL_TREND_QUEUE_NAMES = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "indonesia-dev-publisher-funnel-active-siteIds")
            .put(INDONESIA, STAGING, "indonesia-staging-publisher-funnel-active-siteIds")
            .put(INDONESIA, PRODUCTION, "indonesia-production-publisher-funnel-active-siteIds")
            .put(VIETNAM, DEV, "vietnam-dev-publisher-funnel-active-siteIds")
            .put(VIETNAM, STAGING, "vietnam-staging-publisher-funnel-active-siteIds")
            .put(VIETNAM, PRODUCTION, "vietnam-production-publisher-funnel-active-siteIds")
            .put(THAILAND, DEV, "thailand-dev-publisher-funnel-active-siteIds")
            .put(THAILAND, STAGING, "thailand-staging-publisher-funnel-active-siteIds")
            .put(THAILAND, PRODUCTION, "thailand-production-publisher-funnel-active-siteIds")
            .build();

    @Override
    protected void configure() {
        eagerLoadSingletonInstances();
    }

    private void eagerLoadSingletonInstances() {
        bind(Configuration.class).toInstance(getFreeMarkerConfiguration());
    }

    private Configuration getFreeMarkerConfiguration() {
        Configuration configuration = new Configuration(VERSION_2_3_23);
        configuration.setClassForTemplateLoading(this.getClass(), "/templates/emails");
        configuration.setDefaultEncoding(UTF_8.name());
        configuration.setTemplateExceptionHandler(RETHROW_HANDLER);
        return configuration;
    }

    @Provides @Singleton @BatchNameBinding
    private String provideBatchName() {
        return getPropertyBy(BATCH_NAME_VM_ARGUMENT);
    }

    @Provides @Singleton @DynamoDbNameResolver
    private UnaryOperator<String> provideTableNameResolver() {
        String concatenator = "_";
        String countryCode = getCurrentCountry().getCode();
        return partialName -> {
            switch (getCurrentEnvironment()) {
                case PRODUCTION:
                    return join(concatenator, countryCode, partialName);
                case STAGING:
                    return join(concatenator, STAGING.name(), countryCode, partialName);
                case DEV:
                // let-through to default
                default:
                    return join(concatenator, DEV.name(), partialName);
            }
        };
    }

    @Provides @Singleton @Named(BIND_KEY_NOREPLY_SENDER_EMAIL)
    private String provideNoReplySenderGlobalEmail() {
        return NO_REPLY_SENDER_EMAIL_ADDRESS;
    }

    @Provides @Singleton @Named(BIND_KEY_ISTOOLS_URL)
    private String provideIstoolsUrl() {
        return ISTOOLS_URLS.get(getCurrentCountry());
    }

    @Provides @Singleton @Named(BIND_KEY_FACEBOOK_URL)
    private String provideFacebookUrl() {
        return FACEBOOK_URLS.get(getCurrentCountry());
    }

    @Provides @Singleton @Named(BIND_KEY_TWITTER_URL)
    private String provideTwitterUrl() {
        return TWITTER_URLS.get(getCurrentCountry());
    }

    @Provides @Singleton @Named(BIND_KEY_GOOGLE_PLUS_URL)
    private String provideGooglePlusUrl() {
        return GOOGLE_PLUS_URLS.get(getCurrentCountry());
    }

    @Provides @Singleton @ConversionUpdateQueueNameResolver
    private String provideConversionUpdateQueueName() {
        return CONVERSION_UPDATE_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @CreativeAccessLogUpdateQueueNameResolver
    private String provideCreativeAccessLogUpdateQueueName() {
        return CREATIVE_ACCESS_LOG_UPDATE_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @ConversionInsertionQueueNameResolver
    private String provideConversionInsertionQueueName() {
        return CONVERSION_INSERTION_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @EmailReportQueueNameResolver
    private String provideEmailReportQueueName() {
        return EMAIL_REPORT_QUEUE_NAMES.get(getCurrentCountry(), getCurrentEnvironment());
    }

    @Provides @Singleton @EmailSendingEnabledResolver
    private boolean provideEmailSendingEnabled() {
        if (getCurrentEnvironment() == PRODUCTION) {
            return true;
        }
        return Boolean.valueOf(getPropertyBy(EMAIL_SENDING_ENABLED_VM_ARGUMENT));
    }

    @Provides @Singleton @EmailEventConfigurationSetNameResolver
    private String provideEventConfigurationSetName() {
        return EMAIL_EVENTS_CONFIGURATION_SET_NAME.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @ReportExportQueueNameResolver
    private String provideReportExportQueueName() {
        return REPORT_EXPORT_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @GoogleKeywordAnalyticsQueueNameResolver
    private String provideGoogleKeywordAnalyticQueueName() {
        return GOOGLE_KEYWORD_ANALYTICS_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @EmailSendingDisabledPublisherTypesResolver
    private Set<PublisherAccountType> provideEmailSendingDisabledPublisherTypes() {
        return EMAIL_SENDING_DISABLED_PUBLISHER_TYPES;
    }

    @Provides @Singleton @Named(BIND_KEY_GLOBAL_PUBLISHER_SITE_IDS)
    private Map<String, Long> provideGlobalPublisherSiteIds() {
        return GLOBAL_PUBLISHER_SITE_IDS.get(getCurrentEnvironment());
    }

    @Provides @Singleton @MmdbBucketNameResolver
    private String provideMmdbBucketUrl() {
        return MMDB_BUCKET_URL.get(getCurrentEnvironment());
    }

    @Provides @Singleton @MaxWaitForLogProcessingTimesBinding
    private int provideMaxWaitForLogProcessingTimes() {
        return parseInt(getProperty(MAX_WAIT_FOR_LOG_PROCESSING_TIMES_VM_ARGUMENT,
                MAX_WAIT_FOR_LOG_PROCESSING_TIMES_DEFAULT));
    }

    @Provides @Singleton @ClickLogOptimizerLoaderQueueNameResolver
    private String provideClickLogOptimizerLoaderQueueName() {
        return CLICK_LOG_OPTIMIZER_LOADER_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @AdsScraperQueueNameResolver
    private String provideAdsScraperQueueName() {
        return ADS_SCRAPER_QUEUE_NAMES.get(getCurrentCountry(), getCurrentEnvironment());
    }

    @Provides @Singleton @CampaignClosureNameResolver
    private String provideCampaignClosureQueueName() {
        return CAMPAIGN_CLOSURE_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @SystemMonitoringQueueNameResolver
    private String provideSystemMonitoringQueueName() {
        return SYSTEM_MONITORING_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @SiteAffiliatedCampaignIdsSynchronizeQueueNameResolver
    private String provideSiteAffiliatedCampaignIdsSynchronizeQueueName() {
        return SITE_AFFILIATED_CAMPAIGN_IDS_SYNCHRONIZE_QUEUE_NAMES
                .get(getCurrentCountry(), getCurrentEnvironment());
    }

    @Provides @Singleton @TrackingDataForecastProviderQueueNameResolver
    private String provideTrackingDataForecastProviderQueueName() {
        return TRACKING_DATA_FORECAST_PROVIDER_QUEUE_NAMES
                .get(getCurrentCountry(), getCurrentEnvironment());
    }

    @Provides @Singleton @UpsertedConversionsSenderQueueNameResolver
    private String provideUpsertedConversionsSenderQueueName() {
        return UPSERTED_CONVERSIONS_QUEUE_NAMES.get(getCurrentEnvironment());
    }

    @Provides @Singleton @UpsertedConversionsVipSenderQueueNameResolver
    private String provideUpsertedConversionsVipSenderQueueName() {
        return UPSERTED_CONVERSIONS_VIP_QUEUE_NAMES.get(getCurrentEnvironment());
    }

    @Provides @Singleton @NewlyCreatedConversionsSenderQueueNameResolver
    private String provideNewlyCreatedConversionsSenderQueueName() {
        return NEWLY_CREATED_CONVERSIONS_QUEUE_NAMES.get(getCurrentEnvironment());
    }

    @Provides @Singleton @NewlyCreatedConversionsVipSenderQueueNameResolver
    private String provideNewlyCreatedConversionsVipSenderQueueName() {
        return NEWLY_CREATED_CONVERSIONS_VIP_QUEUE_NAMES.get(getCurrentEnvironment());
    }

    @Provides @Singleton @PublisherTaxRefundQueueNameResolver
    private String providePublisherTaxRefundProviderQueueName() {
        return PUBLISHER_TAX_REFUND_PROVIDER_QUEUE_NAMES
                .get(getCurrentCountry(), getCurrentEnvironment());
    }

    @Provides @Singleton @ConversionCityDetectionQueueNameResolver
    private String provideConversionCityDetectionQueueName() {
        return CONVERSION_CITY_DETECTION_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @ConversionRankAutoUpdateQueueNameResolver
    private String provideConversionRankAutoUpdateQueueName() {
        return CONVERSION_RANK_AUTO_UPDATE_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @Named(BIND_KEY_GURKHA_V2_API_AUTHORIZATION_KEY)
    private String provide() {
        return getSecretValues(getCurrentCountry(), getCurrentEnvironment(),
                GURKHA_V2_API_AUTHORIZATION_KEY_SECRET_NAME);
    }

    @Provides @Singleton @CampaignClosurePublisherPaymentNotifierQueueNameResolver
    private String providePaymentQueueName() {
        return System.getProperty("sqsName",
                PAYMENT_QUEUE_NAMES.get(getCurrentCountry(), getCurrentEnvironment()));
    }

    @Provides @Singleton @Named(BIND_KEY_IS_BATCH_TRANSACTION)
    private boolean provideIsBatchTransaction() {
        return Boolean.parseBoolean(
                System.getProperty(IS_BATCH_TRANSACTION_VM_ARGUMENT, "true"));
    }

    @Provides @Singleton @PublisherTrafficSourceDataQueueNameResolver
    private String providePublisherTrafficSourceDataQueueName() {
        return PUBLISHER_TRAFFIC_SOURCE_DATA_QUEUE_NAMES.get(getCurrentCountry(),
                getCurrentEnvironment());
    }

    @Provides @Singleton @PostbackQueueNameResolver
    private String postbackQueueName() {
        return System.getProperty("sqsName",
                POSTBACK_QUEUE_NAMES.get(getCurrentCountry(), getCurrentEnvironment()));
    }

    @Provides @Singleton @PublisherFunnelTrendQueueNameResolver
    private String providePublisherFunnelTrendQueueName() {
        return System.getProperty("sqsName", PUBLISHER_FUNNEL_TREND_QUEUE_NAMES.get(
                        getCurrentCountry(), getCurrentEnvironment()));
    }

}
