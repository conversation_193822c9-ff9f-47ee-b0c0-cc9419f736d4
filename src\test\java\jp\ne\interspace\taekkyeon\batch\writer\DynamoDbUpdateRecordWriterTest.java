/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;

import org.easybatch.core.record.Batch;
import org.easybatch.core.record.Header;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.DynamoDbUpdateRecord;
import jp.ne.interspace.taekkyeon.service.DynamoDbSyncService;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

/**
 * Unit test for {@link DynamoDbUpdateRecordWriter}.
 *
 * <AUTHOR> Varga
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class DynamoDbUpdateRecordWriterTest {

    @InjectMocks @Spy
    private DynamoDbUpdateRecordWriter underTest;

    @Mock
    private DynamoDbSyncService dynamoDbSyncService;

    @Test
    public void testWriteRecordsShouldCalledMethodsWhenCalled() throws Exception {
        // given
        String key1 = "key1";
        String key2 = "key2";
        Map<String, AttributeValue> attributeValue1 = Collections.emptyMap();
        Map<String, AttributeValue> attributeValue2 = Collections.emptyMap();
        Map<String, Map<String, AttributeValue>> payload = new HashMap<>();
        payload.put(key1, attributeValue1);
        payload.put(key2, attributeValue2);
        DynamoDbUpdateRecord record = new DynamoDbUpdateRecord(mock(Header.class),
                payload);
        Batch batch = new Batch(record);

        // when
        underTest.writeRecords(batch);

        // then
        verify(dynamoDbSyncService).waitUntilAvailable();
        verify(dynamoDbSyncService).syncStatus(payload);
    }
}
