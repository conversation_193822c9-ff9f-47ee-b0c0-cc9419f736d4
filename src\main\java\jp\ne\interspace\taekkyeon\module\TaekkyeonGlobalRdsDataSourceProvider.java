/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.io.PrintWriter;
import java.util.Properties;

import javax.sql.DataSource;

import com.google.inject.Provider;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import lombok.AllArgsConstructor;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.JDBC_PASSWORD_SECRET_KEY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.JDBC_URL_SECRET_KEY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.JDBC_USER_NAME_SECRET_KEY;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonConfig.getSecretValues;

/**
 * {@link DataSource} provider of ACCESSTRADE RDS.
 *
 * <p>
 * See more at <a href="https://github.com/brettwooldridge/HikariCP">
 * https://github.com/brettwooldridge/HikariCP</a>.
 * </p>
 *
 * <AUTHOR> OBS DEV Team
 */
@AllArgsConstructor
class TaekkyeonGlobalRdsDataSourceProvider implements Provider<DataSource> {

    private static final String URL = "dataSource.url";
    private static final String USER = "dataSource.user";
    private static final String PASSWORD = "dataSource.password";
    private static final String CLASS_NAME = "dataSourceClassName";
    private static final String LOG_WRITER = "dataSource.logWriter";

    private static final boolean DO_AUTO_COMMIT = false;
    private static final int MAX_POOL_SIZE = 70;
    private static final long CONN_TIMEOUT_IN_MILLIS = 10000;
    private static final long MAX_LIFETIME_IN_MILLIS = 400000;
    private static final int MINIMUM_IDLE = 10;
    private static final long IDLE_TIMEOUT_IN_MILLIS = 0;
    private static final String DATASOURCE_CLASS = "oracle.jdbc.pool.OracleDataSource";

    private final Country country;
    private final Environment environment;

    @Override
    public DataSource get() {
        return createDataSourceFrom(getPropertiesOf(country, environment));
    }

    private Properties getPropertiesOf(Country country, Environment environment) {
        Properties properties = new Properties();
        properties.setProperty(URL,
                getSecretValues(country, environment, JDBC_URL_SECRET_KEY));
        properties.setProperty(USER,
                getSecretValues(country, environment, JDBC_USER_NAME_SECRET_KEY));
        properties.setProperty(PASSWORD,
                getSecretValues(country, environment, JDBC_PASSWORD_SECRET_KEY));
        properties.setProperty(CLASS_NAME, DATASOURCE_CLASS);
        properties.put(LOG_WRITER, new PrintWriter(System.out));
        return properties;
    }

    private HikariDataSource createDataSourceFrom(Properties properties) {
        HikariConfig configuration = new HikariConfig(properties);
        configuration.setAutoCommit(DO_AUTO_COMMIT);
        configuration.setMaximumPoolSize(MAX_POOL_SIZE);
        configuration.setConnectionTimeout(CONN_TIMEOUT_IN_MILLIS);
        configuration.setMaxLifetime(MAX_LIFETIME_IN_MILLIS);
        configuration.setMinimumIdle(MINIMUM_IDLE);
        configuration.setIdleTimeout(IDLE_TIMEOUT_IN_MILLIS);
        return new HikariDataSource(configuration);
    }
}
