/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import javax.inject.Singleton;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;

/**
 * Guice module for the VM arguments of the Rds multiple insert.
 *
 * <AUTHOR>
 */
public class RdsMultipleInsertModule extends AbstractModule {

    private static final String RDS_MULTIPLE_INSERT_MODE_VM_ARGUMENT = "rdsMultipleInsertMode";

    @Override
    protected void configure() {
    }

    @Provides @Singleton @RdsMultipleInsertModeResolver
    private boolean provideRdsMultipleInsertMode() {
        return Boolean.getBoolean(RDS_MULTIPLE_INSERT_MODE_VM_ARGUMENT);
    }
}

