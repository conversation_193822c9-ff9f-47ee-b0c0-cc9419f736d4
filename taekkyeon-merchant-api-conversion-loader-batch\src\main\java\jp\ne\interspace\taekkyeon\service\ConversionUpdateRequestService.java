/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;

import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceClient;

import static jp.ne.interspace.taekkyeon.module.MerchantApiConversionLoaderModule.BIND_KEY_CONVERSION_UPDATE_REQUESTS_S3_BUCKET;
import static lombok.AccessLevel.PACKAGE;

/**
 * Service layer for handling conversion update request.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class ConversionUpdateRequestService {

    private static final String FILE_NAME_FORMAT = "%s-%s.json";
    private static final DateTimeFormatter FILE_NAME_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss-SSS");

    @Inject
    private SimpleStorageServiceClient s3Client;

    @Inject @Named(BIND_KEY_CONVERSION_UPDATE_REQUESTS_S3_BUCKET) @Getter(PACKAGE)
    private String conversionUpdateRequestsBucketName;

    /**
     * Uploades data to conversion update request s3 bucket.
     *
     * @param fileNamePrefix
     *          prefix of file name
     * @param message
     *          file contents
     * @return file name
     */
    public String upload(String fileNamePrefix, String message) {
        String fileName = String.format(FILE_NAME_FORMAT, fileNamePrefix,
                getCurrentTimeString());
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        return fileName;
    }

    @VisibleForTesting
    String getCurrentTimeString() {
        return LocalDateTime.now().format(FILE_NAME_DATE_FORMAT);
    }
}
