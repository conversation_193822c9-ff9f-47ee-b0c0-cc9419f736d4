log4j.rootLogger=INFO, stdout, cloudWatchLogs
log4j.logger.jp.ne.interspace.taekkyeon=DEBUG
log4j.logger.org.easybatch.core.job=DEBUG

# Direct log messages to stdout
log4j.appender.stdout.Threshold=INFO
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.EnhancedPatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss:SSS Z} %-5p [%t] %c{1}:%L - %m%n%throwable%n
