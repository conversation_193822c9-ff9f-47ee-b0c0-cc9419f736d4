/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding publisher agency commission policies.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum PublisherAgencyCommissionPolicy implements ValueEnum {

    DONT_PAY(0),
    FULL_AT_COMMISSION(1),
    HALF_PUBLISHER_HALF_AT_COMMISSION(2);

    private final int value;
}
