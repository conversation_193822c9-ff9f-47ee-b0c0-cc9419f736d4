/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding creative types.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum CreativeType implements ValueEnum {

    IMAGE(1),
    TEXT(2),
    CUSTOM(3),
    PRODUCT_FEED(4),
    NEWSLETTER(5),
    SEO_CONTENT(6),
    FLASH(7),
    WIDGET(9),
    EMAIL(10),
    QUICK_LINK(11),
    SMART_LINK_TAG(12);

    private final int value;
}
