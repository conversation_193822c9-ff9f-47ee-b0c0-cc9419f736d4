/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding the data of synchronization data.
 *
 * <AUTHOR> Shin
 */
@AllArgsConstructor @Getter @EqualsAndHashCode
public class SynchronizationData {

    private final LocalDateTime syncStartTime;
    private Long conversionId;
    private Long siteId;
}
