/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.TrackingType;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler.ValueEnumTypeHandler;

/**
 * MyBatis {@link TypeHandler} for {@link TrackingType}.
 *
 * <AUTHOR>
 */
@MappedTypes(TrackingType.class)
public class TrakingTypeTypeHandler extends ValueEnumTypeHandler<TrackingType> {

    private static final TrackingType DEFAULT_TRACKING_TYPE = TrackingType.COOKIE;

    public TrakingTypeTypeHandler() {
        super(TrackingType.class, DEFAULT_TRACKING_TYPE);
    }

    /**
     * Returns the system default {@link TrackingType}.
     *
     * @return system default {@link TrackingType}
     */
    public static TrackingType getDefaultTrackingType() {
        return DEFAULT_TRACKING_TYPE;
    }
}
