/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.YearMonth;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.CurrencyRate;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybatis mapper for currency.
 *
 * <AUTHOR> Van
 */
public interface CurrencyMapper {

    /**
         SELECT
            ch.currency,
            NVL(ch.rate, 1) rate
         FROM
            merchant_campaign mc
         INNER JOIN
            merchant_account ma
         ON
            mc.account_no = ma.account_no
         INNER JOIN
            country c
         ON
            ma.country_code = c.code
         LEFT JOIN
            currency_exchange_rate_history ch
         ON
            c.currency = ch.quote_currency
         AND
            ch.currency = #{currency}
         AND
            target_month = #{conversionMonth, jdbcType=DATE,
            typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.TimestampYearMonthTypeHandler}
         WHERE
            mc.campaign_no = #{campaignId}
     */
    @Multiline String SELECT_CURRENCY_RATE_BY_CURRENCY = "";

    /**
         SELECT
            mc.currency,
            NVL(ch.rate, 1) rate
         FROM
            merchant_campaign mc
         INNER JOIN
            merchant_account ma
         ON
            mc.account_no = ma.account_no
         INNER JOIN
            country c
         ON
            ma.country_code = c.code
         LEFT JOIN
            currency_exchange_rate_history ch
         ON
            c.currency = ch.quote_currency
         AND
            ch.target_month = #{conversionMonth, jdbcType=DATE,
            typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.TimestampYearMonthTypeHandler}
         AND
            ch.currency = mc.currency
         WHERE
            mc.campaign_no = #{campaignId}
     */
    @Multiline String SELECT_CURRENCY_RATE = "";

    /**
     * Returns the {@link CurrencyRate} by the given parameters.
     *
     * @param currency
     *            the given currency
     * @param campaignId
     *            the identifiers of the given campaign
     * @param conversionMonth
     *            month of conversion
     * @return the {@link CurrencyRate} by the given parameters
     * @see #SELECT_CURRENCY_RATE_BY_CURRENCY
     */
    @Select(SELECT_CURRENCY_RATE_BY_CURRENCY)
    @ConstructorArgs({ @Arg(column = "currency", javaType = String.class),
            @Arg(column = "rate", javaType = BigDecimal.class) })
    CurrencyRate findCurrencyRate(@Param("currency") String currency,
            @Param("campaignId") long campaignId,
            @Param("conversionMonth") YearMonth conversionMonth);

    /**
     * Returns the {@link CurrencyRate} by the given parameters.
     *
     * @param campaignId
     *          the identifiers of the given campaign
     * @param conversionMonth
     *          month of conversion
     * @return the {@link CurrencyRate} by the given parameters
     * @see #SELECT_CURRENCY_RATE
     */
    @Select(SELECT_CURRENCY_RATE)
    @ConstructorArgs({ @Arg(column = "currency", javaType = String.class),
            @Arg(column = "rate", javaType = BigDecimal.class) })
    CurrencyRate findCampaignCurrencyRate(
            @Param("campaignId") long campaignId,
            @Param("conversionMonth") YearMonth conversionMonth);
}
