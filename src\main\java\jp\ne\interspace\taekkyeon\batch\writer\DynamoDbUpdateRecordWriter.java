/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import java.util.Map;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.google.inject.Inject;

import org.easybatch.core.record.Batch;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.service.DynamoDbSyncService;

/**
 * {@link RecordWriter} implementation for syncing data between {@code Oracle} and
 * {@code DynamoDB}.
 *
 * <AUTHOR> Varga
 */
public class DynamoDbUpdateRecordWriter implements RecordWriter {

    @Inject
    private DynamoDbSyncService dynamoDbSyncService;

    @Override
    public void open() throws Exception {
        // do nothing
    }

    @Override
    public void writeRecords(Batch batch) throws Exception {
        sync(batch);
    }

    @Override
    public void close() throws Exception {
        // do nothing
    }

    @SuppressWarnings("unchecked")
    private void sync(Batch batch) {
        dynamoDbSyncService.waitUntilAvailable();
        for (Record<Map<String, Map<String, AttributeValue>>> record : batch) {
            dynamoDbSyncService.syncStatus(record.getPayload());
        }
    }
}
