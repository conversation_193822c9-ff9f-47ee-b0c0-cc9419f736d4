/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding validation status.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum ValidationStatus implements ValueEnum {

    MATCHED(1),
    INSERTED(2),
    CANNOT_INSERTED(3),
    UPDATED(4),
    ERROR(5);

    private final int value;
}
