/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding all the various campaign statuses.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum CampaignStatus implements ValueEnum {

    GETTING_READY(0),
    RUNNING(1),
    PAUSED(3),
    TERMINATED(2),
    WONT_RUN(5),
    OTHER(4);

    private final int value;
}
