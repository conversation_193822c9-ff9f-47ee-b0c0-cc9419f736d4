/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.stream.Collectors;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.SiteLeadGenerationInternal;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.COMMA;

/**
 * MyBatis {@link TypeHandler} for {@link SiteLeadGenerationInternal}.
 *
 * <AUTHOR> Mayur
 */
public class SiteLeadGenerationInternalTypeHandler implements TypeHandler<String> {

    @Override
    public void setParameter(PreparedStatement ps, int parameterIndex, String parameter,
            JdbcType jdbcType) throws SQLException {
        // do nothing.
    }

    @Override
    public String getResult(ResultSet rs, String columnName) throws SQLException {
        return mapValuesToNames(rs.getString(columnName));
    }

    @Override
    public String getResult(ResultSet rs, int columnIndex) throws SQLException {
        return mapValuesToNames(rs.getString(columnIndex));
    }

    @Override
    public String getResult(CallableStatement cs, int columnIndex) throws SQLException {
        return mapValuesToNames(cs.getString(columnIndex));
    }

    private String mapValuesToNames(String values) {
        if (values != null && !values.isEmpty()) {
            String joinOn = ", ";
            return Arrays.stream(values.split(COMMA)).map(value -> {
                String name = SiteLeadGenerationInternal
                        .getNameBy(Integer.parseInt(value));
                return name;
            }).collect(Collectors.joining(joinOn));
        }
        return "EMPTY";
    }
}
