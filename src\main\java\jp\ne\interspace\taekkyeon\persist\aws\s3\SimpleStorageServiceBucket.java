/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.s3;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

import javax.inject.Singleton;

import com.amazonaws.services.s3.model.AbortMultipartUploadRequest;
import com.amazonaws.services.s3.model.CompleteMultipartUploadRequest;
import com.amazonaws.services.s3.model.CopyObjectResult;
import com.amazonaws.services.s3.model.InitiateMultipartUploadRequest;
import com.amazonaws.services.s3.model.InitiateMultipartUploadResult;
import com.amazonaws.services.s3.model.PartETag;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.s3.model.UploadPartRequest;
import com.amazonaws.services.s3.model.UploadPartResult;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.util.concurrent.RateLimiter;
import com.google.inject.Inject;

import lombok.Getter;

import jp.ne.interspace.taekkyeon.module.BucketNameResolver;
import jp.ne.interspace.taekkyeon.module.Environment;

import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule.SYSTEM_PROPERTY_NAME_FOR_IS_USED_S3_TEST_SEED;
import static jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule.SYSTEM_PROPERTY_NAME_FOR_TEST_SEED;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.LogType.getCurrentLogType;
import static lombok.AccessLevel.PACKAGE;

/**
 * Data access layer representing a S3 bucket.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class SimpleStorageServiceBucket {

    private static final long PART_SIZE = 50 * 1024 * 1024;

    @Inject
    private SimpleStorageServiceClient s3Client;

    @Inject @BucketNameResolver @Getter(value = PACKAGE)
    private String partialBucketName;

    /**
     * Sets up a bucket for test uses with the bucket name suffixed with a seed string.
     */
    public void setUpForTest() {
        if (isNotTestContext()) {
            throw new UnsupportedOperationException("Creating a bucket is not supported");
        }
        s3Client.createBucket(getBucketName());
    }

    /**
     * Deletes the bucket for test uses corresponding by the given bucket name.
     */
    public void tearDownForTest() {
        if (isNotTestContext()) {
            throw new UnsupportedOperationException("Deleting a bucket is not supported");
        }
        List<S3ObjectSummary> summaries = s3Client.listObjects(getBucketName());
        for (S3ObjectSummary summary : summaries) {
            s3Client.deleteObject(getBucketName(), summary.getKey());
        }
        s3Client.deleteBucket(getBucketName());
    }

    /**
     * Returns a list of summary information about the objects in the specified bucket.
     *
     * @param prefix
     *            the optional prefix parameter restricting the response to keys that
     *            begin with the specified prefix
     * @return a list of the object summaries describing the objects stored in the S3
     *         bucket
     */
    public List<S3ObjectSummary> listObjects(String prefix) {
        return s3Client.listObjects(getBucketName(), prefix);
    }

    /**
     * Returns a list of summary information about the objects in the specified bucket.
     *
     * @return a list of the object summaries describing the objects stored in the S3
     *         bucket
     */
    public List<S3ObjectSummary> listObjects() {
        return listObjects(getCurrentLogType().getCode());
    }

    /**
     * Returns a list of summary information about the objects in the specified bucket.
     *
     * @return a list of the object summaries describing the objects stored in the S3
     *         bucket
     */
    public List<S3ObjectSummary> listAllObjects() {
        return s3Client.listObjects(getBucketName());
    }

    /**
     * Returns a set of keys in the s3 bucket with given criteria.
     *
     * @param prefix
     *          the given prefix
     * @return a set of keys in the s3 bucket with given criteria
     */
    public List<String> getAllKeys(String prefix) {
        return s3Client.fetchAllKeys(getBucketName(), prefix);
    }

    /**
     * Gets the object stored in Amazon S3 under the specified bucket and key.
     *
     * @param key
     *            the key under which the desired object is stored
     * @return the object stored in Amazon S3 in the specified bucket and key
     */
    public S3Object getObject(String key) {
        return s3Client.getObject(getBucketName(), key);
    }

    /**
     * Encodes a file into the contents of an S3 object and make it accessible.
     *
     * @param key
     *            the key of the object to create
     * @param file
     *            the file to encode
     * @return a {@link PutObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object and make it accessible
     */
    public PutObjectResult putPublicReadObject(String key, File file) {
        return s3Client.putPublicReadObject(getBucketName(), key, file);
    }

    /**
     * Encodes a String into the contents of an S3 object.
     * <p>
     * String will be encoded to bytes with UTF-8 encoding.
     * </p>
     *
     * @param key
     *            the key of the object to create
     * @param content
     *            the String to encode
     * @return a {@link PutObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object
     */
    public PutObjectResult putObject(String key, String content) {
        return s3Client.putObject(getBucketName(), key, content);
    }

    /**
     * Encodes a file into the contents of an S3 object.
     *
     * @param key
     *            the key of the object to create
     * @param file
     *            the file to encode
     * @return a {@link PutObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object
     */
    public PutObjectResult putObject(String key, File file) {
        return s3Client.putObject(getBucketName(), key, file);
    }

    /**
     * Copies a source object to a new destination in Amazon S3.
     *
     * @param sourceKey
     *            the key in the source bucket under which the source object is stored
     * @param destinationPath
     *            the path in the destination bucket
     * @param destinationKey
     *            the key in the destination bucket under which the new object will be
     *            created
     * @return a {@link CopyObjectResult} object containing the information returned by
     *         Amazon S3 for the newly created object
     */
    public CopyObjectResult copyObject(String sourceKey, String destinationPath,
            String destinationKey) {
        String destinationBucketName = Joiner.on("/").join(getBucketName(), destinationPath);
        return s3Client.copyObject(getBucketName(), sourceKey, destinationBucketName,
                destinationKey);
    }

    /**
     * Deletes the specified object in the specified bucket. Once deleted, the object can
     * only be restored if versioning was enabled when the object was deleted.
     * <p>
     * If attempting to delete an object that does not exist, Amazon S3 returns a success
     * message instead of an error message.
     * </p>
     *
     * @param key
     *            The key of the object to delete
     */
    public void deleteObject(String key) {
        s3Client.deleteObject(getBucketName(), key);
    }

    /**
     * Reattempts will be made with the interval of 2 seconds.
     */
    public void waitUntilAvailable() {
        RateLimiter interval = RateLimiter.create(2);
        interval.acquire();
    }

    /**
     * Check if the specified object exists in the given bucket.
     *
     * @param objectName
     *            the name of object that has to be checked
     * @return {@code true} if the specified object exsits
     */
    public boolean doesObjectExist(String objectName) {
        return s3Client.doesObjectExist(getBucketName(), objectName);
    }

    /**
     * Returns the bucket name.
     *
     * @return the bucket name
     */
    public String getBucketName() {
        return getPartialBucketName() + getTestSeedIfAvaliable();
    }

    /**
     * Updates bucket setting as public.
     */
    public void updateBucketAsPublic() {
        s3Client.updateBucketAsPublic(getBucketName());
    }

    /**
     * Uploads a large file to the S3 bucket.
     *
     * @param filePath
     *            the path of the file to upload
     * @param file
     *            the file to upload
     */
    public void uploadMultipart(String filePath, File file) {
        List<PartETag> partETags = new ArrayList<>();
        String uploadId;
        InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(
                getBucketName(), filePath);
        InitiateMultipartUploadResult initResponse = s3Client
                .initiateMultipartUpload(initRequest);
        uploadId = initResponse.getUploadId();
        try {
            long contentLength = file.length();
            long filePosition = 0;
            int partNumber = 1;
            while (filePosition < contentLength) {
                long partSize = Math.min(PART_SIZE, contentLength - filePosition);
                FileInputStream partStream = new FileInputStream(file);
                partStream.skip(filePosition);
                UploadPartRequest uploadRequest = new UploadPartRequest()
                        .withBucketName(getBucketName())
                        .withKey(filePath)
                        .withUploadId(uploadId)
                        .withPartNumber(partNumber)
                        .withInputStream(partStream)
                        .withPartSize(partSize);
                UploadPartResult uploadResult = s3Client.uploadPart(uploadRequest);
                partETags.add(uploadResult.getPartETag());
                System.out.println("Uploaded part " + partNumber);
                partStream.close();
                filePosition += partSize;
                partNumber++;
            }
            CompleteMultipartUploadRequest compRequest = new CompleteMultipartUploadRequest(
                    getBucketName(), filePath, uploadId, partETags);
            s3Client.completeMultipartUpload(compRequest);
            System.out.println("Upload completed successfully!");

        } catch (Exception e) {
            System.err.println("Upload failed, aborting multipart upload...");
            s3Client.abortMultipartUpload(new AbortMultipartUploadRequest(
                    getBucketName(), filePath, uploadId));
            e.printStackTrace();
        }
    }

    @VisibleForTesting
    String getTestSeedSystemProperty() {
        return getProperty(SYSTEM_PROPERTY_NAME_FOR_TEST_SEED);
    }

    @VisibleForTesting
    Environment getCurrentEnvironment() {
        return Environment.getCurrentEnvironment();
    }

    private String getTestSeedIfAvaliable() {
        String seed = getTestSeedSystemProperty();
        return getCurrentEnvironment() == DEV && !Strings.isNullOrEmpty(seed)
                && Boolean.valueOf(
                        getProperty(SYSTEM_PROPERTY_NAME_FOR_IS_USED_S3_TEST_SEED))
                                ? seed.toLowerCase().replace("_", "-") : EMPTY;
    }

    private boolean isNotTestContext() {
        return EMPTY.equals(getTestSeedIfAvaliable());
    }
}
