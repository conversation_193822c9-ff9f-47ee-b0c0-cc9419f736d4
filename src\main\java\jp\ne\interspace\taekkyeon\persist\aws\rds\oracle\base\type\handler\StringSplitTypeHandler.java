/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.COMMA;


/**
 * MyBatis {@link org.apache.ibatis.type.TypeHandler} for {@link Set} of string.
 *
 * <AUTHOR>
 */
public class StringSplitTypeHandler extends BaseTypeHandler<Set<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int index,
                Set<String> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null) {
            ps.setString(index, parameter.toString());
        }
    }

    @Override
    public Set<String> getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        String columnValueStr = rs.getString(columnName);
        if (columnValueStr != null) {
            return new HashSet<>(Arrays.asList(columnValueStr.split(COMMA)));
        }
        return null;
    }

    @Override
    public Set<String> getNullableResult(ResultSet rs, int columnIndex)
            throws SQLException {
        String columnValueStr = rs.getString(columnIndex);
        if (columnValueStr != null) {
            return new HashSet<>(Arrays.asList(columnValueStr.split(COMMA)));
        }
        return null;
    }

    @Override
    public Set<String> getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        String columnValueStr = cs.getString(columnIndex);
        if (columnValueStr != null) {
            return new HashSet<>(Arrays.asList(columnValueStr.split(COMMA)));
        }
        return null;
    }
}
