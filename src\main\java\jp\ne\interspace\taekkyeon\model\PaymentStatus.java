/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding all the different payment statuses.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum PaymentStatus implements ValueEnum {

    UNPAID(0),
    PAID(1),
    UNDER_PROCESS(2),
    CONFIRMED(3),
    PROBLEM_REPORTED(4);

    private final int value;
}
