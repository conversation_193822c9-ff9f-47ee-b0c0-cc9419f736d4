/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import static java.math.BigDecimal.ZERO;

/**
 * DTO for holding the data of a single item of a currency rate.
 *
 * <AUTHOR> <PERSON>
 */
@AllArgsConstructor @Getter @ToString
public class CurrencyRate {

    public static final CurrencyRate DEFAULT_CURRENCY_RATE = new CurrencyRate(null, ZERO);

    private final String currency;
    private final BigDecimal rate;
}
