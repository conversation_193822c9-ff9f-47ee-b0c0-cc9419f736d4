/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding campaign closure action type.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum CampaignClosureActionType {

    CREATE_CAMPAIGN,
    FINISH_VALIDATION,
    CLOSE_CAMPAIGN,
    CLOSE_MONTH;
}
