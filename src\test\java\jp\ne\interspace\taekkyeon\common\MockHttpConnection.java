/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * It is used to mock http connection.
 *
 * <AUTHOR>
 */
public class MockHttpConnection extends HttpURLConnection {

    static final String ERROR_MESSAGE = "error";
    static final IOException IO_EXCEPTION = new IOException();

    private static final InputStream ERROR_STREAM = new ByteArrayInputStream(
            ERROR_MESSAGE.getBytes());

    private final boolean throwExceptionWhenConnect;
    private final boolean hasErrorStream;
    private final int responseCode;

    /**
     * Initialize the mock connection.
     *
     * @param url to create connection.
     * @param responseCode reponse code for testing.
     * @param throwExceptionWhenConnect to test exception case.
     * @param hasErrorStream to test null error stream case.
     * @throws MalformedURLException throw in case url is invalid.
     */
    public MockHttpConnection(String url, int responseCode,
            boolean throwExceptionWhenConnect, boolean hasErrorStream)
            throws MalformedURLException {
        super(new URL(url));
        this.responseCode = responseCode;
        this.throwExceptionWhenConnect = throwExceptionWhenConnect;
        this.hasErrorStream = hasErrorStream;
    }

    public MockHttpConnection(String url, int responseCode,
            boolean throwExceptionWhenConnect) throws MalformedURLException {
        this(url, responseCode, throwExceptionWhenConnect, true);
    }

    @Override
    public void disconnect() {
    }

    @Override
    public boolean usingProxy() {
        return false;
    }

    @Override
    public void connect() throws IOException {
        if (throwExceptionWhenConnect) {
            throw IO_EXCEPTION;
        }
    }

    @Override
    public int getResponseCode() throws IOException {
        return responseCode;
    }

    @Override
    public InputStream getErrorStream() {
        return hasErrorStream ? ERROR_STREAM : null;
    }
}
