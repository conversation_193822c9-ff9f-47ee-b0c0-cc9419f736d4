/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import com.google.inject.AbstractModule;
import com.google.inject.Module;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.ENABLE_MULTIPLE_DATABASES;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.IS_RDS_DATABASE_DISABLED;
import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Country.VIETNAM;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.ORACLE;

/**
 * Taekkyeon main module for Guice injection.
 *
 * <AUTHOR>
 */
public class TaekkyeonMainModule extends AbstractModule {

    private static final String BATCH_MODULE_CLASS_NAME_VM_ARGUMENT = "batchModuleClassName";

    @Override
    protected void configure() {
        install(new TaekkyeonPropertiesModule());
        if (!IS_RDS_DATABASE_DISABLED) {
            if (ENABLE_MULTIPLE_DATABASES) {
                install(new TaekkyeonGlobalRdsMapperModule(VIETNAM));
                install(new TaekkyeonGlobalRdsMapperModule(THAILAND));
                install(new TaekkyeonGlobalRdsMapperModule(INDONESIA));
            } else {
                install(new TaekkyeonMyBatisModule(ORACLE));
            }
        }
        installCustomBatchModule();
    }

    private void installCustomBatchModule() {
        String batchModuleClassName = System
                .getProperty(BATCH_MODULE_CLASS_NAME_VM_ARGUMENT);
        if (batchModuleClassName != null) {
            try {
                Module customBatchModule = (Module) Class.forName(batchModuleClassName)
                        .newInstance();
                install(customBatchModule);
            } catch (InstantiationException | IllegalAccessException
                    | ClassNotFoundException ex) {
                throw new RuntimeException(ex);
            }
        }
    }
}
