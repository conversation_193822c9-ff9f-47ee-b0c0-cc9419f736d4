/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.CampaignClosureStatus;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler.ValueEnumTypeHandler;

/**
 * MyBatis {@link TypeHandler} for {@link CampaignClosureStatus}.
 *
 * <AUTHOR> Vuong
 */
@MappedTypes(CampaignClosureStatus.class)
public class CampaignClosureStatusTypeHandler
        extends ValueEnumTypeHandler<CampaignClosureStatus> {

    public CampaignClosureStatusTypeHandler() {
        super(CampaignClosureStatus.class, CampaignClosureStatus.VALIDATING);
    }
}
