SET DATABASE SQL SYNTAX ORA TRUE;

DROP TABLE IF EXISTS TRANSACTION_TEST_TABLE;
CREATE TABLE TRANSACTION_TEST_TABLE (
    "TEST_ID" NUMBER(10,0) NOT NULL,
    "TEST_NAME" VARCHAR2(512), 
);

DROP TABLE IF EXISTS MERCHANT_ACCOUNT;
CREATE TABLE MERCHANT_ACCOUNT (
    "ACCOUNT_NO" NUMBER(10,0) NOT NULL,
    "MERCHANT_TYPE_ID" NUMBER(1,0) NOT NULL,
    "CORPORATE_NAME" VARCHAR2(128),
    "CORPORATE_ZIP_CODE" VARCHAR2(10),
    "CORPORATE_PREFECTURE" VARCHAR2(128),
    "CORPORATE_CITY" VARCHAR2(128),
    "CORPORATE_ADDRESS" VARCHAR2(128),
    "CORPORATE_ADDRESS2" VARCHAR2(128),
    "CORPORATE_PHONE" VARCHAR2(32),
    "CORPORATE_FAX" VARCHAR2(32),
    "CORPORATE_DIRECTOR_NAME" VARCHAR2(128),
    "CORPORATE_REMARK" VARCHAR2(2000),
    "FOSTER_LASTNAME" VARCHAR2(64),
    "FOSTER_FIRSTNAME" VARCHAR2(64),
    "FOSTER_MIDDLENAME" VARCHAR2(64),
    "FOSTER_ZIP_CODE" VARCHAR2(10),
    "FOSTER_PREFECTURE" VARCHAR2(128),
    "FOSTER_CITY" VARCHAR2(128),
    "FOSTER_ADDRESS" VARCHAR2(128),
    "FOSTER_ADDRESS2" VARCHAR2(128),
    "FOSTER_SECTION_NAME" VARCHAR2(128),
    "FOSTER_POST_NAME" VARCHAR2(128),
    "FOSTER_EMAIL" VARCHAR2(64),
    "FOSTER_PHONE" VARCHAR2(32),
    "FOSTER_FAX" VARCHAR2(32),
    "FOSTER_REMARK" VARCHAR2(2000),
    "LOGIN_NAME" VARCHAR2(64),
    "LOGIN_PASSWORD" VARCHAR2(32),
    "ACCOUNT_STATE" NUMBER(1,0) NOT NULL,
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "ACCOUNTANT_LASTNAME" VARCHAR2(64),
    "ACCOUNTANT_FIRSTNAME" VARCHAR2(64),
    "ACCOUNTANT_MIDDLENAME" VARCHAR2(64),
    "ACCOUNTANT_EMAIL" VARCHAR2(64),
    "ACCOUNTANT_PHONE" VARCHAR2(32),
    "U_ID" VARCHAR2(32) DEFAULT 'v5542527tvx7w4ts6suvswssss2137xx',
    "COUNTRY_CODE" VARCHAR2(2) DEFAULT 'ID' NOT NULL
);

DROP TABLE IF EXISTS MERCHANT_CAMPAIGN;
CREATE TABLE MERCHANT_CAMPAIGN (
    "CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
    "ACCOUNT_NO" NUMBER(10,0) NOT NULL, 
    "CAMPAIGN_STATE_ID" NUMBER(2,0) NOT NULL, 
    "CAMPAIGN_NAME" VARCHAR2(512) NOT NULL,
    "IMAGE_URL" VARCHAR2(512), 
    "URL" VARCHAR2(512) NOT NULL, 
    "DESCRIPTION" VARCHAR2(4000), 
    "CATEGORY1" NUMBER(10,0) NOT NULL, 
    "CATEGORY2" NUMBER(10,0) NOT NULL, 
    "CATEGORY3" NUMBER(10,0) NOT NULL,
    "AUTO_AFF_LIMITATION_OPTION" NUMBER(1,0) NOT NULL, 
    "AUTO_AFF_LIMITATION_DIVISION" NUMBER(1,0) NOT NULL,
    "AFF_CONDITION_SPECIAL" VARCHAR2(2048), 
    "RESULT_APPROVAL_SPECIAL" VARCHAR2(2000), 
    "PR_FOR_PARTNER" VARCHAR2(4000), 
    "GET_PARAMETER_FLAG" NUMBER(1,0) NOT NULL, 
    "POINTBACK_PERMISSION" NUMBER(1,0) NOT NULL, 
    "SELF_CONVERSION_FLAG" NUMBER(1,0), 
    "CAMPAIGN_START_DATE" DATE, 
    "CAMPAIGN_END_DATE" DATE, 
    "AUTO_AFF_APPR_DURATION" NUMBER(2,0) DEFAULT 3, 
    "OEM_FLAG" NUMBER(1,0), 
    "AUTO_ACTION_APPR_DURATION" NUMBER(2,0), 
    "HIDDEN_FLAG" NUMBER(1,0), 
    "START_DATE" DATE, 
    "END_DATE" DATE,
    "OVERLAP_FLG" NUMBER(1,0) DEFAULT 0, 
    "OFFER_CODE" VARCHAR2(32), 
    "DESCRIPTION_EN" VARCHAR2(4000), 
    "CAMPAIGN_TYPE" NUMBER(2,0) DEFAULT 0,
    "CURRENCY" VARCHAR2(3) DEFAULT 'USD',
    "AD_PLATFORM_ID" NUMBER(1,0) DEFAULT 0,
    "GLOBAL_FLAG" NUMBER(1, 0) DEFAULT 0 NOT NULL,
    "CREATED_BY" VARCHAR2(256), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256), 
    "UPDATED_ON" DATE, 
    "DEVICE_TYPES" VARCHAR2(128)
);

DROP TABLE IF EXISTS AFFILIATION_RANK_HISTORY;
CREATE TABLE AFFILIATION_RANK_HISTORY (
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL, 
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
    "TARGET_MONTH" DATE NOT NULL, 
    "RANK" NUMBER(2,0) NOT NULL, 
    "CREATED_BY" VARCHAR2(256), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256), 
    "UPDATED_ON" DATE
);

DROP TABLE IF EXISTS MERCHANT_CAMPAIGN_SETTING;
CREATE TABLE MERCHANT_CAMPAIGN_SETTING (
    "CAMPAIGN_NO" NUMBER(10,0), 
    "COOKIE_EXPIRATION_DATE_VIEW" NUMBER(10,0) DEFAULT 86400 NOT NULL,
    "VERIFY_CUT_FLAG" NUMBER(1,0) DEFAULT 0 NOT NULL, 
    "VERIFY_CUT_TARGET" NUMBER(1,0),
    "VERIFY_CUT_CONDITION" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256), 
    "UPDATED_ON" DATE, 
    "SELF_CONVERSION_FLAG" NUMBER(1,0),
    "CV_ONLY_ONCE_FLAG" NUMBER DEFAULT 0
);

DROP TABLE IF EXISTS COUNTRY;
CREATE TABLE COUNTRY (
    "CODE" CHAR(2),
    "NAME" VARCHAR2(256),
    "CURRENCY" VARCHAR2(3) ,
    "SUPPORT_EMAIL" VARCHAR2(64) ,
    "ACCESSTRADE_URL" VARCHAR2(64) ,
    "PHONE" VARCHAR2(32) ,
    "MINIMUM_AMOUNT" NUMBER(10,0) ,
    "ZONE_ID" VARCHAR2(40) ,
    "IS_TAX_CALCULATION_REQUIRED" NUMBER(1,0) DEFAULT 0,
    "MONTHLY_CLOSURE_REPORT_FLAG" NUMBER(1,0) DEFAULT 0,
    "CJ_API_KEY" VARCHAR2(256),
    "CJ_PUBLISHER_ID" VARCHAR2(10),
    "GLOBAL_PUBLISHERS_ENABLED" NUMBER(1,0) DEFAULT 0,
    "MINIMUM_AMOUNT_IN_USD" NUMBER DEFAULT 0,
    "IS_CONVERSION_PROCESSING" NUMBER(1,0),
    "LOCALE" VARCHAR2(8),
    "TAX_MIN_CALCULATION" NUMBER(12,2),
    "EMAIL_SENDER" VARCHAR2(128),
    "HAS_PENDING_MERCHANT_PAYMENT" NUMBER(1,0)
);

DROP TABLE IF EXISTS MONTHLY_CLOSING;
CREATE TABLE MONTHLY_CLOSING (
    "CLOSED_MONTH" CHAR(6) NOT NULL,
    "TARGET_MONTH" CHAR(6) NOT NULL,
    "TEMPORARY_CLOSING_FLAG" NUMBER(1,0) NOT NULL,
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "COUNTRY_CODE" VARCHAR2(2) NOT NULL
);

DROP TABLE IF EXISTS SYNCHRONIZATION_DATA;
CREATE TABLE SYNCHRONIZATION_DATA (
    "TABLE_NAME" VARCHAR2(64),
    "SYNC_START_TIME" DATE,
    "DATABASE_NAME" VARCHAR2(32),
    "CONVERSION_ID" NUMBER(10, 0),
    "COUNTRY_CODE" VARCHAR2(2),
    "SITE_ID" NUMBER(10, 0),
    "CLOSED_MONTH" DATE
);
