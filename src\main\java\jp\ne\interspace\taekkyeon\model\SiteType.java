/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration for holding all the different site types.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public enum SiteType implements ValueEnum {

    EMPTY(0),
    BLOG(1),
    SOCIAL_NETWORK(5),
    COUPON(7),
    CASHBACK(9),
    REWARD_POINT(2),
    FORUM(6),
    COMMUNITY(4),
    POR<PERSON>L(8),
    SEARCH_ENGINE(3),
    AFFILIATE_NETWORK(10),
    OTHER_ADVERTISING_NETWORK(11),
    INFLUENCER(12),
    MEDIA_BUYER(13),
    COMPARISON(14),
    MEDIA(15);

    private final int value;
}
