/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import javax.inject.Singleton;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.util.IOUtils;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CityResponse;
import io.github.mngsk.devicedetector.Detection;
import io.github.mngsk.devicedetector.DeviceDetector;
import io.github.mngsk.devicedetector.DeviceDetector.DeviceDetectorBuilder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.model.UserDeviceDetails;
import jp.ne.interspace.taekkyeon.model.UserLocationDetails;
import jp.ne.interspace.taekkyeon.module.MmdbBucketNameResolver;
import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceClient;

import static com.google.common.base.Strings.isNullOrEmpty;
import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.SPACE;
import static jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule.SYSTEM_PROPERTY_NAME_FOR_IS_USED_S3_TEST_SEED;
import static jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule.SYSTEM_PROPERTY_NAME_FOR_TEST_SEED;
import static jp.ne.interspace.taekkyeon.model.UserDeviceDetails.DEFAULT_USER_DEVICE_DETAILS;
import static jp.ne.interspace.taekkyeon.model.UserLocationDetails.DEFAULT_USER_LOCATION_DETAILS;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Service layer for handling click log data.
 *
 * <AUTHOR>
 */
@Singleton @Slf4j
public class ClickLogParserService {

    private final DatabaseReader databaseReader;

    private final DeviceDetector deviceDetector;

    private static final String UNKNOWN_VALUE = "unknown";

    private LoadingCache<String, UserLocationDetails> userLocationDetailsCache = CacheBuilder
            .newBuilder()
            .maximumSize(1000)
            .build(new CacheLoader<String, UserLocationDetails>() {

                @Override
                public UserLocationDetails load(String ipAddress) {
                    return getUserLocationDetailsBy(ipAddress);
                }
            });

    private LoadingCache<String, UserDeviceDetails> userDeviceDetailsCache = CacheBuilder
            .newBuilder()
            .maximumSize(1000)
            .build(new CacheLoader<String, UserDeviceDetails>() {

                @Override
                public UserDeviceDetails load(String userAgent) {
                    return getUserDeviceDetailsBy(userAgent);
                }
            });

    private LoadingCache<String, String> referralDomainCache = CacheBuilder
            .newBuilder()
            .maximumSize(1000)
            .build(new CacheLoader<String, String>() {

                @Override
                public String load(String referralUrl) {
                    return getReferralDomainBy(referralUrl);
                }
            });

    @Inject
    public ClickLogParserService(SimpleStorageServiceClient s3Client,
            @MmdbBucketNameResolver String mmdbBucketUrl) throws IOException {
        databaseReader = getDatabaseReader(s3Client, mmdbBucketUrl);
        deviceDetector = new DeviceDetectorBuilder().build();
    }

    /**
     * For unit test only.
     */
    ClickLogParserService() {
        databaseReader = null;
        deviceDetector = null;
    }

    /**
     * Returns the user location details by given ip address.
     *
     * @param ipAddress
     *            the given ip address
     * @return the user location details by given ip address
     */
    public UserLocationDetails getUserLocationDetailsFrom(String ipAddress) {
        if (isNullOrEmpty(ipAddress)) {
            return DEFAULT_USER_LOCATION_DETAILS;
        }
        return userLocationDetailsCache.getUnchecked(ipAddress);
    }

    /**
     * Returns the user device details by given user agent.
     *
     * @param userAgent
     *            the given user agent
     * @return the user device details by given user agent
     */
    public UserDeviceDetails getUserDeviceDetailsFrom(String userAgent) {
        if (isNullOrEmpty(userAgent)) {
            return DEFAULT_USER_DEVICE_DETAILS;
        }
        return userDeviceDetailsCache.getUnchecked(userAgent);
    }

    /**
     * Returns the referral domain by given referral url.
     *
     * @param referralUrl
     *            the given referral url
     * @return the referral domain by given referral url
     */
    public String getReferralDomainFrom(String referralUrl) {
        if (isNullOrEmpty(referralUrl)) {
            return UNKNOWN_VALUE;
        }
        return referralDomainCache.getUnchecked(referralUrl);
    }

    @VisibleForTesting
    UserLocationDetails getUserLocationDetailsBy(String ipAddress) {
        try {
            CityResponse cityResponse = databaseReader
                    .city(InetAddress.getByName(ipAddress));
            return new UserLocationDetails(getCountryName(cityResponse, ipAddress),
                    getCityName(ipAddress, cityResponse));
        } catch (Exception e) {
            getLogger().warn("Failed to get location details for IP: " + ipAddress, e);
            return DEFAULT_USER_LOCATION_DETAILS;
        }
    }

    @VisibleForTesting
    UserDeviceDetails getUserDeviceDetailsBy(String userAgent) {
        Detection detection = createDetection(userAgent);
        if (detection != null) {
            String deviceBrand = detection.getDevice().flatMap(item -> item.getBrand())
                    .orElse(UNKNOWN_VALUE);

            String browser = detection.getClient()
                    .map(client -> client.toString().split(SPACE)[0])
                    .orElse(UNKNOWN_VALUE);
            return new UserDeviceDetails(deviceBrand, browser);
        }
        return DEFAULT_USER_DEVICE_DETAILS;
    }

    @VisibleForTesting
    String getReferralDomainBy(String referralUrl) {
        if (!isNullOrEmptyOrNullString(referralUrl)) {
            try {
                URL uri = new URL(referralUrl);
                String host = uri.getHost();
                return Strings.isNullOrEmpty(host) ? UNKNOWN_VALUE : host;
            } catch (MalformedURLException e) {
                return UNKNOWN_VALUE;
            }
        }
        return UNKNOWN_VALUE;
    }

    @VisibleForTesting
    Detection createDetection(String userAgent) {
        if (isNullOrEmptyOrNullString(userAgent)) {
            return null;
        }
        return getDetectionBy(userAgent);
    }

    @VisibleForTesting
    String getCountryName(CityResponse cityResponse, String ipAddress) {
        try {
            return cityResponse != null
                    && cityResponse.getCountry().getName() != null
                            ? cityResponse.getCountry().getName() : UNKNOWN_VALUE;
        } catch (Exception e) {
            getLogger().warn("Failed to get country name for IP: " + ipAddress, e);
        }
        return UNKNOWN_VALUE;
    }

    @VisibleForTesting
    String getCityName(String ipAddress, CityResponse cityResponse) {
        try {
            return cityResponse != null && cityResponse.getCity().getName() != null
                    ? cityResponse.getCity().getName() : UNKNOWN_VALUE;
        } catch (Exception e) {
            getLogger().warn("Failed to get city name for IP: " + ipAddress, e);
        }
        return UNKNOWN_VALUE;
    }

    @VisibleForTesting
    boolean isNullOrEmptyOrNullString(String string) {
        return (Strings.isNullOrEmpty(string)
                || EMPTY.equalsIgnoreCase(string));
    }

    @VisibleForTesting
    String getBucketName(String mmdbBucketUrl) {
        return mmdbBucketUrl + getTestSeedIfAvaliable();
    }

    @VisibleForTesting
    String getTestSeedSystemProperty() {
        return getProperty(SYSTEM_PROPERTY_NAME_FOR_TEST_SEED);
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    private DatabaseReader getDatabaseReader(SimpleStorageServiceClient s3Client,
            String mmdbBucketUrl) throws IOException {
        List<S3ObjectSummary> s3ObjectSummarys = s3Client
                .listObjects(getBucketName(mmdbBucketUrl), "GeoLite2-City");
        S3ObjectSummary summary = s3ObjectSummarys.get(0);
        S3Object s3Object = s3Client.getObject(getBucketName(mmdbBucketUrl),
                summary.getKey());
        try (InputStream fileInputStream = s3Object.getObjectContent()) {
            return new DatabaseReader.Builder(
                    new ByteArrayInputStream(IOUtils.toByteArray(fileInputStream)))
                            .build();
        }
    }

    private Detection getDetectionBy(String userAgent) {
        return deviceDetector.detect(userAgent);
    }

    private String getTestSeedIfAvaliable() {
        String seed = getTestSeedSystemProperty();
        return getCurrentEnvironment() == DEV && !Strings.isNullOrEmpty(seed)
                && Boolean.valueOf(
                        getProperty(SYSTEM_PROPERTY_NAME_FOR_IS_USED_S3_TEST_SEED))
                                ? seed.toLowerCase().replace("_", "-") : EMPTY;
    }
}
