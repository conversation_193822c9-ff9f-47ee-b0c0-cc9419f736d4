/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.common;

/**
 * Constant for holding for permission name.
 *
 * <AUTHOR>
 */
public class PermissionConstant {

    public static final String DEVELOPER_REJECTED_TO_PENDING_OR_APPROVED = "developer:rejected_to_pending_or_approved";
    public static final String DEVELOPER_APPROVED_TO_PENDING_OR_REJECTED = "developer:approved_to_pending_or_rejected";
    public static final String DEVELOPER_PENDING_TO_APPROVED_OR_REJECTED = "action_approval:mass_action_approve_csv";
}
