/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import jp.ne.interspace.taekkyeon.model.EmailTemplateType;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.base.type.handler.ValueEnumTypeHandler;

import static jp.ne.interspace.taekkyeon.model.EmailTemplateType.PUBLISHER_ONBOARDING_WELCOME;

/**
 * MyBatis {@link TypeHandler} for {@link EmailTemplateType}.
 *
 * <AUTHOR>
 */
@MappedTypes(EmailTemplateType.class)
public class EmailTemplateTypeTypeHandler
        extends ValueEnumTypeHandler<EmailTemplateType> {

    public EmailTemplateTypeTypeHandler() {
        super(EmailTemplateType.class, PUBLISHER_ONBOARDING_WELCOME);
    }
}
