/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding postback parameter types.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum PostbackParameterType implements ValueEnum {

    FIXED(1),
    CONVERSION_PARAMETER(2),
    CONVERSION_DATA(3),
    SUB_ID_UTM(4),
    SUB_ID_TARGET(5),
    SUB_ID_OPT(6);

    private final int value;
}
