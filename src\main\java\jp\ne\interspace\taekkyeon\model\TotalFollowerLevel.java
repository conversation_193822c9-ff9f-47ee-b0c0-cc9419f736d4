/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for holding all the total follower level.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public enum TotalFollowerLevel implements ValueEnum {

    EMPTY(0),
    LEVEL1(1),
    LEVEL2(2),
    LEVEL3(3);

    private final int value;
}
