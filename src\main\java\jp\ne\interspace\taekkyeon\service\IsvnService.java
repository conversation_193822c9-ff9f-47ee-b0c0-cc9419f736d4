/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.util.TaekkyeonHttpClient;

import static com.google.common.collect.ImmutableMap.of;
import static java.nio.charset.StandardCharsets.UTF_8;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.PIPE;
import static jp.ne.interspace.taekkyeon.module.TaekkyeonIsvnIntegrationModule.BIND_KEY_ISVN_URL;


/**
 * Service layer for handling Isvn integration.
 *
 * <AUTHOR> Nguyen
 */
@Singleton @Slf4j
public class IsvnService {

    public static final String SECRET_KEY = "ee7efYSKhDaTBtI2QbHEn7t6zPHkzE6e";
    public static final String CLIENT_ID = "9JIux8Bs79NwlMA1";
    public static final String EVENT_FROM = "GLOBAL";
    public static final String EVENT_CODE = "BONUS_FIXED_INSERT";
    private static final String CLIENT_ID_FIELD = "client-id";
    private static final String CLIENT_TRACE_NO_FIELD = "client-trace-no";
    private static final String CLIENT_REQUEST_TIME_FIELD = "client-request-time";
    private static final String CLIENT_SIGNATURE_FIELD = "client-signature";
    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";
    private static final String HEX_FORMAT = "%02x";
    private static final int RETRY_INTERVAL = 300000;
    private static final int MAX_RETRIES = 3;

    @Inject @Named(BIND_KEY_ISVN_URL) @Getter @VisibleForTesting
    private String isvnUrl;

    @Inject
    private TaekkyeonHttpClient httpClient;

    /**
     * Sends the request to the Isvn by parameters.
     *
     * @param requestBody the given body of request
     */
    public void sendCallbackEvent(Object requestBody) {
        int callCount = 0;
        do {
            try {
                callCount++;
                String responseStatus =
                        httpClient.post(getIsvnUrl(), buildHeaders(), requestBody);
                if (!Strings.isNullOrEmpty(responseStatus)) {
                    return;
                }
                waitFiveMinutes();
            } catch (Exception ex) {
                getLogger().error(
                        "Error occurs while sending API to ISVN: " + requestBody, ex);
                waitFiveMinutes();
            }
        } while (callCount < MAX_RETRIES);
    }

    @VisibleForTesting
    Map<String, String> buildHeaders() {
        try {
            String traceNo = getUuid();
            String requestTime = getEpochTime();
            String signature = generateSignature(traceNo, requestTime);
            return ImmutableMap.<String, String>builder().put(CLIENT_ID_FIELD, CLIENT_ID)
                    .put(CLIENT_TRACE_NO_FIELD, traceNo)
                    .put(CLIENT_REQUEST_TIME_FIELD, requestTime)
                    .put(CLIENT_SIGNATURE_FIELD, signature).build();
        } catch (Exception ex) {
            getLogger().error("Error occurs while build header", ex);
        }
        return of();
    }

    @VisibleForTesting
    String generateSignature(String traceNo, String requestTime)
            throws NoSuchAlgorithmException, InvalidKeyException {
        String dataToSign = Joiner.on(PIPE)
                .join(CLIENT_ID, traceNo, requestTime, SECRET_KEY);
        System.out.println(dataToSign);
        Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
        mac.init(new SecretKeySpec(SECRET_KEY.getBytes(UTF_8),
                HMAC_SHA256_ALGORITHM));
        byte[] hashBytes = mac.doFinal(dataToSign.getBytes(UTF_8));
        return IntStream.range(0, hashBytes.length)
                .mapToObj(i -> String.format(HEX_FORMAT, 0xff & hashBytes[i]))
                .collect(Collectors.joining());
    }

    @VisibleForTesting
    String getEpochTime() {
        return String.valueOf(Instant.now().toEpochMilli());
    }

    @VisibleForTesting
    String getUuid() {
        return String.valueOf(UUID.randomUUID());
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    @VisibleForTesting
    void waitFiveMinutes() {
        try {
            Thread.sleep(RETRY_INTERVAL);
        } catch (InterruptedException e) {
            log.error("Failed delay " + RETRY_INTERVAL + "ms");
        }
    }
}
