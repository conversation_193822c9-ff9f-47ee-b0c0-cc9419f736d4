/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.junit;

import com.google.inject.Exposed;
import com.google.inject.Provides;

import jp.ne.interspace.taekkyeon.module.OracleResolver;

import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.ORACLE;

/**
 * Taekkyeon module for global JUnit HSQLDB data source.
 *
 * <AUTHOR> Pachpind
 */
@OracleResolver
public class TaekkyeonGlobalHsqldbOracleJunitModule extends TaekkyeonGlobalHsqldbJunitModule {

    public TaekkyeonGlobalHsqldbOracleJunitModule() {
        super(INDONESIA, ORACLE);
    }

    private static final String INTEGRATION_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH =
            "database/hsqldb/database-integration-test-schema.sql";
    private static final String INTEGRATION_TEST_HSQLDB_DATA_SQL_FILE_PATH =
            "database/hsqldb/database-integration-test-data.sql";
    private static final String UNIT_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH =
            "database/hsqldb/database-schema.sql";
    private static final String UNIT_TEST_HSQLDB_DATA_SQL_FILE_PATH =
            "database/hsqldb/database-test-data.sql";

    @Provides @Exposed @OracleResolver
    private HsqldbSqlFilePath provideHsqldbSqlFilePath() {
        return new HsqldbSqlFilePath(UNIT_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH,
                UNIT_TEST_HSQLDB_DATA_SQL_FILE_PATH,
                INTEGRATION_TEST_HSQLDB_SCHEMA_SQL_FILE_PATH,
                INTEGRATION_TEST_HSQLDB_DATA_SQL_FILE_PATH);
    }
}
