/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import java.util.List;

import org.easybatch.core.record.Batch;
import org.easybatch.core.record.GenericRecord;
import org.easybatch.core.record.Record;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionsWithClickIdDetails;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionUpdateRequestMapper;
import jp.ne.interspace.taekkyeon.service.ConversionUpdateRequestService;

import static java.util.Collections.singletonList;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link MerchantApiConversionLoaderRecordWriter}.
 *
 * <AUTHOR> Shin
 */
@RunWith(MockitoJUnitRunner.class)
public class MerchantApiConversionLoaderRecordWriterTest {

    private static final String IMPORT_TYPE = "CONVERSION_IMPORT";
    private static final long STAFF_ID = 4;
    private static final String STAFF_EMAIL = "<EMAIL>";
    private static final long CAMPAIGN_ID = 1L;

    @InjectMocks @Spy
    private MerchantApiConversionLoaderRecordWriter underTest;

    @Mock
    private ConversionUpdateRequestService conversionUpdateRequestService;

    @Mock
    private ConversionUpdateRequestMapper conversionUpdateRequestMapper;

    @Test
    public void testWriteRecordsShouldNotCallObjectsWhenBatchIsEmpty() throws Exception {
        // when
        underTest.writeRecords(new Batch());

        // then
        verifyZeroInteractions(conversionUpdateRequestService);
        verifyZeroInteractions(conversionUpdateRequestMapper);
    }

    @Test
    public void testWriteRecordsShouldCallCorrectMethodsWhenBatchIsNotEmpty()
            throws Exception {
        // given
        ConversionsWithClickIdDetails payload = mock(ConversionsWithClickIdDetails.class);
        List<ConversionRegistrationDetails> conversions = singletonList(
                mock(ConversionRegistrationDetails.class));
        Record<ConversionsWithClickIdDetails> record = new GenericRecord<>(null, payload);
        Batch batch = new Batch(record);
        String serializeRequest = "serializeRequest";
        when(payload.getCampaignId()).thenReturn(CAMPAIGN_ID);
        when(payload.getConversions()).thenReturn(conversions);
        doReturn(STAFF_ID).when(underTest).getStaffId();
        doReturn(STAFF_EMAIL).when(underTest).getStaffEmailBy(STAFF_ID);
        doReturn(serializeRequest).when(underTest).serializeRequest(payload, STAFF_ID,
                IMPORT_TYPE);
        String fileName = "fileName";
        when(conversionUpdateRequestService.upload(IMPORT_TYPE, serializeRequest))
                .thenReturn(fileName);

        // when
        underTest.writeRecords(batch);

        // then
        verify(conversionUpdateRequestMapper).insertRequest(fileName, CAMPAIGN_ID,
                STAFF_EMAIL, 1);
    }
}
