/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core;

import java.time.LocalDateTime;
import java.time.YearMonth;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.GlobalConversionStatusSynchronizationData;
import jp.ne.interspace.taekkyeon.model.SynchronizationData;

import static jp.ne.interspace.taekkyeon.module.DatabaseType.ORACLE;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.REDSHIFT;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

/**
 * Integration test for {@link SynchronizationDataMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class SynchronizationDataMapperTest {

    private static final String COUNTRY_CODE_SG = "SG";
    private static final String COUNTRY_CODE_TH = "TH";

    @Inject
    private SynchronizationDataMapper underTest;

    @Test
    public void testFindSynchronizationDataShouldReturnCorrectSynchronizationDataWhenSynchronizationDataIsFoundAndCountryCodeIsNull() {
        // when
        SynchronizationData actual = underTest.findSynchronizationData(
                "creative_access_log_summaries", ORACLE.toString(), null);

        // then
        assertNotNull(actual);
        assertEquals(LocalDateTime.of(2024, 07, 21, 1, 2, 3), actual.getSyncStartTime());
        assertEquals(101L, actual.getConversionId().longValue());
        assertEquals(1001L, actual.getSiteId().longValue());
    }

    @Test
    public void testFindSynchronizationDataShouldReturnCorrectSynchronizationDataWhenSynchronizationDataIsFoundAndCountryCodeIsNotNull() {
        // when
        SynchronizationData actual = underTest.findSynchronizationData(
                "click_anomaly_detection", ORACLE.toString(), "VN");

        // then
        assertNotNull(actual);
        assertEquals(LocalDateTime.of(2024, 07, 21, 2, 3, 5), actual.getSyncStartTime());
        assertEquals(103L, actual.getConversionId().longValue());
        assertEquals(1003L, actual.getSiteId().longValue());
    }

    @Test
    public void testFindSynchronizationDataShouldReturnNullWhenSynchronizationDataIsNotFound() {
        // when
        SynchronizationData actual = underTest.findSynchronizationData(
                "tmp_click_anomaly_detection", ORACLE.toString(), null);

        // then
        assertNull(actual);
    }

    @Test
    public void testFindGlobalConversionStatusSynchronizationDataShouldReturnCorrectDataWhenDataIsFound() {
        // given
        String tableName = "table_1";

        // when
        GlobalConversionStatusSynchronizationData actual =
                underTest.findGlobalConversionStatusSynchronizationData(tableName,
                        COUNTRY_CODE_TH);

        // then
        assertNotNull(actual);
        assertEquals(LocalDateTime.of(2024, 7, 29, 22, 33, 44),
                actual.getSyncStartTime());
        assertEquals(45678, actual.getConversionId().longValue());
        assertEquals(3333, actual.getSiteId().longValue());
        assertEquals(YearMonth.of(2024, 8), actual.getClosedMonth());
    }

    @Test
    public void testFindGlobalConversionStatusSynchronizationDataShouldReturnNullWhenDataIsNotFound() {
        // given
        String tableName = "table_11";

        // when
        GlobalConversionStatusSynchronizationData actual =
                underTest.findGlobalConversionStatusSynchronizationData(tableName,
                        COUNTRY_CODE_TH);

        // then
        assertNull(actual);
    }

    @Test
    public void testUpdateSynchronizationDataShouldReturnOneWhenSynchronizationDataIsFound() {
        // given
        long conversionId = 23456;
        LocalDateTime latestUpdateTime = LocalDateTime.of(2024, 7, 31, 22, 33, 44);
        String tableName = "table_a";

        // when
        int actual = underTest.updateSynchronizationData(conversionId, latestUpdateTime,
                tableName, ORACLE.toString(), COUNTRY_CODE_SG);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testUpdateSynchronizationDataShouldReturnOneWhenSynchronizationDataIsNotFound() {
        // given
        long conversionId = 34567;
        LocalDateTime latestUpdateTime = LocalDateTime.of(2024, 7, 31, 21, 43, 52);
        String tableName = "table_aa";

        // when
        int actual = underTest.updateSynchronizationData(conversionId, latestUpdateTime,
                tableName, ORACLE.toString(), COUNTRY_CODE_SG);

        // then
        assertEquals(0, actual);
    }

    @Test
    public void testUpsertGlobalConversionStatusShouldReturnOneWhenGivenCountryCodeAndGlobalConversionStatusSynchronizationDataIsNotFound() {
        // given
        GlobalConversionStatusSynchronizationData globalSyncData =
                new GlobalConversionStatusSynchronizationData(
                        LocalDateTime.of(2014, 8, 1, 4, 5, 6), 123456, 11111,
                        YearMonth.of(2014, 8));

        // when
        int actual = underTest.upsertGlobalConversionStatus(globalSyncData,
                COUNTRY_CODE_SG);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testUpsertGlobalConversionStatusShouldReturnOneWhenGivenCountryCodeAndGlobalConversionStatusSynchronizationDataIsFound() {
        // given
        String countryCode = "ID";
        GlobalConversionStatusSynchronizationData globalSyncData =
                new GlobalConversionStatusSynchronizationData(
                        LocalDateTime.of(2024, 12, 12, 11, 22, 33), 234567, 222222,
                        YearMonth.of(2024, 11));

        // when
        int actual = underTest.upsertGlobalConversionStatus(globalSyncData, countryCode);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testDeleteShouldReturnOneWhenSynchronizationDataIsFound() {
        // when
        int actual = underTest.delete("userData", REDSHIFT.toString(), COUNTRY_CODE_SG);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testDeleteShouldReturnZeroWhenSynchronizationDataIsNotFound() {
        // when
        int actual = underTest.delete("userData", REDSHIFT.toString(), COUNTRY_CODE_TH);

        // then
        assertEquals(0, actual);
    }
}
