/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding shopee node.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @ToString
public class ShopeeNode {

    private final String checkoutId;
    private final String conversionId;
    private final BigDecimal grossCommission;
    private final long purchaseTime;
    private final BigDecimal cappedCommission;
    private final BigDecimal totalBrandCommission;
    private final BigDecimal estimatedTotalCommission;
    private final String buyerType;
    private final String utmContent;
    private final String referrer;
    private final String device;
    private final List<ShopeeOrder> orders;

}
