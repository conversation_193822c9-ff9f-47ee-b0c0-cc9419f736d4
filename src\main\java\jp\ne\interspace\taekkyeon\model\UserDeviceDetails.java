/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO holding the user's device brand and browser.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public class UserDeviceDetails {

    public static final UserDeviceDetails DEFAULT_USER_DEVICE_DETAILS = new UserDeviceDetails("unknown", "unknown");

    private final String deviceBrand;
    private final String browser;
}
