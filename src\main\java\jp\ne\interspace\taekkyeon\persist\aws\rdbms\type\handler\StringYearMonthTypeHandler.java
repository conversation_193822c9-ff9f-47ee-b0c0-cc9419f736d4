/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

/**
 * MyBatis custom {@link TypeHandler} for {@link YearMonth}.
 *
 * <AUTHOR> Mayur
 */
public class StringYearMonthTypeHandler extends BaseTypeHandler<YearMonth> {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter
            .ofPattern("yyyyMM");

    @Override
    public void setNonNullParameter(PreparedStatement ps, int parameterIndex,
            YearMonth parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(parameterIndex, String.valueOf(parameter));
    }

    @Override
    public YearMonth getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        String yearMonth = rs.getString(columnName);
        if (yearMonth == null) {
            return null;
        } else {
            return YearMonth.parse(yearMonth, FORMATTER);
        }
    }

    @Override
    public YearMonth getNullableResult(ResultSet rs, int columnIndex)
            throws SQLException {
        String yearMonth = rs.getString(columnIndex);
        if (yearMonth == null) {
            return null;
        } else {
            return YearMonth.parse(yearMonth, FORMATTER);
        }
    }

    @Override
    public YearMonth getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        String yearMonth = cs.getString(columnIndex);
        if (yearMonth == null) {
            return null;
        } else {
            return YearMonth.parse(yearMonth, FORMATTER);
        }
    }
}
